import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool (more efficient than creating a new connection for each request)
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

export async function POST(request) {
  try {
    const data = await request.json();
    console.log('Received heat cycle data:', data);

    // Validate required fields - support both goat_id (legacy) and animal_id (new)
    const animalId = data.animal_id || data.goat_id;
    if (!animalId || !data.heat_date) {
      console.log('Missing required fields:', { animal_id: animalId, heat_date: data.heat_date });
      return NextResponse.json(
        { error: 'Missing required fields: animal_id and heat_date are required' },
        { status: 400 }
      );
    }

    // Process signs data - handle all possible formats
    let processedSigns = '';

    if (typeof data.signs === 'string') {
      // If it's already a string, check if it looks like JSON
      if (data.signs.startsWith('{') && data.signs.endsWith('}')) {
        try {
          // Try to parse it as JSON
          const signsObj = JSON.parse(data.signs);
          processedSigns = Object.entries(signsObj)
            .filter(([_, value]) => value === true)
            .map(([key]) => key)
            .join(',');
        } catch (e) {
          // If it's not valid JSON, use as is (might be comma-separated already)
          processedSigns = data.signs;
        }
      } else {
        // Use as is (likely already comma-separated)
        processedSigns = data.signs;
      }
    } else if (typeof data.signs === 'object' && data.signs !== null) {
      // If it's an object, extract true values
      processedSigns = Object.entries(data.signs)
        .filter(([_, value]) => value === true)
        .map(([key]) => key)
        .join(',');
    }

    console.log('Processed signs:', processedSigns);
    console.log('Attempting to insert heat cycle with animal_id:', animalId);

    // Insert all data into the heat_cycles table
    const [result] = await pool.execute(
      `INSERT INTO heat_cycles (
        animal_id,
        heat_date,
        notes,
        intensity,
        signs,
        breeding_scheduled,
        planned_breeding_date
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        animalId,
        data.heat_date,
        data.notes || null,
        data.intensity || 'moderate',
        processedSigns || null,
        data.breeding_scheduled || 0,
        data.planned_breeding_date || null
      ]
    );

    // Get the inserted ID
    const heatCycleId = result.insertId;
    console.log('Heat cycle inserted with ID:', heatCycleId);

    return NextResponse.json({
      success: true,
      message: 'Heat cycle recorded successfully',
      id: heatCycleId
    });
  } catch (error) {
    console.error('Error recording heat cycle:', error);
    return NextResponse.json(
      { error: `Failed to record heat cycle: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function GET(request) {
  let connection;

  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');
    const animalId = searchParams.get('animal_id') || searchParams.get('goat_id'); // Support both for backward compatibility
    const animalType = searchParams.get('animal_type');
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');

    // Get a connection from the pool
    connection = await pool.getConnection();

    try {
      // Build the WHERE clause based on filters
      let whereConditions = [];
      let queryParams = [];

      if (animalId) {
        whereConditions.push('hc.animal_id = ?');
        queryParams.push(animalId);
      }

      if (animalType) {
        whereConditions.push('a.animal_type = ?');
        queryParams.push(animalType);
      }

      if (startDate) {
        whereConditions.push('hc.heat_date >= ?');
        queryParams.push(startDate);
      }

      if (endDate) {
        whereConditions.push('hc.heat_date <= ?');
        queryParams.push(endDate);
      }

      const whereClause = whereConditions.length > 0
        ? 'WHERE ' + whereConditions.join(' AND ')
        : '';

      // Fetch heat cycles with animal information
      const heatCyclesQuery = `
        SELECT
          hc.*,
          a.name AS animal_name,
          a.breed AS animal_breed,
          a.tag_number AS animal_tag,
          a.animal_type AS animal_type
        FROM heat_cycles hc
        JOIN animals a ON hc.animal_id = a.id
        ${whereClause}
        ORDER BY hc.heat_date DESC
        LIMIT ? OFFSET ?
      `;

      queryParams.push(limit, offset);
      const [heatCycles] = await connection.execute(heatCyclesQuery, queryParams);

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM heat_cycles hc
        JOIN animals a ON hc.animal_id = a.id
        ${whereClause}
      `;
      const [countResult] = await connection.execute(countQuery, queryParams.slice(0, -2)); // Remove limit and offset
      const totalCount = countResult[0].total;

      // Count females currently in heat (last 3 days)
      const [femalesInHeatResult] = await connection.execute(`
        SELECT COUNT(DISTINCT hc.animal_id) as count
        FROM heat_cycles hc
        WHERE hc.heat_date >= DATE_SUB(CURDATE(), INTERVAL 3 DAY)
        AND hc.breeding_scheduled = 0
      `);
      const femalesInHeat = femalesInHeatResult[0].count;

      // Calculate average cycle lengths per animal
      const [cycleLengths] = await connection.execute(`
        SELECT
          a.id as animal_id,
          a.name as animal_name,
          a.animal_type,
          AVG(DATEDIFF(
            hc2.heat_date,
            hc1.heat_date
          )) as avg_cycle_length
        FROM heat_cycles hc1
        JOIN heat_cycles hc2 ON hc1.animal_id = hc2.animal_id
          AND hc2.heat_date > hc1.heat_date
          AND hc2.id = (
            SELECT MIN(id)
            FROM heat_cycles
            WHERE animal_id = hc1.animal_id
            AND heat_date > hc1.heat_date
          )
        JOIN animals a ON hc1.animal_id = a.id
        GROUP BY a.id, a.name, a.animal_type
        HAVING COUNT(*) >= 2
      `);

      // Predict upcoming heat cycles based on average cycle length
      const [upcomingHeat] = await connection.execute(`
        SELECT
          a.id as animal_id,
          a.name as animal_name,
          a.animal_type,
          a.tag_number,
          MAX(hc.heat_date) as last_heat_date,
          DATE_ADD(MAX(hc.heat_date), INTERVAL COALESCE(
            (SELECT AVG(DATEDIFF(hc2.heat_date, hc1.heat_date))
             FROM heat_cycles hc1
             JOIN heat_cycles hc2 ON hc1.animal_id = hc2.animal_id
               AND hc2.heat_date > hc1.heat_date
               AND hc2.id = (
                 SELECT MIN(id)
                 FROM heat_cycles
                 WHERE animal_id = hc1.animal_id
                 AND heat_date > hc1.heat_date
               )
             WHERE hc1.animal_id = a.id
             GROUP BY hc1.animal_id
             HAVING COUNT(*) >= 2
            ), 21
          ) DAY) as predicted_next_heat
        FROM heat_cycles hc
        JOIN animals a ON hc.animal_id = a.id
        GROUP BY a.id, a.name, a.animal_type, a.tag_number
        HAVING MAX(hc.heat_date) >= DATE_SUB(CURDATE(), INTERVAL 60 DAY)
        AND DATE_ADD(MAX(hc.heat_date), INTERVAL COALESCE(
            (SELECT AVG(DATEDIFF(hc2.heat_date, hc1.heat_date))
             FROM heat_cycles hc1
             JOIN heat_cycles hc2 ON hc1.animal_id = hc2.animal_id
               AND hc2.heat_date > hc1.heat_date
               AND hc2.id = (
                 SELECT MIN(id)
                 FROM heat_cycles
                 WHERE animal_id = hc1.animal_id
                 AND heat_date > hc1.heat_date
               )
             WHERE hc1.animal_id = a.id
             GROUP BY hc1.animal_id
            ), 21
          ) DAY) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 14 DAY)
        ORDER BY predicted_next_heat ASC
      `);

      // Return the data
      return NextResponse.json({
        heatCycles: heatCycles,
        stats: {
          totalCount,
          femalesInHeat,
          cycleLengths: cycleLengths || [],
          upcomingHeat: upcomingHeat || []
        }
      });

    } catch (queryError) {
      console.error('Error executing database query:', queryError);
      return NextResponse.json(
        { error: 'Database query error: ' + queryError.message },
        { status: 500 }
      );
    } finally {
      // Release the connection
      if (connection) connection.release();
    }
  } catch (error) {
    console.error('Error in heat cycles API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch heat cycles: ' + error.message },
      { status: 500 }
    );
  }
}







