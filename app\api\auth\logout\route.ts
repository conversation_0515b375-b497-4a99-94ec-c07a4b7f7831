import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

export async function POST(request: NextRequest) {
  try {
    console.log('Logout request received')

    // Get the authorization header
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '') ||
                  request.cookies.get('token')?.value ||
                  request.cookies.get('auth-token')?.value

    let userInfo = null

    // If we have a token, decode it to get user info for logging
    if (token) {
      try {
        const decoded = jwt.verify(token, JWT_SECRET) as any
        userInfo = {
          userId: decoded.userId,
          username: decoded.username,
          role: decoded.role
        }
        console.log('Logging out user:', userInfo)
      } catch (jwtError) {
        console.log('Invalid token during logout, proceeding anyway')
      }
    }

    // Log the logout event
    console.log('User logged out successfully:', userInfo?.username || 'Unknown user')

    // Create response
    const response = NextResponse.json(
      {
        message: 'Logged out successfully',
        user: userInfo?.username || null
      },
      { status: 200 }
    )

    // Clear both possible token cookies
    response.cookies.set('token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0, // Expire immediately
      path: '/'
    })

    response.cookies.set('auth-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0, // Expire immediately
      path: '/'
    })

    return response

  } catch (error) {
    console.error('Logout error:', error)

    // Even if there's an error, we should still clear cookies and return success
    // because the client-side logout should proceed
    const response = NextResponse.json(
      {
        message: 'Logged out (with errors)',
        error: 'Server error during logout'
      },
      { status: 200 } // Still return 200 so client-side logout proceeds
    )

    // Clear both possible token cookies
    response.cookies.set('token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/'
    })

    response.cookies.set('auth-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/'
    })

    return response
  }
}
