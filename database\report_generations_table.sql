-- Create report_generations table for tracking report generation history
CREATE TABLE IF NOT EXISTS report_generations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  report_type VARCHAR(50) NOT NULL,
  date_range VARCHAR(20) NOT NULL,
  generated_by VARCHAR(100) DEFAULT 'System',
  filters JSON,
  generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_report_type (report_type),
  INDEX idx_generated_at (generated_at),
  INDEX idx_generated_by (generated_by)
);

-- Insert some sample report generation records for testing
INSERT INTO report_generations (report_type, date_range, generated_by, filters, generated_at) VALUES
('financial', '30', 'User', '{"format": "pdf"}', DATE_SUB(NOW(), INTERVAL 2 DAY)),
('health', '90', 'User', '{"format": "csv"}', DATE_SUB(NOW(), INTERVAL 5 DAY)),
('breeding', '30', 'System', '{"format": "excel"}', DATE_SUB(NOW(), INTERVAL 1 WEEK)),
('all', '365', 'User', '{"format": "pdf"}', DATE_SUB(NOW(), INTERVAL 10 DAY)),
('inventory', '30', 'User', '{"format": "print"}', DATE_SUB(NOW(), INTERVAL 3 DAY));
