const mysql = require('mysql2/promise');

async function addSampleGoats() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'goat_management'
  });

  const sampleGoats = [
    {
      tag_number: 'G001',
      name: '<PERSON>',
      breed: 'Boer',
      gender: 'Female',
      birth_date: '2022-03-15',
      status: 'Healthy',
      weight: 45.5,
      color: 'Brown and White'
    },
    {
      tag_number: 'G002',
      name: '<PERSON>',
      breed: 'Alpine',
      gender: 'Male',
      birth_date: '2021-08-20',
      status: 'Healthy',
      weight: 65.2,
      color: 'Black and White'
    },
    {
      tag_number: 'G003',
      name: '<PERSON>',
      breed: 'Nubian',
      gender: 'Female',
      birth_date: '2023-01-10',
      status: 'Pregnant',
      weight: 42.0,
      color: 'Cream'
    },
    {
      tag_number: 'G004',
      name: '<PERSON>',
      breed: '<PERSON><PERSON><PERSON>',
      gender: 'Male',
      birth_date: '2022-11-05',
      status: 'Healthy',
      weight: 55.8,
      color: 'White'
    },
    {
      tag_number: 'G005',
      name: '<PERSON>',
      breed: 'Boer',
      gender: 'Female',
      birth_date: '2023-06-12',
      status: 'Lactating',
      weight: 38.5,
      color: 'Red and White'
    },
    {
      tag_number: 'G006',
      name: 'Charlie',
      breed: 'Alpine',
      gender: 'Male',
      birth_date: '2022-09-30',
      status: 'Sick',
      weight: 48.2,
      color: 'Brown'
    },
    {
      tag_number: 'G007',
      name: 'Rosie',
      breed: 'Nubian',
      gender: 'Female',
      birth_date: '2021-12-18',
      status: 'Healthy',
      weight: 50.1,
      color: 'Tan and White'
    },
    {
      tag_number: 'G008',
      name: 'Zeus',
      breed: 'Boer',
      gender: 'Male',
      birth_date: '2020-05-25',
      status: 'Healthy',
      weight: 72.5,
      color: 'Brown and Black'
    }
  ];

  try {
    // Check if goats already exist
    const [existing] = await connection.execute('SELECT COUNT(*) as count FROM goats');
    console.log(`Current goats in database: ${existing[0].count}`);

    if (existing[0].count < 8) {
      console.log('Adding sample goats...');
      
      for (const goat of sampleGoats) {
        try {
          await connection.execute(
            `INSERT INTO goats (tag_number, name, breed, gender, birth_date, status, weight, color, acquisition_date, is_registered) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURDATE(), 0)`,
            [
              goat.tag_number,
              goat.name,
              goat.breed,
              goat.gender,
              goat.birth_date,
              goat.status,
              goat.weight,
              goat.color
            ]
          );
          console.log(`Added goat: ${goat.name} (${goat.tag_number})`);
        } catch (error) {
          if (error.code === 'ER_DUP_ENTRY') {
            console.log(`Goat ${goat.tag_number} already exists, skipping...`);
          } else {
            console.error(`Error adding goat ${goat.name}:`, error.message);
          }
        }
      }
    } else {
      console.log('Database already has enough sample data.');
    }

    // Show final count
    const [final] = await connection.execute('SELECT COUNT(*) as count FROM goats');
    console.log(`Total goats in database: ${final[0].count}`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await connection.end();
  }
}

addSampleGoats();
