import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

export async function GET(request) {
  try {
    // Create a connection
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    });

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const maxAgeMonths = parseInt(searchParams.get('max_age_months') || '6');
    const safeMaxAgeMonths = Math.max(1, Math.min(maxAgeMonths, 24));

    console.log('Fetching kids with max age:', safeMaxAgeMonths, 'months');

    // Get kids data with sire and dam names
    const [kids] = await connection.execute(`
      SELECT
        a.id,
        a.tag_number,
        a.name,
        a.breed,
        a.gender,
        a.birth_date,
        a.status,
        a.weight,
        a.animal_type,
        a.notes,
        a.sire AS sire_tag,
        a.dam AS dam_tag,
        sire.name AS sire_name,
        dam.name AS dam_name,
        TIMESTAMPDIFF(MONTH, a.birth_date, CURDATE()) AS age_months,
        TIMESTAMPDIFF(DAY, a.birth_date, CURDATE()) AS age_days
      FROM animals a
      LEFT JOIN animals sire ON a.sire = sire.tag_number
      LEFT JOIN animals dam ON a.dam = dam.tag_number
      WHERE a.birth_date IS NOT NULL
      AND a.birth_date >= DATE_SUB(CURDATE(), INTERVAL ? MONTH)
      ORDER BY a.birth_date DESC
      LIMIT 100
    `, [safeMaxAgeMonths]);

    console.log('Found', kids.length, 'kids');

    // Get statistics
    const [statsResult] = await connection.execute(`
      SELECT
        COUNT(*) as total_kids,
        SUM(CASE WHEN gender = 'Male' THEN 1 ELSE 0 END) as male_count,
        SUM(CASE WHEN gender = 'Female' THEN 1 ELSE 0 END) as female_count,
        AVG(weight) as avg_weight
      FROM animals
      WHERE birth_date IS NOT NULL
      AND birth_date >= DATE_SUB(CURDATE(), INTERVAL ? MONTH)
    `, [safeMaxAgeMonths]);

    const stat = statsResult[0];
    const stats = {
      totalKids: stat.total_kids || 0,
      maleCount: stat.male_count || 0,
      femaleCount: stat.female_count || 0,
      avgWeight: stat.avg_weight ? parseFloat(stat.avg_weight).toFixed(2) : '0.00'
    };

    await connection.end();

    return NextResponse.json({
      kids: kids,
      stats: stats,
      totalCount: stat.total_kids || 0,
      maxAgeMonths: maxAgeMonths
    });

  } catch (error) {
    console.error('Error in kids API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch kids: ' + error.message },
      { status: 500 }
    );
  }
}

