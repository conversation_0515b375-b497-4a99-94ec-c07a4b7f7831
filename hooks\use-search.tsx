"use client"

import { useState, useEffect, useCallback } from "react"

interface UseSearchProps<T> {
  items: T[]
  searchFields: (keyof T)[]
  initialSearchTerm?: string
}

export function useSearch<T>({ items, searchFields, initialSearchTerm = "" }: UseSearchProps<T>) {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm)
  const [filteredItems, setFilteredItems] = useState<T[]>(items)
  const [isSearching, setIsSearching] = useState(false)

  // Debounce search to improve performance
  const debounce = (func: Function, delay: number) => {
    let timeoutId: NodeJS.Timeout
    return (...args: any[]) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func(...args), delay)
    }
  }

  // Filter items based on search term
  const filterItems = useCallback(
    (term: string) => {
      if (!term.trim()) {
        return items
      }

      const lowerCaseTerm = term.toLowerCase()

      return items.filter((item) => {
        return searchFields.some((field) => {
          const value = item[field]

          if (value === null || value === undefined) {
            return false
          }

          // Handle different types of values
          if (typeof value === "string") {
            return value.toLowerCase().includes(lowerCaseTerm)
          } else if (typeof value === "number") {
            return value.toString().includes(lowerCaseTerm)
          } else if (Array.isArray(value)) {
            return value.some((v) => v && typeof v === "string" && v.toLowerCase().includes(lowerCaseTerm))
          }

          return false
        })
      })
    },
    [items, searchFields],
  )

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((term: string) => {
      setIsSearching(true)
      const results = filterItems(term)
      setFilteredItems(results)
      setIsSearching(false)
    }, 300),
    [filterItems],
  )

  // Update filtered items when search term changes
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredItems(items)
      return
    }

    debouncedSearch(searchTerm)
  }, [searchTerm, items, debouncedSearch])

  // Update filtered items when items change
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredItems(items)
    } else {
      debouncedSearch(searchTerm)
    }
  }, [items, searchTerm, debouncedSearch])

  return {
    searchTerm,
    setSearchTerm,
    filteredItems,
    isSearching,
  }
}

