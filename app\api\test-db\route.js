import { NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

export async function GET() {
  let connection

  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management',
    })

    // Test basic query
    const [rows] = await connection.execute('SELECT COUNT(*) as count FROM animals')
    
    // Check if report_generations table exists
    const [tables] = await connection.execute("SHOW TABLES LIKE 'report_generations'")
    
    return NextResponse.json({
      success: true,
      database_connected: true,
      animals_count: rows[0].count,
      report_generations_table_exists: tables.length > 0,
      message: 'Database connection successful'
    })

  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      message: 'Database connection failed'
    }, { status: 500 })
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}
