'use client'

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { LoadingButton } from "@/components/ui/loading-button"
import { useToast } from "@/components/ui/use-toast"
import { LucideAlertCircle, LucideAlertTriangle } from "lucide-react"

interface DeleteUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  userId: number | null;
  username: string;
}

export function DeleteUserModal({ isOpen, onClose, onSuccess, userId, username }: DeleteUserModalProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Handle delete user
  const handleDeleteUser = async () => {
    if (!userId) return
    
    setIsLoading(true)
    setError(null)
    
    try {
      // Call API to delete user
      const response = await fetch(`/api/users/${userId}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete user')
      }
      
      // Show success message
      toast({
        title: "Success",
        description: "User has been deleted successfully",
        variant: "default",
      })
      
      // Close modal and refresh user list
      onSuccess()
      onClose()
      
    } catch (err: any) {
      console.error('Error deleting user:', err)
      setError(err.message)
      
      toast({
        title: "Error",
        description: err.message,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <LucideAlertTriangle className="h-5 w-5" />
            Delete User
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. The user will be permanently deleted from the system.
          </DialogDescription>
        </DialogHeader>
        
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-red-800 mb-4">
            <div className="flex items-center gap-2">
              <LucideAlertCircle className="h-4 w-4" />
              <span className="font-medium">Error</span>
            </div>
            <p className="text-sm mt-1">{error}</p>
          </div>
        )}
        
        <div className="py-4">
          <p className="mb-4">
            Are you sure you want to delete the user <span className="font-semibold">{username}</span>?
          </p>
          
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 text-amber-800">
            <div className="flex items-center gap-2">
              <LucideAlertTriangle className="h-4 w-4" />
              <span className="font-medium">Warning</span>
            </div>
            <p className="text-sm mt-1">
              Deleting this user will remove all their access to the system. Any data associated with this user will remain in the system.
            </p>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <LoadingButton 
            variant="destructive" 
            onClick={handleDeleteUser} 
            isLoading={isLoading}
          >
            Delete User
          </LoadingButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
