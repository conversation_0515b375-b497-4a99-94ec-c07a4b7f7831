import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

export async function POST(request) {
  try {
    const data = await request.json();
    console.log('Received breeding record data:', data);
    
    // Validate required fields
    if (!data.female_id || !data.breeding_date) {
      console.log('Missing required fields:', { female_id: data.female_id, breeding_date: data.breeding_date });
      return NextResponse.json(
        { error: 'Missing required fields: female_id and breeding_date are required' },
        { status: 400 }
      );
    }
    
    // Get a connection from the pool
    const connection = await pool.getConnection();
    
    try {
      // Start transaction
      await connection.beginTransaction();
      
      // Insert data into the breeding_records table (using current database schema)
      const [result] = await connection.execute(
        `INSERT INTO breeding_records (
          female_id,
          male_id,
          breeding_method,
          breeding_date,
          expected_kidding_date,
          actual_kidding_date,
          number_of_kids,
          notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          data.female_id,
          data.male_id || null,
          data.breeding_method || 'Natural',
          data.breeding_date,
          data.expected_birth_date || data.expected_kidding_date || null, // Support both new and legacy field names
          data.actual_birth_date || data.actual_kidding_date || null,
          data.number_of_offspring || data.number_of_kids || null,
          data.notes || null
        ]
      );
      
      // Commit transaction
      await connection.commit();
      
      // Get the inserted ID
      const breedingRecordId = result.insertId;
      console.log('Breeding record inserted with ID:', breedingRecordId);
      
      return NextResponse.json({ 
        success: true, 
        message: 'Breeding record created successfully',
        id: breedingRecordId
      });
    } catch (error) {
      // Rollback in case of error
      await connection.rollback();
      throw error;
    } finally {
      // Release connection back to the pool
      connection.release();
    }
  } catch (error) {
    console.error('Error creating breeding record:', error);
    return NextResponse.json(
      { error: `Failed to create breeding record: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const [rows] = await pool.execute(`
      SELECT br.*,
             f.name as female_name,
             f.animal_type as female_type,
             f.breed as female_breed,
             f.tag_number as female_tag,
             m.name as male_name,
             m.animal_type as male_type,
             m.breed as male_breed,
             m.tag_number as male_tag
      FROM breeding_records br
      LEFT JOIN animals f ON br.female_id = f.id
      LEFT JOIN animals m ON br.male_id = m.id
      ORDER BY br.breeding_date DESC
    `);
    
    return NextResponse.json(rows);
  } catch (error) {
    console.error('Error fetching breeding records:', error);
    return NextResponse.json(
      { error: 'Failed to fetch breeding records' },
      { status: 500 }
    );
  }
}
