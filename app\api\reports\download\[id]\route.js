import { NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0
})

export async function GET(request, { params }) {
  let connection

  try {
    const reportId = params.id

    // Get a connection from the pool
    connection = await pool.getConnection()

    // Get report details
    const [reports] = await connection.execute(`
      SELECT 
        id,
        report_type,
        date_range,
        generated_by,
        filters,
        file_name,
        file_format,
        generated_at
      FROM report_generations 
      WHERE id = ?
    `, [reportId])

    if (reports.length === 0) {
      return NextResponse.json(
        { error: 'Report not found' },
        { status: 404 }
      )
    }

    const report = reports[0]

    // For now, we'll regenerate the report data since we don't store actual files
    // In a production system, you would store the actual files and serve them
    
    // Get the report data based on the report type and date range
    const reportData = await generateReportData(connection, report.report_type, report.date_range)

    // Generate the appropriate format
    let content, contentType, fileName

    switch (report.file_format) {
      case 'csv':
        content = generateCSV(reportData, report.report_type)
        contentType = 'text/csv'
        fileName = report.file_name || `${report.report_type}_report.csv`
        break
      case 'excel':
        content = generateCSV(reportData, report.report_type) // For now, use CSV format for Excel
        contentType = 'application/vnd.ms-excel'
        fileName = report.file_name || `${report.report_type}_report.xlsx`
        break
      case 'json':
        content = JSON.stringify(reportData, null, 2)
        contentType = 'application/json'
        fileName = report.file_name || `${report.report_type}_report.json`
        break
      default:
        // For PDF and print, redirect to the reports page with parameters
        const reportUrl = `/reports?type=${report.report_type}&range=${report.date_range}&action=print`
        return NextResponse.redirect(new URL(reportUrl, request.url))
    }

    // Return the file content
    return new NextResponse(content, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Cache-Control': 'no-cache',
      },
    })

  } catch (error) {
    console.error('Error downloading report:', error)
    return NextResponse.json(
      { error: 'Failed to download report: ' + error.message },
      { status: 500 }
    )
  } finally {
    if (connection) connection.release()
  }
}

// Helper function to generate report data
async function generateReportData(connection, reportType, dateRange) {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - parseInt(dateRange))

  const formattedStartDate = startDate.toISOString().split('T')[0]
  const formattedEndDate = endDate.toISOString().split('T')[0]

  let data = {}

  try {
    switch (reportType.toLowerCase()) {
      case 'financial':
        const [financial] = await connection.execute(`
          SELECT 
            transaction_date,
            transaction_type,
            category,
            amount,
            description
          FROM financial_transactions 
          WHERE transaction_date BETWEEN ? AND ?
          ORDER BY transaction_date DESC
        `, [formattedStartDate, formattedEndDate])
        data = { financial_transactions: financial }
        break

      case 'health':
        const [health] = await connection.execute(`
          SELECT 
            hr.record_date,
            hr.record_type,
            hr.diagnosis,
            hr.treatment,
            hr.notes,
            a.name as animal_name,
            a.animal_type,
            a.breed
          FROM health_records hr
          JOIN animals a ON hr.animal_id = a.id
          WHERE hr.record_date BETWEEN ? AND ?
          ORDER BY hr.record_date DESC
        `, [formattedStartDate, formattedEndDate])
        data = { health_records: health }
        break

      case 'breeding':
        const [breeding] = await connection.execute(`
          SELECT 
            br.breeding_date,
            br.expected_kidding_date,
            br.actual_kidding_date,
            br.status,
            br.number_of_kids,
            br.notes,
            doe.name as doe_name,
            buck.name as buck_name
          FROM breeding_records br
          JOIN animals doe ON br.doe_id = doe.id
          LEFT JOIN animals buck ON br.buck_id = buck.id
          WHERE br.breeding_date BETWEEN ? AND ?
          ORDER BY br.breeding_date DESC
        `, [formattedStartDate, formattedEndDate])
        data = { breeding_records: breeding }
        break

      case 'inventory':
        const [animals] = await connection.execute(`
          SELECT 
            tag_number,
            name,
            animal_type,
            breed,
            gender,
            birth_date,
            status,
            weight,
            created_at
          FROM animals 
          WHERE created_at BETWEEN ? AND ?
          ORDER BY created_at DESC
        `, [formattedStartDate, formattedEndDate])
        data = { animals: animals }
        break

      default:
        // All reports
        const [allFinancial] = await connection.execute(`
          SELECT 'financial' as type, transaction_date as date, category, amount, description
          FROM financial_transactions 
          WHERE transaction_date BETWEEN ? AND ?
        `, [formattedStartDate, formattedEndDate])
        
        const [allHealth] = await connection.execute(`
          SELECT 'health' as type, hr.record_date as date, hr.record_type, a.name as animal_name
          FROM health_records hr
          JOIN animals a ON hr.animal_id = a.id
          WHERE hr.record_date BETWEEN ? AND ?
        `, [formattedStartDate, formattedEndDate])

        data = { 
          financial: allFinancial,
          health: allHealth,
          date_range: `${formattedStartDate} to ${formattedEndDate}`
        }
    }

    return data
  } catch (error) {
    console.error('Error generating report data:', error)
    return { error: 'Failed to generate report data' }
  }
}

// Helper function to generate CSV content
function generateCSV(data, reportType) {
  let csv = ''
  
  switch (reportType.toLowerCase()) {
    case 'financial':
      csv = 'Date,Type,Category,Amount,Description\n'
      if (data.financial_transactions) {
        data.financial_transactions.forEach(row => {
          csv += `${row.transaction_date},${row.transaction_type},${row.category},${row.amount},"${row.description || ''}"\n`
        })
      }
      break

    case 'health':
      csv = 'Date,Type,Animal,Breed,Diagnosis,Treatment,Notes\n'
      if (data.health_records) {
        data.health_records.forEach(row => {
          csv += `${row.record_date},${row.record_type},${row.animal_name},${row.breed},"${row.diagnosis || ''}","${row.treatment || ''}","${row.notes || ''}"\n`
        })
      }
      break

    case 'breeding':
      csv = 'Breeding Date,Expected Kidding,Actual Kidding,Status,Kids,Doe,Buck,Notes\n'
      if (data.breeding_records) {
        data.breeding_records.forEach(row => {
          csv += `${row.breeding_date},${row.expected_kidding_date || ''},${row.actual_kidding_date || ''},${row.status},${row.number_of_kids || ''},${row.doe_name},${row.buck_name || ''},"${row.notes || ''}"\n`
        })
      }
      break

    case 'inventory':
      csv = 'Tag Number,Name,Type,Breed,Gender,Birth Date,Status,Weight,Created\n'
      if (data.animals) {
        data.animals.forEach(row => {
          csv += `${row.tag_number},${row.name},${row.animal_type},${row.breed},${row.gender},${row.birth_date || ''},${row.status},${row.weight || ''},${row.created_at}\n`
        })
      }
      break

    default:
      csv = 'Report Type,Date,Details\n'
      if (data.financial) {
        data.financial.forEach(row => {
          csv += `Financial,${row.date},${row.category} - ${row.amount}\n`
        })
      }
      if (data.health) {
        data.health.forEach(row => {
          csv += `Health,${row.date},${row.record_type} - ${row.animal_name}\n`
        })
      }
  }

  return csv
}
