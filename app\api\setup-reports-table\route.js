import { NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

export async function POST() {
  let connection

  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management',
    })

    // Create report_generations table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS report_generations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        report_type VARCHAR(50) NOT NULL,
        date_range VARCHAR(20) NOT NULL,
        generated_by VARCHAR(100) DEFAULT 'User',
        filters JSON,
        file_name VARCHAR(255),
        file_format VARCHAR(10),
        generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_report_type (report_type),
        INDEX idx_generated_at (generated_at),
        INDEX idx_generated_by (generated_by)
      )
    `)

    // Insert some sample report generation records for testing
    await connection.execute(`
      INSERT IGNORE INTO report_generations (id, report_type, date_range, generated_by, filters, file_name, file_format, generated_at) VALUES
      (1, 'financial', '30', 'User', '{"format": "pdf"}', 'Financial_Report_2024-01-15.pdf', 'pdf', DATE_SUB(NOW(), INTERVAL 2 DAY)),
      (2, 'health', '90', 'User', '{"format": "csv"}', 'Health_Report_2024-01-13.csv', 'csv', DATE_SUB(NOW(), INTERVAL 5 DAY)),
      (3, 'breeding', '30', 'System', '{"format": "excel"}', 'Breeding_Report_2024-01-08.xlsx', 'excel', DATE_SUB(NOW(), INTERVAL 1 WEEK)),
      (4, 'all', '365', 'User', '{"format": "pdf"}', 'Complete_Report_2024-01-05.pdf', 'pdf', DATE_SUB(NOW(), INTERVAL 10 DAY)),
      (5, 'inventory', '30', 'User', '{"format": "print"}', 'Inventory_Report_2024-01-12.pdf', 'print', DATE_SUB(NOW(), INTERVAL 3 DAY))
    `)

    return NextResponse.json({
      success: true,
      message: 'Report generations table created and populated successfully'
    })

  } catch (error) {
    console.error('Setup error:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      message: 'Failed to setup reports table'
    }, { status: 500 })
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}
