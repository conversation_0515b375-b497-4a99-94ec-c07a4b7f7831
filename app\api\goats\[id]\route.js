import { NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

// GET - Fetch individual goat
export async function GET(request, { params }) {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    })

    const goatId = params.id

    const [rows] = await connection.execute(
      `SELECT 
        id, tag_number, name, breed, gender, birth_date, acquisition_date,
        status, weight, color, markings, sire, dam, purchase_price, notes,
        is_registered, registration_number, animal_type, created_at, updated_at
      FROM animals 
      WHERE id = ? AND animal_type = 'Goat'`,
      [goatId]
    )

    await connection.end()

    if (rows.length === 0) {
      return NextResponse.json(
        { error: 'Goat not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(rows[0])

  } catch (error) {
    console.error('Error fetching goat:', error)
    return NextResponse.json(
      { error: 'Failed to fetch goat: ' + error.message },
      { status: 500 }
    )
  }
}

// PUT - Update individual goat
export async function PUT(request, { params }) {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    })

    const goatId = params.id
    const body = await request.json()

    // Prepare update data
    const updateData = {
      tag_number: body.tag_number,
      name: body.name,
      breed: body.breed,
      gender: body.gender,
      birth_date: body.birth_date || null,
      acquisition_date: body.acquisition_date || null,
      status: body.status,
      weight: body.weight ? parseFloat(body.weight) : null,
      color: body.color || null,
      markings: body.markings || null,
      sire: body.sire || null,
      dam: body.dam || null,
      purchase_price: body.purchase_price ? parseFloat(body.purchase_price) : null,
      notes: body.notes || null,
      is_registered: body.is_registered || false,
      registration_number: body.registration_number || null,
      updated_at: new Date()
    }

    // Build dynamic update query
    const updateFields = []
    const updateValues = []
    
    Object.entries(updateData).forEach(([key, value]) => {
      updateFields.push(`${key} = ?`)
      updateValues.push(value)
    })
    
    updateValues.push(goatId) // Add ID for WHERE clause

    const updateQuery = `
      UPDATE animals 
      SET ${updateFields.join(', ')}
      WHERE id = ? AND animal_type = 'Goat'
    `

    const [result] = await connection.execute(updateQuery, updateValues)

    if (result.affectedRows === 0) {
      await connection.end()
      return NextResponse.json(
        { error: 'Goat not found or no changes made' },
        { status: 404 }
      )
    }

    // Fetch updated goat data
    const [updatedRows] = await connection.execute(
      `SELECT 
        id, tag_number, name, breed, gender, birth_date, acquisition_date,
        status, weight, color, markings, sire, dam, purchase_price, notes,
        is_registered, registration_number, animal_type, created_at, updated_at
      FROM animals 
      WHERE id = ? AND animal_type = 'Goat'`,
      [goatId]
    )

    await connection.end()

    return NextResponse.json(updatedRows[0])

  } catch (error) {
    console.error('Error updating goat:', error)
    return NextResponse.json(
      { error: 'Failed to update goat: ' + error.message },
      { status: 500 }
    )
  }
}

// DELETE - Delete individual goat
export async function DELETE(request, { params }) {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    })

    const goatId = params.id

    const [result] = await connection.execute(
      'DELETE FROM animals WHERE id = ? AND animal_type = "Goat"',
      [goatId]
    )

    await connection.end()

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'Goat not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Goat deleted successfully' 
    })

  } catch (error) {
    console.error('Error deleting goat:', error)
    return NextResponse.json(
      { error: 'Failed to delete goat: ' + error.message },
      { status: 500 }
    )
  }
}
