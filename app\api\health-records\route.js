import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

export async function POST(request) {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.animal_id || !data.record_date || !data.record_type || !data.diagnosis) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create a connection for this request
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    });

    // Convert is_critical boolean to tinyint (0 or 1)
    const is_critical = data.is_critical ? 1 : 0;

    // Map custom record types to valid ENUM values
    let recordType = data.record_type;
    let additionalInfo = '';

    // Check if the record type is one of our custom types
    if (['deworming', 'hoof_trimming'].includes(recordType.toLowerCase())) {
      // Store the original record type in the notes field
      additionalInfo = `Type: ${recordType}\n`;
      // Map to "Other" which is a valid ENUM value
      recordType = 'Other';
    }

    // Append additional info to notes if needed
    const notes = additionalInfo
      ? (data.notes ? additionalInfo + data.notes : additionalInfo)
      : (data.notes || null);

    // Insert record into database
    const [result] = await connection.execute(
      `INSERT INTO health_records (
        animal_id, record_date, record_type, diagnosis,
        treatment, medication, dosage, administered_by,
        vet_name, follow_up_date, outcome, notes, is_critical
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        data.animal_id,
        data.record_date,
        recordType, // Use the mapped record type
        data.diagnosis,
        data.treatment || null,
        data.medication || null,
        data.dosage || null,
        data.administered_by || null,
        data.vet_name || null,
        data.follow_up_date || null,
        data.outcome,
        notes, // Use the enhanced notes
        is_critical
      ]
    );

    await connection.end();

    return NextResponse.json({
      success: true,
      message: 'Health record added successfully',
      id: result.insertId
    });
  } catch (error) {
    console.error('Error adding health record:', error);

    // Provide more specific error messages for common database errors
    let errorMessage = 'Failed to add health record';

    if (error.code === 'WARN_DATA_TRUNCATED') {
      // Handle data truncation errors (like ENUM value mismatches)
      if (error.sqlMessage && error.sqlMessage.includes('record_type')) {
        errorMessage = 'Invalid record type. Please select a valid option.';
      } else {
        errorMessage = 'Some data was invalid or too long for the database. Please check your inputs.';
      }
    } else if (error.code === 'ER_NO_REFERENCED_ROW') {
      // Handle foreign key constraint errors
      errorMessage = 'The selected animal does not exist in the database.';
    }

    return NextResponse.json(
      { error: errorMessage, details: error.message },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    });

    const [rows] = await connection.execute(`
      SELECT hr.*, a.name as animal_name, a.animal_type, a.tag_number, a.breed
      FROM health_records hr
      JOIN animals a ON hr.animal_id = a.id
      ORDER BY hr.record_date DESC
    `);

    await connection.end();

    return NextResponse.json(rows);
  } catch (error) {
    console.error('Error fetching health records:', error);
    return NextResponse.json(
      { error: 'Failed to fetch health records' },
      { status: 500 }
    );
  }
}