"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { LucideArrowLeft } from "lucide-react"

interface BackButtonProps {
  href: string
  label?: string
  variant?: "default" | "outline" | "ghost"
  className?: string
}

export function BackButton({
  href,
  label = "Back",
  variant = "outline",
  className = "",
}: BackButtonProps) {
  return (
    <Link href={href}>
      <Button variant={variant} className={`btn-outline-accent ${className}`}>
        <LucideArrowLeft className="mr-2 h-4 w-4" />
        {label}
      </Button>
    </Link>
  )
}
