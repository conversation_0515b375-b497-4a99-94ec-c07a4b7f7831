"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { LoadingButton } from "@/components/ui/loading-button"
import {
  LucideCalendarClock,
  LucideSave,
  LucidePlus,
  LucideInfo,
  LucideAlarmClock,
} from "lucide-react"
import { BackButton } from "@/components/ui/back-button"


// Sample goat groups
const goatGroups = [
  { id: "GG-001", name: "All Goats", count: 10 },
  { id: "GG-002", name: "Pregnant Does", count: 2 },
  { id: "GG-003", name: "Lactating Does", count: 3 },
  { id: "GG-004", name: "Kids", count: 4 },
  { id: "GG-005", name: "Bucks", count: 1 },
]

// Sample feed items
const feedItems = [
  { id: "FEED-001", name: "Alfalfa Hay", category: "Hay", unit: "kg" },
  { id: "FEED-002", name: "Grain Mix", category: "Grain", unit: "kg" },
  { id: "FEED-003", name: "Mineral Blocks", category: "Supplements", unit: "blocks" },
  { id: "FEED-004", name: "Alfalfa Pellets", category: "Pellets", unit: "kg" },
  { id: "FEED-005", name: "Grass Hay", category: "Hay", unit: "kg" },
  { id: "FEED-006", name: "Protein Supplement", category: "Supplements", unit: "kg" },
]

// Days of the week
const daysOfWeek = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]

export default function AddFeedingSchedulePage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    time: "07:00",
    days: [],
    goatGroups: [],
    feedItems: [{ feedId: "", amount: "", unit: "" }],
    notes: "",
    active: true,
  })

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))
  }

  const handleDayToggle = (day) => {
    setFormData((prev) => {
      if (prev.days.includes(day)) {
        return {
          ...prev,
          days: prev.days.filter((d) => d !== day),
        }
      } else {
        return {
          ...prev,
          days: [...prev.days, day],
        }
      }
    })
  }

  const handleSelectAllDays = () => {
    setFormData((prev) => ({
      ...prev,
      days: [...daysOfWeek],
    }))
  }

  const handleClearAllDays = () => {
    setFormData((prev) => ({
      ...prev,
      days: [],
    }))
  }

  const handleSelectWeekdays = () => {
    setFormData((prev) => ({
      ...prev,
      days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
    }))
  }

  const handleSelectWeekends = () => {
    setFormData((prev) => ({
      ...prev,
      days: ["Saturday", "Sunday"],
    }))
  }

  const handleGoatGroupChange = (groupName) => {
    setFormData((prev) => {
      if (prev.goatGroups.includes(groupName)) {
        return {
          ...prev,
          goatGroups: prev.goatGroups.filter((g) => g !== groupName),
        }
      } else {
        return {
          ...prev,
          goatGroups: [...prev.goatGroups, groupName],
        }
      }
    })
  }

  const handleFeedItemChange = (index, field, value) => {
    setFormData((prev) => {
      const updatedFeedItems = [...prev.feedItems]
      updatedFeedItems[index] = {
        ...updatedFeedItems[index],
        [field]: value,
      }

      // If feed item is selected, set the unit
      if (field === "feedId") {
        const feedItem = feedItems.find((f) => f.id === value)
        if (feedItem) {
          updatedFeedItems[index].unit = feedItem.unit
        }
      }

      return {
        ...prev,
        feedItems: updatedFeedItems,
      }
    })
  }

  const addFeedItem = () => {
    setFormData((prev) => ({
      ...prev,
      feedItems: [...prev.feedItems, { feedId: "", amount: "", unit: "" }],
    }))
  }

  const removeFeedItem = (index) => {
    setFormData((prev) => {
      const updatedFeedItems = [...prev.feedItems]
      updatedFeedItems.splice(index, 1)
      return {
        ...prev,
        feedItems: updatedFeedItems,
      }
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false)
      router.push("/feeding/schedules")
    }, 1500)
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-3xl font-bold tracking-tight text-gradient-secondary">Create Feeding Schedule</h1>
        <BackButton href="/feeding/schedules" label="Back to Feeding Schedules" variant="outline" className="btn-outline-secondary" />
      </div>

        {/* Form */}
        <Card className="border-t-4 border-t-amber-500">
          <CardHeader>
            <div className="flex items-center gap-2">
              <LucideCalendarClock className="h-6 w-6 text-amber-500" />
              <CardTitle>Feeding Schedule</CardTitle>
            </div>
            <CardDescription>Create a recurring feeding schedule for your goats</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">
                      Schedule Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="Enter schedule name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="input-primary"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="time">
                      Feeding Time <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="time"
                        name="time"
                        type="time"
                        value={formData.time}
                        onChange={handleChange}
                        required
                        className="input-primary pl-10"
                      />
                      <LucideAlarmClock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-amber-500" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Schedule Days */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-amber-700">Schedule Days</h3>
                  <div className="flex gap-2">
                    <Button type="button" variant="outline" size="sm" onClick={handleSelectAllDays} className="text-xs">
                      All Days
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleSelectWeekdays}
                      className="text-xs"
                    >
                      Weekdays
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleSelectWeekends}
                      className="text-xs"
                    >
                      Weekends
                    </Button>
                    <Button type="button" variant="outline" size="sm" onClick={handleClearAllDays} className="text-xs">
                      Clear
                    </Button>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  {daysOfWeek.map((day) => (
                    <Button
                      key={day}
                      type="button"
                      variant={formData.days.includes(day) ? "default" : "outline"}
                      className={
                        formData.days.includes(day)
                          ? "bg-amber-500 hover:bg-amber-600 text-white"
                          : "border-amber-500 text-amber-600 hover:bg-amber-50"
                      }
                      onClick={() => handleDayToggle(day)}
                    >
                      {day}
                    </Button>
                  ))}
                </div>
                {formData.days.length === 0 && <p className="text-sm text-red-500">Please select at least one day</p>}
              </div>

              {/* Goat Groups */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">Goat Groups</h3>
                <div className="space-y-2">
                  <Label>
                    Select Goat Groups <span className="text-red-500">*</span>
                  </Label>
                  <div className="border rounded-md p-4 bg-amber-50/30">
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                      {goatGroups.map((group) => (
                        <div key={group.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={group.id}
                            checked={formData.goatGroups.includes(group.name)}
                            onCheckedChange={() => handleGoatGroupChange(group.name)}
                          />
                          <Label htmlFor={group.id} className="cursor-pointer">
                            {group.name} ({group.count} goats)
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  {formData.goatGroups.length === 0 && (
                    <p className="text-sm text-red-500">Please select at least one goat group</p>
                  )}
                </div>
              </div>

              {/* Feed Items */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">Feed Items</h3>
                <div className="space-y-4">
                  {formData.feedItems.map((item, index) => (
                    <div key={index} className="flex flex-col space-y-2 p-4 border rounded-md">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">Feed Item {index + 1}</h4>
                        {formData.feedItems.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => removeFeedItem(index)}
                          >
                            &times;
                          </Button>
                        )}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`feed-${index}`}>
                            Feed Type <span className="text-red-500">*</span>
                          </Label>
                          <Select
                            value={item.feedId}
                            onValueChange={(value) => handleFeedItemChange(index, "feedId", value)}
                            required
                          >
                            <SelectTrigger id={`feed-${index}`} className="input-primary">
                              <SelectValue placeholder="Select feed" />
                            </SelectTrigger>
                            <SelectContent>
                              {feedItems.map((feed) => (
                                <SelectItem key={feed.id} value={feed.id}>
                                  {feed.name} ({feed.category})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`amount-${index}`}>
                            Amount <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id={`amount-${index}`}
                            value={item.amount}
                            onChange={(e) => handleFeedItemChange(index, "amount", e.target.value)}
                            required
                            className="input-primary"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`unit-${index}`}>Unit</Label>
                          <div className="h-10 px-3 py-2 rounded-md border bg-muted">{item.unit}</div>
                        </div>
                      </div>
                    </div>
                  ))}

                  <Button
                    type="button"
                    variant="outline"
                    className="w-full border-amber-500 text-amber-600 hover:bg-amber-50"
                    onClick={addFeedItem}
                  >
                    <LucidePlus className="mr-2 h-4 w-4" />
                    Add Another Feed Item
                  </Button>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">Additional Information</h3>
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    placeholder="Enter any additional notes or instructions for this feeding schedule"
                    value={formData.notes}
                    onChange={handleChange}
                    className="input-primary min-h-[100px]"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="active"
                    checked={formData.active}
                    onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, active: checked === true }))}
                  />
                  <Label htmlFor="active" className="cursor-pointer">
                    Schedule is active
                  </Label>
                </div>
              </div>

              {/* Schedule Summary */}
              {formData.name &&
                formData.days.length > 0 &&
                formData.goatGroups.length > 0 &&
                formData.feedItems[0].feedId && (
                  <div className="bg-amber-50 p-4 rounded-md border border-amber-200">
                    <div className="flex gap-3">
                      <LucideInfo className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
                      <div className="text-sm text-amber-800">
                        <p className="font-medium">Schedule Summary:</p>
                        <p>
                          <span className="font-medium">{formData.name}</span> - Time: {formData.time} - Days:{" "}
                          {formData.days.length === 7 ? "Every day" : formData.days.join(", ")} - Groups:{" "}
                          {formData.goatGroups.join(", ")}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" onClick={() => router.push("/feeding/schedules")}>
                Cancel
              </Button>
              <LoadingButton
                type="submit"
                isLoading={isSubmitting}
                loadingText="Saving..."
                disabled={formData.days.length === 0 || formData.goatGroups.length === 0}
                className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
              >
                <LucideSave className="mr-2 h-4 w-4" />
                Save Feeding Schedule
              </LoadingButton>
            </CardFooter>
          </form>
        </Card>
      </div>
  )
}

