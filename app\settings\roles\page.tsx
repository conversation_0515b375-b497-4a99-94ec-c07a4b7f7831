'use client'

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  LucideArrowLeft,
  LucideCheck,
  LucideX,
  LucideShield,
  LucideUserCog,
  LucideLoader2
} from "lucide-react"
import { EditRoleModal } from "../edit-role-modal"
import { useToast } from "@/components/ui/use-toast"
import { RouteGuard } from "@/components/PermissionGuard"

interface Permission {
  name: string;
  admin: boolean;
  manager: boolean;
  staff: boolean;
}

interface RoleConfig {
  name: string;
  description: string;
  color: string;
  bgColor: string;
  permissions: string[];
}

export default function RoleManagementPage() {
  const { toast } = useToast()
  const [isEditRoleModalOpen, setIsEditRoleModalOpen] = useState(false)
  const [selectedRoleType, setSelectedRoleType] = useState<'Admin' | 'Manager' | 'Staff'>('Admin')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Define permissions for each role based on our RBAC system
  const [permissions] = useState<Permission[]>([
    { name: "Manage Users", admin: true, manager: false, staff: false },
    { name: "Manage Goats", admin: true, manager: true, staff: false },
    { name: "View Goats", admin: true, manager: true, staff: true },
    { name: "Manage Health Records", admin: true, manager: true, staff: false },
    { name: "Add Health Records", admin: true, manager: true, staff: true },
    { name: "Manage Breeding", admin: true, manager: true, staff: false },
    { name: "Manage Finances", admin: true, manager: true, staff: false },
    { name: "System Settings", admin: true, manager: false, staff: false },
  ])

  // Role configurations
  const roles: RoleConfig[] = [
    {
      name: "Admin",
      description: "Full access to all features",
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      permissions: permissions.filter(p => p.admin).map(p => p.name)
    },
    {
      name: "Manager",
      description: "Access to most features except user management",
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      permissions: permissions.filter(p => p.manager).map(p => p.name)
    },
    {
      name: "Staff",
      description: "Limited access to daily operations",
      color: "text-green-600",
      bgColor: "bg-green-100",
      permissions: permissions.filter(p => p.staff).map(p => p.name)
    }
  ]

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 500)

    return () => clearTimeout(timer)
  }, [])

  // Handle edit role
  const handleEditRole = (roleType: 'Admin' | 'Manager' | 'Staff') => {
    console.log('Editing role:', roleType)
    setSelectedRoleType(roleType)
    setIsEditRoleModalOpen(true)
  }

  // Handle role update success
  const handleRoleUpdateSuccess = () => {
    console.log('Role permissions updated successfully')
    toast({
      title: "Success",
      description: `${selectedRoleType} role permissions have been updated successfully`,
      variant: "default",
    })
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href="/settings">
            <Button variant="ghost" size="sm" className="flex items-center gap-2">
              <LucideArrowLeft className="h-4 w-4" />
              Back to Settings
            </Button>
          </Link>
        </div>

        <div className="flex flex-col items-center justify-center py-12">
          <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
          <p className="text-muted-foreground">Loading role management...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center gap-4">
        <Link href="/settings">
          <Button variant="ghost" size="sm" className="flex items-center gap-2">
            <LucideArrowLeft className="h-4 w-4" />
            Back to Settings
          </Button>
        </Link>
      </div>

      {/* Page Title */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Role Management</h1>
        <p className="text-muted-foreground">Configure user roles and permissions.</p>
      </div>

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <p className="text-red-600">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Role Cards */}
      <div className="space-y-6">
        {roles.map((role) => (
          <Card key={role.name} className="border-2 hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              {/* Role Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <div className={`h-10 w-10 rounded-full ${role.bgColor} flex items-center justify-center`}>
                    <LucideShield className={`h-5 w-5 ${role.color}`} />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">{role.name}</h3>
                    <p className="text-sm text-muted-foreground">{role.description}</p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                  onClick={() => handleEditRole(role.name as 'Admin' | 'Manager' | 'Staff')}
                >
                  <LucideUserCog className="h-4 w-4" />
                  Edit Role
                </Button>
              </div>

              {/* Permissions Grid */}
              <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
                {permissions.map((permission, index) => {
                  const hasPermission = permission[role.name.toLowerCase() as keyof Permission] as boolean
                  return (
                    <div key={index} className="flex items-center gap-2">
                      {hasPermission ? (
                        <LucideCheck className="h-4 w-4 text-green-500 flex-shrink-0" />
                      ) : (
                        <LucideX className="h-4 w-4 text-red-500 flex-shrink-0" />
                      )}
                      <span className={`text-sm ${hasPermission ? 'text-gray-900' : 'text-gray-500'}`}>
                        {permission.name}
                      </span>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Edit Role Modal */}
      <EditRoleModal
        isOpen={isEditRoleModalOpen}
        onClose={() => setIsEditRoleModalOpen(false)}
        onSuccess={handleRoleUpdateSuccess}
        roleType={selectedRoleType}
        permissions={permissions}
      />
    </div>
  )
}
