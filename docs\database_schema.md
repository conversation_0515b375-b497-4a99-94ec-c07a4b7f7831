# Database Schema Documentation

## Users Table

The `users` table stores information about system users and their access permissions.

```sql
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(191) DEFAULT NULL,
  `full_name` varchar(255) DEFAULT NULL,
  `role` enum('Admin','Manager','Staff','Viewer') NOT NULL DEFAULT 'Viewer',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  INDEX `idx_is_active` (`is_active`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

### Columns

| Column | Type | Description |
|--------|------|-------------|
| `id` | int | Primary key, auto-incrementing identifier |
| `username` | varchar(100) | Unique username for login |
| `password_hash` | varchar(255) | Bcrypt-hashed password |
| `email` | varchar(191) | User's email address (optional, unique) |
| `full_name` | varchar(255) | User's full name (optional) |
| `role` | enum | User's role: 'Admin', 'Manager', 'Staff', or 'Viewer' |
| `created_at` | timestamp | When the user account was created |
| `last_login` | timestamp | When the user last logged in (optional) |
| `is_active` | tinyint(1) | Whether the user account is active (1) or inactive (0) |

### Roles and Permissions

1. **Admin**: Full access to all features
   - Manage Users
   - Manage Goats
   - Manage Health Records
   - Manage Breeding
   - Manage Finances
   - System Settings

2. **Manager**: Access to most features except user management
   - Manage Goats
   - Manage Health Records
   - Manage Breeding
   - Manage Finances

3. **Staff**: Limited access to daily operations
   - View Goats
   - Add Health Records

4. **Viewer**: Read-only access to most data

### Notes

- Instead of deleting users, we deactivate them by setting `is_active` to 0
- Deactivated users cannot log in but their data remains in the system
- This approach preserves data integrity and allows for reactivation when needed
