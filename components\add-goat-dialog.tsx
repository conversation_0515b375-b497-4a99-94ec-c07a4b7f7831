"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { LoadingButton } from "@/components/ui/loading-button"
import { LucideSave, LucideUsers } from "lucide-react"

export function AddGoatDialog() {
  const router = useRouter()
  const [open, setOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    breed: "",
    gender: "",
    birthDate: "",
    tagNumber: "",
    status: "Healthy",
    notes: "",
  })

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false)
      setOpen(false)
      // Optionally redirect to the goat detail page
      // router.push("/goats")
    }, 1500)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
   { /* <DialogTrigger asChild>
        <Button
          variant="outline"
          className="border-2 border-green-500 text-green-600 hover:bg-green-50 hover:text-green-700 transition-all duration-300"
        >
          <LucideUsers className="mr-2 h-4 w-4" />
          Add Goat
        </Button>
      </DialogTrigger> */}
      <DialogContent className="sm:max-w-[550px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Add New Goat</DialogTitle>
            <DialogDescription>
              Enter the details for the new goat. Required fields are marked with an asterisk (*).
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Enter goat name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="input-primary"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tagNumber">
                  Tag Number <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="tagNumber"
                  name="tagNumber"
                  placeholder="Enter tag number"
                  value={formData.tagNumber}
                  onChange={handleChange}
                  required
                  className="input-primary"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="breed">
                  Breed <span className="text-red-500">*</span>
                </Label>
                <Select value={formData.breed} onValueChange={(value) => handleSelectChange("breed", value)} required>
                  <SelectTrigger id="breed" className="input-primary">
                    <SelectValue placeholder="Select breed" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Boer">Boer</SelectItem>
                    <SelectItem value="Alpine">Alpine</SelectItem>
                    <SelectItem value="Nubian">Nubian</SelectItem>
                    <SelectItem value="Saanen">Saanen</SelectItem>
                    <SelectItem value="LaMancha">LaMancha</SelectItem>
                    <SelectItem value="Pygmy">Pygmy</SelectItem>
                    <SelectItem value="Nigerian Dwarf">Nigerian Dwarf</SelectItem>
                    <SelectItem value="Kiko">Kiko</SelectItem>
                    <SelectItem value="Mixed">Mixed</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="gender">
                  Gender <span className="text-red-500">*</span>
                </Label>
                <Select value={formData.gender} onValueChange={(value) => handleSelectChange("gender", value)} required>
                  <SelectTrigger id="gender" className="input-primary">
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Female">Female</SelectItem>
                    <SelectItem value="Male">Male</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="birthDate">Birth Date</Label>
                <Input
                  id="birthDate"
                  name="birthDate"
                  type="date"
                  value={formData.birthDate}
                  onChange={handleChange}
                  className="input-primary"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleSelectChange("status", value)}>
                  <SelectTrigger id="status" className="input-primary">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Healthy">Healthy</SelectItem>
                    <SelectItem value="Sick">Sick</SelectItem>
                    <SelectItem value="Injured">Injured</SelectItem>
                    <SelectItem value="Quarantined">Quarantined</SelectItem>
                    <SelectItem value="Deceased">Deceased</SelectItem>
                    <SelectItem value="Pregnant">Pregnant</SelectItem>
                    <SelectItem value="Lactating">Lactating</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                placeholder="Enter any additional notes"
                value={formData.notes}
                onChange={handleChange}
                className="input-primary min-h-[100px]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" type="button" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <LoadingButton
              type="submit"
              isLoading={isSubmitting}
              loadingText="Saving..."
              className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
            >
              <LucideSave className="mr-2 h-4 w-4" />
              Save Goat
            </LoadingButton>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

