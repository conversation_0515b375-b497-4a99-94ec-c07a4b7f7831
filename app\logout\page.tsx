'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { LoadingButton } from '@/components/ui/loading-button'
import { 
  LucideLogOut, 
  LucideCheck, 
  LucideLoader2,
  LucideHome,
  LucideUser
} from 'lucide-react'

export default function LogoutPage() {
  const router = useRouter()
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const [isLoggedOut, setIsLoggedOut] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [userInfo, setUserInfo] = useState<{ username: string; role: string } | null>(null)

  // Get user info before logout
  useEffect(() => {
    try {
      const userStr = localStorage.getItem('user')
      if (userStr) {
        const user = JSON.parse(userStr)
        setUserInfo({ username: user.username, role: user.role })
      }
    } catch (error) {
      console.error('Error getting user info:', error)
    }
  }, [])

  // Auto-logout on page load
  useEffect(() => {
    const performLogout = async () => {
      setIsLoggingOut(true)
      setError(null)

      try {
        // Call logout API endpoint
        const response = await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        })

        if (!response.ok) {
          throw new Error('Logout failed')
        }

        // Clear local storage
        localStorage.removeItem('user')
        localStorage.removeItem('token')
        
        // Clear any other auth-related data
        sessionStorage.clear()

        // Dispatch custom event to notify components of user change
        window.dispatchEvent(new Event('userChanged'))

        // Mark as logged out
        setIsLoggedOut(true)

        // Redirect to login after a short delay
        setTimeout(() => {
          router.push('/login')
        }, 2000)

      } catch (err: any) {
        console.error('Logout error:', err)
        setError(err.message || 'An error occurred during logout')
        
        // Even if API fails, clear local data and redirect
        localStorage.removeItem('user')
        localStorage.removeItem('token')
        sessionStorage.clear()
        window.dispatchEvent(new Event('userChanged'))
        
        setTimeout(() => {
          router.push('/login')
        }, 3000)
      } finally {
        setIsLoggingOut(false)
      }
    }

    // Small delay to show the logout page briefly
    const timer = setTimeout(performLogout, 500)
    return () => clearTimeout(timer)
  }, [router])

  const handleReturnToLogin = () => {
    router.push('/login')
  }

  const handleReturnToDashboard = () => {
    router.push('/dashboard')
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md border-red-200 bg-red-50">
          <CardContent className="p-8 text-center">
            <div className="h-16 w-16 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-4">
              <LucideLogOut className="h-8 w-8 text-red-600" />
            </div>
            <h1 className="text-2xl font-bold text-red-900 mb-2">Logout Error</h1>
            <p className="text-red-700 mb-6">{error}</p>
            <div className="space-y-3">
              <Button 
                onClick={handleReturnToLogin}
                className="w-full bg-red-600 hover:bg-red-700 text-white"
              >
                <LucideUser className="mr-2 h-4 w-4" />
                Go to Login
              </Button>
              <Button 
                variant="outline"
                onClick={handleReturnToDashboard}
                className="w-full border-red-300 text-red-700 hover:bg-red-100"
              >
                <LucideHome className="mr-2 h-4 w-4" />
                Return to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isLoggedOut) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md border-green-200 bg-green-50">
          <CardContent className="p-8 text-center">
            <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-4">
              <LucideCheck className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-2xl font-bold text-green-900 mb-2">Logged Out Successfully</h1>
            <p className="text-green-700 mb-2">You have been successfully logged out.</p>
            {userInfo && (
              <p className="text-sm text-green-600 mb-6">
                Goodbye, {userInfo.username}! ({userInfo.role})
              </p>
            )}
            <p className="text-sm text-green-600 mb-6">
              Redirecting to login page...
            </p>
            <Button 
              onClick={handleReturnToLogin}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              <LucideUser className="mr-2 h-4 w-4" />
              Go to Login Now
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardContent className="p-8 text-center">
          <div className="h-16 w-16 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-4">
            {isLoggingOut ? (
              <LucideLoader2 className="h-8 w-8 text-blue-600 animate-spin" />
            ) : (
              <LucideLogOut className="h-8 w-8 text-blue-600" />
            )}
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {isLoggingOut ? 'Logging Out...' : 'Logout'}
          </h1>
          {userInfo && (
            <p className="text-gray-600 mb-4">
              Logging out {userInfo.username} ({userInfo.role})
            </p>
          )}
          <p className="text-gray-600 mb-6">
            {isLoggingOut 
              ? 'Please wait while we securely log you out...'
              : 'Preparing to log you out...'
            }
          </p>
          {isLoggingOut && (
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '70%' }}></div>
            </div>
          )}
          <LoadingButton
            isLoading={isLoggingOut}
            disabled={isLoggingOut}
            className="w-full"
            variant="outline"
            onClick={handleReturnToLogin}
          >
            {isLoggingOut ? 'Logging Out...' : 'Cancel'}
          </LoadingButton>
        </CardContent>
      </Card>
    </div>
  )
}
