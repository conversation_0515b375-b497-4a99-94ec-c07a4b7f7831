// <PERSON>ript to run the migration to add is_active column to users table
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function runMigration() {
  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'goat_management',
    multipleStatements: true // Allow multiple SQL statements
  });

  try {
    console.log('Connected to database. Running migration...');
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '..', 'migrations', 'add_is_active_to_users.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Execute the migration
    await connection.query(migrationSQL);
    
    console.log('Migration completed successfully!');
    
    // Verify the column was added
    const [rows] = await connection.query('DESCRIBE users');
    const isActiveColumn = rows.find(row => row.Field === 'is_active');
    
    if (isActiveColumn) {
      console.log('Verification successful: is_active column added to users table');
      console.log('Column details:', isActiveColumn);
    } else {
      console.error('Verification failed: is_active column not found in users table');
    }
    
  } catch (error) {
    console.error('Error running migration:', error);
  } finally {
    // Close the connection
    await connection.end();
    console.log('Database connection closed');
  }
}

// Run the migration
runMigration();
