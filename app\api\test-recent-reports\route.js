import { NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

export async function GET() {
  let connection

  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management',
    })

    // Test basic query
    const [countResult] = await connection.execute('SELECT COUNT(*) as count FROM report_generations')
    const totalReports = countResult[0].count

    // Get the 5 most recent reports
    const [recentReports] = await connection.execute(`
      SELECT 
        id,
        report_type,
        date_range,
        generated_by,
        file_name,
        file_format,
        generated_at
      FROM report_generations 
      ORDER BY generated_at DESC 
      LIMIT 5
    `)

    return NextResponse.json({
      success: true,
      database_connected: true,
      total_reports: totalReports,
      recent_reports_count: recentReports.length,
      recent_reports: recentReports,
      message: 'Database test successful'
    })

  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      message: 'Database test failed'
    }, { status: 500 })
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}
