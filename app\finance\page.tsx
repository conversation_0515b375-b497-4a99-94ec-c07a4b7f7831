"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  LucideArrowDownRight,
  LucideArrowUpRight,
  LucideCoins,
  LucidePlus,
  LucideLoader2,
  LucideAlertCircle,
  LucideRefreshCw,
  LucideSearch,
  LucideFilter,
  LucideCalendar,
  LucideCheck,
  LucideX
} from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON>, <PERSON><PERSON>L<PERSON>, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { DatePicker } from "@/components/ui/date-picker"

export default function FinancePage() {
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  // Define TypeScript interfaces for our data
  interface Transaction {
    id: number;
    transaction_type: 'income' | 'expense';
    amount: number;
    transaction_date: string;
    formatted_date?: string;
    description: string;
    category: string;
    payment_method: string;
    reference_number?: string;
    notes?: string;
    is_recurring?: boolean;
    receipt_path?: string;
  }

  interface Summary {
    total_income: number;
    total_expenses: number;
    net_profit: number;
    income_count: number;
    expense_count: number;
    transaction_count: number;
  }

  interface Category {
    category: string;
    transaction_type: 'income' | 'expense';
    total: number;
    count: number;
  }

  interface MonthlyTrend {
    month: string;
    transaction_type: 'income' | 'expense';
    total: number;
  }

  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [summary, setSummary] = useState<Summary>({
    total_income: 0,
    total_expenses: 0,
    net_profit: 0,
    income_count: 0,
    expense_count: 0,
    transaction_count: 0
  })
  const [categories, setCategories] = useState<Category[]>([])
  const [monthlyTrends, setMonthlyTrends] = useState<MonthlyTrend[]>([])
  const [uniqueCategories, setUniqueCategories] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState("transactions")

  // Define DateRange interface
  interface DateRange {
    from: Date | null;
    to: Date | null;
  }

  // Filter states
  const [period, setPeriod] = useState("30days")
  const [category, setCategory] = useState("all")
  const [type, setType] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [dateRange, setDateRange] = useState<DateRange>({ from: null, to: null })

  // Fetch financial data
  const fetchFinancialData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Build query parameters
      const queryParams = new URLSearchParams()
      queryParams.append('period', period)
      queryParams.append('category', category)
      queryParams.append('type', type)

      if (dateRange.from && dateRange.to) {
        queryParams.append('startDate', formatDateForAPI(dateRange.from))
        queryParams.append('endDate', formatDateForAPI(dateRange.to))
      }

      // Add a cache-busting parameter to prevent caching issues
      queryParams.append('_t', Date.now().toString())

      // Fetch data from API with timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 15000) // 15 second timeout

      try {
        const response = await fetch(`/api/finance/transactions?${queryParams.toString()}`, {
          signal: controller.signal,
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        })

        clearTimeout(timeoutId)

        // Define response data type
        type ApiResponse = {
          error?: string;
          transactions?: Transaction[];
          summary?: Summary;
          categories?: Category[];
          monthlyTrends?: MonthlyTrend[];
          uniqueCategories?: string[];
        }

        // Parse the response
        let data: ApiResponse

        try {
          data = await response.json()
        } catch (parseError) {
          console.error('Failed to parse response:', parseError)
          setError('Failed to parse server response. Please try again.')
          return
        }

        // Check if the response was successful
        if (!response.ok) {
          const errorMessage = data.error || response.statusText || 'Unknown error'
          console.warn('API error response:', errorMessage)
          setError(`Failed to fetch financial data: ${errorMessage}`)
          return
        }

        // Validate the response data
        if (!data.transactions || !Array.isArray(data.transactions)) {
          console.warn('Invalid response format - missing transactions array:', data)
          setError('Invalid data format received from server')
          return
        }

        // Update state with fetched data
        setTransactions(data.transactions)
        setSummary(data.summary || {
          total_income: 0,
          total_expenses: 0,
          net_profit: 0,
          income_count: 0,
          expense_count: 0,
          transaction_count: 0
        })
        setCategories(data.categories || [])
        setMonthlyTrends(data.monthlyTrends || [])
        setUniqueCategories(data.uniqueCategories || [])
      } catch (fetchError) {
        clearTimeout(timeoutId)

        if (fetchError.name === 'AbortError') {
          setError('Request timed out. Please try again.')
        } else {
          throw fetchError // Re-throw to be caught by the outer catch
        }
      }
    } catch (err) {
      console.error('Error fetching financial data:', err)
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
      toast({
        title: "Error",
        description: "Failed to load financial data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Format date for API
  const formatDateForAPI = (date: Date | null): string | null => {
    if (!date) return null
    return date.toISOString().split('T')[0]
  }

  // Format date for display
  const formatDate = (dateString: string | Date | null): string => {
    if (!dateString) return "N/A"
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }

  // Reset filters
  const resetFilters = () => {
    setPeriod("30days")
    setCategory("all")
    setType("all")
    setSearchTerm("")
    setDateRange({ from: null, to: null })
  }

  // Filter transactions by search term
  const filteredTransactions = transactions.filter((transaction: Transaction) => {
    if (!searchTerm) return true

    const searchLower = searchTerm.toLowerCase()
    return (
      transaction.description.toLowerCase().includes(searchLower) ||
      transaction.category.toLowerCase().includes(searchLower) ||
      transaction.payment_method.toLowerCase().includes(searchLower) ||
      (transaction.notes && transaction.notes.toLowerCase().includes(searchLower)) ||
      (transaction.reference_number && transaction.reference_number.toLowerCase().includes(searchLower))
    )
  })

  // Fetch data on component mount and when filters change
  useEffect(() => {
    fetchFinancialData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [period, category, type, dateRange.from, dateRange.to])

  return (
    <div className="flex flex-col gap-6">
      {/* Header section */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center gap-3">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-amber">Financial Management</h1>
          {isLoading && <LucideLoader2 className="h-5 w-5 text-cyan-500 animate-spin" />}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={fetchFinancialData}
            disabled={isLoading}
            className="h-10 w-10 rounded-full"
            title="Refresh data"
          >
            <LucideRefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          <Link href="/finance/add">
            <Button className="bg-gradient-to-r from-cyan-500 to-sky-500 hover:from-cyan-600 hover:to-sky-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
              <LucidePlus className="mr-2 h-4 w-4" />
              Add Transaction
            </Button>
          </Link>
        </div>
      </div>

      {/* Error message */}
      {error && !isLoading && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
          <h3 className="font-medium flex items-center gap-2">
            <LucideAlertCircle className="h-5 w-5" />
            Error Loading Financial Data
          </h3>
          <p className="mt-1 text-sm">{error}</p>
          <Button
            variant="outline"
            className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
            onClick={fetchFinancialData}
          >
            Retry
          </Button>
        </div>
      )}

      {/* Filters */}
      <Card className="bg-gradient-to-r from-cyan-50 to-sky-50">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <LucideFilter className="h-4 w-4 text-cyan-500" />
              Filter Transactions
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={resetFilters}
              disabled={period === "30days" && category === "all" && type === "all" && !searchTerm && !dateRange.from && !dateRange.to}
              className="h-8 text-xs"
            >
              Reset
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-xs font-medium mb-1 block">Time Period</label>
              <Select value={period} onValueChange={setPeriod}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7days">Last 7 days</SelectItem>
                  <SelectItem value="30days">Last 30 days</SelectItem>
                  <SelectItem value="90days">Last 90 days</SelectItem>
                  <SelectItem value="year">This year</SelectItem>
                  <SelectItem value="all">All time</SelectItem>
                  <SelectItem value="custom">Custom range</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {period === "custom" && (
              <div className="md:col-span-2">
                <label className="text-xs font-medium mb-1 block">Date Range</label>
                <div className="flex items-center gap-2">
                  <DatePicker
                    selected={dateRange.from}
                    onSelect={(date) => setDateRange(prev => ({ ...prev, from: date }))}
                    placeholder="From date"
                  />
                  <span className="text-muted-foreground">to</span>
                  <DatePicker
                    selected={dateRange.to}
                    onSelect={(date) => setDateRange(prev => ({ ...prev, to: date }))}
                    placeholder="To date"
                  />
                </div>
              </div>
            )}

            <div>
              <label className="text-xs font-medium mb-1 block">Category</label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {uniqueCategories.map(cat => (
                    <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-xs font-medium mb-1 block">Transaction Type</label>
              <Select value={type} onValueChange={setType}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="income">Income</SelectItem>
                  <SelectItem value="expense">Expense</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="mt-4 relative">
            <LucideSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search transactions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-10"
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                onClick={() => setSearchTerm("")}
              >
                <LucideX className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Stat cards */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card className="stat-card-primary">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">Total Income</CardTitle>
              <LucideArrowUpRight className="h-4 w-4 text-green-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <LucideLoader2 className="h-4 w-4 animate-spin" />
                  <span>Loading...</span>
                </div>
              ) : (
                formatCurrency(summary.total_income || 0)
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {period === "7days" ? "Last 7 days" :
               period === "30days" ? "Last 30 days" :
               period === "90days" ? "Last 90 days" :
               period === "year" ? "This year" :
               period === "all" ? "All time" :
               dateRange.from && dateRange.to ? `${formatDate(dateRange.from)} - ${formatDate(dateRange.to)}` :
               "Custom period"}
            </p>
          </CardContent>
        </Card>
        <Card className="stat-card-accent">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
              <LucideArrowDownRight className="h-4 w-4 text-destructive" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <LucideLoader2 className="h-4 w-4 animate-spin" />
                  <span>Loading...</span>
                </div>
              ) : (
                formatCurrency(summary.total_expenses || 0)
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {period === "7days" ? "Last 7 days" :
               period === "30days" ? "Last 30 days" :
               period === "90days" ? "Last 90 days" :
               period === "year" ? "This year" :
               period === "all" ? "All time" :
               dateRange.from && dateRange.to ? `${formatDate(dateRange.from)} - ${formatDate(dateRange.to)}` :
               "Custom period"}
            </p>
          </CardContent>
        </Card>
        <Card className="stat-card-amber">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
              <LucideCoins className="h-4 w-4 text-amber-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${(summary.net_profit || 0) >= 0 ? "text-green-600" : "text-destructive"}`}>
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <LucideLoader2 className="h-4 w-4 animate-spin" />
                  <span>Loading...</span>
                </div>
              ) : (
                formatCurrency(summary.net_profit || 0)
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {period === "7days" ? "Last 7 days" :
               period === "30days" ? "Last 30 days" :
               period === "90days" ? "Last 90 days" :
               period === "year" ? "This year" :
               period === "all" ? "All time" :
               dateRange.from && dateRange.to ? `${formatDate(dateRange.from)} - ${formatDate(dateRange.to)}` :
               "Custom period"}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
        <TabsList className="grid w-full grid-cols-2 p-1 bg-gradient-to-r from-cyan-50 via-sky-50 to-blue-50 rounded-xl">
          <TabsTrigger
            value="transactions"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-cyan-500 data-[state=active]:to-sky-500 data-[state=active]:text-white transition-all duration-300 hover:text-cyan-700"
          >
            Transactions
          </TabsTrigger>
          <TabsTrigger
            value="analytics"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-sky-500 data-[state=active]:to-blue-500 data-[state=active]:text-white transition-all duration-300 hover:text-sky-700"
          >
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Transactions Tab Content */}
        <TabsContent value="transactions" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Financial Transactions</CardTitle>
                {!isLoading && (
                  <div className="text-sm text-muted-foreground">
                    Showing {filteredTransactions.length} of {transactions.length} transactions
                  </div>
                )}
              </div>
              <CardDescription>
                View and manage your financial transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                  <p className="text-muted-foreground">Loading transactions...</p>
                </div>
              ) : filteredTransactions.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead className="text-right">Amount</TableHead>
                        <TableHead>Payment Method</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredTransactions.map((transaction) => (
                        <TableRow key={transaction.id} className="hover:bg-muted/50">
                          <TableCell>{formatDate(transaction.transaction_date)}</TableCell>
                          <TableCell className="font-medium">{transaction.description}</TableCell>
                          <TableCell>{transaction.category}</TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={
                                transaction.transaction_type === "income"
                                  ? "bg-green-100 text-green-800 hover:bg-green-200 border-green-300"
                                  : "bg-red-100 text-red-800 hover:bg-red-200 border-red-300"
                              }
                            >
                              {transaction.transaction_type === "income" ? "Income" : "Expense"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right font-medium">
                            <span className={transaction.transaction_type === "income" ? "text-green-600" : "text-red-600"}>
                              {transaction.transaction_type === "income" ? "+" : "-"}
                              {formatCurrency(transaction.amount)}
                            </span>
                          </TableCell>
                          <TableCell>{transaction.payment_method}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-12">
                  <LucideCoins className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-1">No transactions found</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm ? "Try adjusting your search or filters" : "Add your first transaction to get started"}
                  </p>
                  {searchTerm ? (
                    <Button variant="outline" onClick={() => setSearchTerm("")}>
                      Clear Search
                    </Button>
                  ) : (
                    <Link href="/finance/add">
                      <Button className="bg-gradient-to-r from-cyan-500 to-sky-500 hover:from-cyan-600 hover:to-sky-600 text-white">
                        <LucidePlus className="mr-2 h-4 w-4" />
                        Add Transaction
                      </Button>
                    </Link>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab Content */}
        <TabsContent value="analytics" className="mt-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Category Breakdown</CardTitle>
                <CardDescription>
                  Financial transactions by category
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                    <p className="text-muted-foreground">Loading category data...</p>
                  </div>
                ) : categories.length > 0 ? (
                  <div className="space-y-4">
                    {categories
                      .filter(cat => cat.transaction_type === 'expense')
                      .slice(0, 5)
                      .map((cat) => (
                        <div key={cat.category} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div className="h-3 w-3 rounded-full bg-red-500"></div>
                              <span className="text-sm font-medium">{cat.category}</span>
                            </div>
                            <div className="text-sm font-medium text-red-600">
                              {formatCurrency(cat.total)}
                            </div>
                          </div>
                          <div className="h-2 bg-red-100 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-red-500 rounded-full"
                              style={{
                                width: `${Math.min(100, (cat.total / (summary.total_expenses || 1)) * 100)}%`,
                              }}
                            ></div>
                          </div>
                        </div>
                      ))}

                    {categories
                      .filter(cat => cat.transaction_type === 'income')
                      .slice(0, 5)
                      .map((cat) => (
                        <div key={cat.category} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div className="h-3 w-3 rounded-full bg-green-500"></div>
                              <span className="text-sm font-medium">{cat.category}</span>
                            </div>
                            <div className="text-sm font-medium text-green-600">
                              {formatCurrency(cat.total)}
                            </div>
                          </div>
                          <div className="h-2 bg-green-100 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-green-500 rounded-full"
                              style={{
                                width: `${Math.min(100, (cat.total / (summary.total_income || 1)) * 100)}%`,
                              }}
                            ></div>
                          </div>
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <LucideCoins className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-1">No category data available</h3>
                    <p className="text-muted-foreground mb-4">
                      Add transactions to see category breakdown
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Transaction Summary</CardTitle>
                <CardDescription>
                  Overview of your financial activity
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                    <p className="text-muted-foreground">Loading summary data...</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                        <div className="text-sm text-green-800 mb-1">Income Transactions</div>
                        <div className="text-2xl font-bold text-green-700">{summary.income_count || 0}</div>
                      </div>
                      <div className="bg-red-50 p-4 rounded-lg border border-red-100">
                        <div className="text-sm text-red-800 mb-1">Expense Transactions</div>
                        <div className="text-2xl font-bold text-red-700">{summary.expense_count || 0}</div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-cyan-50 to-sky-50 p-4 rounded-lg border border-sky-100">
                      <div className="text-sm text-sky-800 mb-1">Average Transaction Value</div>
                      <div className="text-2xl font-bold text-sky-700">
                        {formatCurrency(
                          summary.transaction_count > 0
                            ? (summary.total_income + summary.total_expenses) / summary.transaction_count
                            : 0
                        )}
                      </div>
                    </div>

                    <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
                      <div className="text-sm text-amber-800 mb-1">Income to Expense Ratio</div>
                      <div className="text-2xl font-bold text-amber-700">
                        {summary.total_expenses > 0
                          ? (summary.total_income / summary.total_expenses).toFixed(2)
                          : "N/A"}
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {summary.total_expenses > 0
                          ? summary.total_income > summary.total_expenses
                            ? "You're earning more than spending"
                            : "You're spending more than earning"
                          : "No expense data available"}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

