import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

// Define the valid role types
const validRoles = ['admin', 'manager', 'staff']

// GET /api/roles/[role] - Get role permissions
export async function GET(
  request: NextRequest,
  { params }: { params: { role: string } }
) {
  try {
    // Validate database connection
    if (!db || typeof db.query !== 'function') {
      console.error('Database connection not available')
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      )
    }
    
    // Ensure params is properly awaited
    const { role } = params
    console.log('Getting permissions for role:', role)
    
    // Validate role
    if (!role || !validRoles.includes(role.toLowerCase())) {
      return NextResponse.json(
        { error: 'Invalid role type' },
        { status: 400 }
      )
    }
    
    // In a real application, you would fetch this from a database
    // For now, we'll return a static set of permissions
    const permissions = [
      { name: "Manage Users", value: role === 'admin' },
      { name: "Manage Goats", value: role === 'admin' || role === 'manager' },
      { name: "View Goats", value: true }, // All roles can view goats
      { name: "Manage Health Records", value: role === 'admin' || role === 'manager' },
      { name: "Add Health Records", value: true }, // All roles can add health records
      { name: "Manage Breeding", value: role === 'admin' || role === 'manager' },
      { name: "Manage Finances", value: role === 'admin' || role === 'manager' },
      { name: "System Settings", value: role === 'admin' },
    ]
    
    return NextResponse.json({ role, permissions })
  } catch (error: any) {
    console.error(`Error fetching ${params.role} role permissions:`, error)
    return NextResponse.json(
      { error: `Failed to fetch role permissions: ${error.message || 'Unknown error'}` },
      { status: 500 }
    )
  }
}

// PUT /api/roles/[role] - Update role permissions
export async function PUT(
  request: NextRequest,
  { params }: { params: { role: string } }
) {
  try {
    // Validate database connection
    if (!db || typeof db.query !== 'function') {
      console.error('Database connection not available')
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      )
    }
    
    // Ensure params is properly awaited
    const { role } = params
    console.log('Updating permissions for role:', role)
    
    // Validate role
    if (!role || !validRoles.includes(role.toLowerCase())) {
      return NextResponse.json(
        { error: 'Invalid role type' },
        { status: 400 }
      )
    }
    
    // Parse request body
    let requestData
    try {
      requestData = await request.json()
      console.log('Request data:', requestData)
    } catch (parseError) {
      console.error('Error parsing request body:', parseError)
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      )
    }
    
    // Validate permissions data
    const { permissions } = requestData
    if (!permissions || !Array.isArray(permissions)) {
      return NextResponse.json(
        { error: 'Invalid permissions data' },
        { status: 400 }
      )
    }
    
    // In a real application, you would update this in a database
    // For now, we'll just log the changes and return success
    console.log(`Updated ${role} permissions:`, permissions)
    
    // Simulate database update delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return NextResponse.json({
      success: true,
      message: `${role} role permissions updated successfully`,
      role,
      permissions
    })
  } catch (error: any) {
    console.error(`Error updating ${params.role} role permissions:`, error)
    return NextResponse.json(
      { error: `Failed to update role permissions: ${error.message || 'Unknown error'}` },
      { status: 500 }
    )
  }
}
