const mysql = require('mysql2/promise');
require('dotenv').config();

async function inspectGoatsTable() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    });

    console.log('Connected to database');

    // Check if goats table exists
    const [tables] = await connection.execute(`
      SELECT COUNT(*) as count FROM information_schema.tables 
      WHERE table_schema = DATABASE() AND table_name = 'goats'
    `);

    if (tables[0].count > 0) {
      console.log('Goats table exists. Inspecting structure...');
      
      // Get table structure
      const [columns] = await connection.execute(`
        DESCRIBE goats
      `);

      console.log('\nGoats table structure:');
      console.log('Column Name | Type | Null | Key | Default | Extra');
      console.log('------------|------|------|-----|---------|------');
      columns.forEach(col => {
        console.log(`${col.Field} | ${col.Type} | ${col.Null} | ${col.Key} | ${col.Default} | ${col.Extra}`);
      });

      // Get sample data
      const [sampleData] = await connection.execute(`
        SELECT * FROM goats LIMIT 3
      `);

      console.log('\nSample data:');
      console.log(JSON.stringify(sampleData, null, 2));

    } else {
      console.log('Goats table does not exist');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the inspection
inspectGoatsTable();
