import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const animalType = searchParams.get('type'); // Optional filter by animal type

    // Create a connection for this request
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    });

    // Build query with optional animal type filter
    let query = `
      SELECT id, name, animal_type, tag_number, breed, gender, status
      FROM animals
      WHERE 1=1
    `;
    const queryParams = [];

    if (animalType && animalType !== 'all') {
      query += ' AND animal_type = ?';
      queryParams.push(animalType);
    }

    query += ' ORDER BY animal_type, name';

    const [rows] = await connection.execute(query, queryParams);

    await connection.end();

    return NextResponse.json(rows);
  } catch (error) {
    console.error('Error fetching animals:', error);
    return NextResponse.json(
      { error: 'Failed to fetch animals' },
      { status: 500 }
    );
  }
}
