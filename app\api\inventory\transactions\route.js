import { NextResponse } from 'next/server';
import pool from '@/lib/db'; // This is the correct import

// Add debug logging for pool
console.log('Database pool status:', pool ? 'Initialized' : 'Not initialized');

// Helper function to generate a unique transaction ID without uuid dependency
function generateTransactionId() {
  const timestamp = Date.now();
  const randomPart = Math.floor(Math.random() * 10000);
  return `IT-${timestamp}${randomPart}`;
}

// Validate data types against schema
function validateDataTypes(data, schema) {
  const issues = [];
  
  schema.forEach(column => {
    const fieldName = column.COLUMN_NAME;
    
    // Skip validation for created_at and updated_at fields
    if (fieldName === 'created_at' || fieldName === 'updated_at') {
      return;
    }
    
    const value = data[fieldName];
    
    // Skip if value is null/undefined and field allows NULL
    if ((value === null || value === undefined) && column.IS_NULLABLE === 'YES') {
      return;
    }
    
    // Check required fields
    if (column.IS_NULLABLE === 'NO' && (value === null || value === undefined)) {
      issues.push(`Field '${fieldName}' is required but missing`);
      return;
    }
    
    // Validate data types
    if (value !== null && value !== undefined) {
      if (column.COLUMN_TYPE.includes('int') && !Number.isInteger(Number(value))) {
        issues.push(`Field '${fieldName}' should be an integer, got: ${value}`);
      }
      else if (column.COLUMN_TYPE.includes('decimal') || column.COLUMN_TYPE.includes('float') || column.COLUMN_TYPE.includes('double')) {
        if (isNaN(Number(value))) {
          issues.push(`Field '${fieldName}' should be a number, got: ${value}`);
        }
      }
      else if (column.COLUMN_TYPE.includes('date') && isNaN(Date.parse(value))) {
        issues.push(`Field '${fieldName}' should be a valid date, got: ${value}`);
      }
      else if (column.COLUMN_TYPE.includes('enum')) {
        // Extract enum values
        const enumValues = column.COLUMN_TYPE.match(/'([^']*)'/g).map(v => v.replace(/'/g, ''));
        
        // Case-insensitive check for enum values
        const normalizedValue = typeof value === 'string' ? value.toLowerCase() : value;
        const normalizedEnumValues = enumValues.map(v => v.toLowerCase());
        
        if (!normalizedEnumValues.includes(normalizedValue)) {
          issues.push(`Field '${fieldName}' should be one of [${enumValues.join(', ')}], got: ${value}`);
        }
      }
    }
  });
  
  return issues;
}

// Check database schema
async function checkDatabaseSchema() {
  try {
    if (!pool) {
      console.error('Database pool is not initialized');
      return null;
    }
    
    const [columns] = await pool.execute(`
      SELECT 
        COLUMN_NAME, 
        COLUMN_TYPE, 
        IS_NULLABLE,
        COLUMN_DEFAULT
      FROM 
        INFORMATION_SCHEMA.COLUMNS 
      WHERE 
        TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'inventory_transactions'
      ORDER BY 
        ORDINAL_POSITION
    `);
    
    return columns;
  } catch (error) {
    console.error('Error fetching schema:', error);
    return null;
  }
}

export async function POST(request) {
  console.log('=== INVENTORY TRANSACTION API POST REQUEST RECEIVED ===');
  
  // Check if pool is initialized
  if (!pool) {
    console.error('Database pool is not initialized');
    return NextResponse.json(
      { error: 'Database connection not available' },
      { status: 500 }
    );
  }
  
  // Check database schema first
  const schema = await checkDatabaseSchema();
  
  try {
    const data = await request.json();
    console.log('API received data:', JSON.stringify(data, null, 2));
    
    // Normalize transaction_type to lowercase
    if (data.transaction_type) {
      data.transaction_type = data.transaction_type.toLowerCase();
      console.log('Normalized transaction_type to:', data.transaction_type);
    }
    
    // Check for field name mismatches
    const expectedFields = [
      'item', 'transaction_type', 'quantity', 'unit', 
      'transaction_date', 'notes'
    ];
    
    const missingFields = expectedFields.filter(field => 
      !Object.keys(data).includes(field) && 
      !['notes'].includes(field)
    );
    
    if (missingFields.length > 0) {
      console.error('Field name mismatches detected:', missingFields);
      return NextResponse.json(
        { error: `Field name mismatches detected: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }
    
    // Validate required fields
    if (!data.item || !data.transaction_type || !data.quantity || !data.unit || !data.transaction_date) {
      console.error('Missing required fields:', { 
        item: data.item, 
        transaction_type: data.transaction_type, 
        quantity: data.quantity, 
        unit: data.unit,
        transaction_date: data.transaction_date
      });
      return NextResponse.json(
        { error: 'Missing required fields: item, transaction_type, quantity, unit, and transaction_date are required' },
        { status: 400 }
      );
    }

    // Validate data types if schema is available
    if (schema) {
      // Generate a transaction ID if not provided
      if (!data.id) {
        data.id = generateTransactionId();
        console.log('Generated transaction ID:', data.id);
      }
      
      const dataTypeIssues = validateDataTypes({
        id: data.id,
        item: data.item,
        transaction_type: data.transaction_type.toLowerCase(),
        quantity: parseFloat(data.quantity),
        unit: data.unit,
        transaction_date: data.transaction_date,
        notes: data.notes || null
      }, schema.filter(col => col.COLUMN_NAME !== 'created_at' && col.COLUMN_NAME !== 'updated_at'));
      
      if (dataTypeIssues.length > 0) {
        console.error('Data type validation issues:', dataTypeIssues);
        return NextResponse.json(
          { error: 'Data validation failed', issues: dataTypeIssues },
          { status: 400 }
        );
      }
    }
    
    // Extract the transaction data
    const {
      id = generateTransactionId(),
      item,
      transaction_type,
      quantity,
      unit,
      transaction_date,
      notes
    } = data;

    // Use lowercase for transaction_type to match ENUM format in database
    const formattedTransactionType = transaction_type.toLowerCase();

    // Log the exact SQL parameters that will be used
    const sqlParams = [
      id,
      formattedTransactionType,
      item,
      parseFloat(quantity), // Ensure quantity is a number
      unit,
      transaction_date,
      notes || null
    ];
    
    console.log('SQL parameters:', JSON.stringify(sqlParams, null, 2));

    // Generate the SQL query for debugging
    const sqlQuery = `
      INSERT INTO inventory_transactions (
        id,
        transaction_type,
        item,
        quantity,
        unit,
        transaction_date,
        notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    console.log('SQL query:', sqlQuery);

    // Insert into inventory_transactions table
    try {
      const [result] = await pool.execute(
        sqlQuery,
        sqlParams
      );

      console.log('Database insert result:', result);

      return NextResponse.json({ 
        success: true, 
        message: 'Inventory transaction recorded successfully',
        transactionId: id
      });
    } catch (dbError) {
      console.error('Database error details:', {
        message: dbError.message,
        code: dbError.code,
        errno: dbError.errno,
        sqlMessage: dbError.sqlMessage,
        sqlState: dbError.sqlState,
        sql: dbError.sql
      });
      
      // Check for specific error types
      if (dbError.code === 'ER_BAD_FIELD_ERROR') {
        console.error('Column name mismatch detected!');
      } else if (dbError.code === 'ER_TRUNCATED_WRONG_VALUE_FOR_FIELD') {
        console.error('Data type mismatch detected!');
      } else if (dbError.code === 'ER_DATA_TOO_LONG') {
        console.error('Data too long for column!');
      }
      
      return NextResponse.json(
        { error: 'Database error: ' + dbError.message, sqlMessage: dbError.sqlMessage },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error creating inventory transaction:', error);
    return NextResponse.json(
      { error: 'Failed to record transaction: ' + error.message },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const [transactions] = await pool.execute(`
      SELECT 
        t.*
      FROM 
        inventory_transactions t
      ORDER BY 
        t.transaction_date DESC, t.created_at DESC
    `);

    return NextResponse.json(transactions);
  } catch (error) {
    console.error('Error fetching inventory transactions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch inventory transactions: ' + error.message },
      { status: 500 }
    );
  }
}








