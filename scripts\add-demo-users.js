// <PERSON>ript to add demo users for testing
const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
require('dotenv').config();

async function addDemoUsers() {
  // Create a connection to the database
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'goat_management'
  });

  try {
    console.log('Connected to database. Adding demo users...');
    
    // Demo users with their credentials
    const demoUsers = [
      {
        username: 'admin',
        password: 'admin123',
        email: '<EMAIL>',
        full_name: 'System Administrator',
        role: 'Admin'
      },
      {
        username: 'manager',
        password: 'manager123',
        email: '<EMAIL>',
        full_name: 'Farm Manager',
        role: 'Manager'
      },
      {
        username: 'staff',
        password: 'staff123',
        email: '<EMAIL>',
        full_name: 'Farm Staff',
        role: 'Staff'
      },
      {
        username: '<PERSON><PERSON><PERSON>',
        password: 'password123',
        email: '<PERSON><PERSON><PERSON>@example.com',
        full_name: '<PERSON> <PERSON>saiti',
        role: 'Admin'
      },
      {
        username: 'mosy',
        password: 'password123',
        email: '<EMAIL>',
        full_name: 'Mosy Admin',
        role: 'Admin'
      }
    ];

    for (const user of demoUsers) {
      try {
        // Check if user already exists
        const [existing] = await connection.execute(
          'SELECT id FROM users WHERE username = ?',
          [user.username]
        );

        if (existing.length > 0) {
          console.log(`User ${user.username} already exists, skipping...`);
          continue;
        }

        // Hash the password
        const saltRounds = 10;
        const password_hash = await bcrypt.hash(user.password, saltRounds);

        // Insert the user
        await connection.execute(
          `INSERT INTO users (username, password_hash, email, full_name, role, created_at, is_active) 
           VALUES (?, ?, ?, ?, ?, NOW(), 1)`,
          [user.username, password_hash, user.email, user.full_name, user.role]
        );

        console.log(`Added user: ${user.username} (${user.role})`);
      } catch (error) {
        console.error(`Error adding user ${user.username}:`, error.message);
      }
    }

    console.log('Demo users setup completed!');
    console.log('\nDemo Credentials:');
    console.log('Admin: admin / admin123');
    console.log('Manager: manager / manager123');
    console.log('Staff: staff / staff123');
    console.log('Masaiti: Masaiti / password123');
    console.log('Mosy: mosy / password123');

  } catch (error) {
    console.error('Error setting up demo users:', error);
  } finally {
    await connection.end();
  }
}

// Run the script
addDemoUsers().catch(console.error);
