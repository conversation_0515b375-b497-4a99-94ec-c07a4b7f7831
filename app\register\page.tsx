"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import DashboardLayout from "@/components/dashboard-layout"
import { addGoat } from "@/app/actions/goats"
import { addSheep } from "@/app/actions/sheep"
import { addCattle } from "@/app/actions/cattle"
import { addPig } from "@/app/actions/pigs"
import { useFormStatus } from "react-dom"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Plus, LucideUsers } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Animal type icons mapping (using LucideUsers for all animals)
const animalIcons = {
  Goat: LucideUsers,
  Sheep: LucideUsers,
  Cattle: LucideUsers,
  Pig: LucideUsers
}

// Breed options for each animal type
const breedOptions = {
  Goat: [
    "East African (Local)", "Boer", "Alpine", "Nubian", "Saanen", 
    "LaMancha", "Pygmy", "Kiko", "Other"
  ],
  Sheep: [
    "Merino", "Suffolk", "Dorper", "Romney", "Corriedale", 
    "Leicester Longwool", "Border Leicester", "Cheviot", "Jacob", 
    "Katahdin", "Barbados Black Belly", "East African (Local)", "Other"
  ],
  Cattle: [
    "Holstein", "Jersey", "Angus", "Hereford", "Charolais", 
    "Simmental", "Limousin", "Brahman", "Shorthorn", "Guernsey", 
    "Brown Swiss", "Ayrshire", "Zebu", "East African (Local)", "Other"
  ],
  Pig: [
    "Yorkshire", "Landrace", "Duroc", "Hampshire", "Berkshire", 
    "Chester White", "Poland China", "Spotted", "Tamworth", 
    "Large Black", "Mangalitsa", "Gloucestershire Old Spots", 
    "East African (Local)", "Other"
  ]
}

// Action functions mapping
const actionFunctions = {
  Goat: addGoat,
  Sheep: addSheep,
  Cattle: addCattle,
  Pig: addPig
}

// Redirect paths after successful submission
const redirectPaths = {
  Goat: "/goats",
  Sheep: "/sheep", 
  Cattle: "/cattle",
  Pig: "/pigs"
}

function SubmitButton({ animalType }: { animalType: string }) {
  const { pending } = useFormStatus()

  return (
    <Button type="submit" disabled={pending} className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white shadow-md">
      {pending ? "Adding..." : `Add ${animalType}`}
    </Button>
  )
}

export default function RegisterPage() {
  const router = useRouter()
  const [selectedAnimalType, setSelectedAnimalType] = useState<string>("")
  const [isRegistered, setIsRegistered] = useState(false)
  const [status, setStatus] = useState("Healthy")
  const [birthDate, setBirthDate] = useState<string>("")
  const [sires, setSires] = useState<any[]>([])
  const [dams, setDams] = useState<any[]>([])
  const [loadingParents, setLoadingParents] = useState(false)

  // Fetch parent animals from database
  const fetchParentAnimals = async (animalType: string) => {
    if (!animalType) return

    setLoadingParents(true)
    try {
      // Fetch sires (males)
      const siresResponse = await fetch(`/api/animals/parents?animal_type=${animalType}&gender=Male`)
      const siresData = await siresResponse.json()

      // Fetch dams (females)
      const damsResponse = await fetch(`/api/animals/parents?animal_type=${animalType}&gender=Female`)
      const damsData = await damsResponse.json()

      if (siresData.success) {
        setSires(siresData.animals)
      }

      if (damsData.success) {
        setDams(damsData.animals)
      }
    } catch (error) {
      console.error('Error fetching parent animals:', error)
      toast({
        title: "Warning",
        description: "Could not load parent animals. You can still register the animal.",
        variant: "destructive",
      })
    } finally {
      setLoadingParents(false)
    }
  }

  const handleAnimalTypeChange = (value: string) => {
    setSelectedAnimalType(value)
    setIsRegistered(false) // Reset registration state when animal type changes
    setStatus("Healthy") // Reset status when animal type changes
    setBirthDate("") // Reset birth date when animal type changes
    setSires([]) // Clear previous sires
    setDams([]) // Clear previous dams

    // Fetch parent animals for the selected type
    fetchParentAnimals(value)
  }

  const handleBirthDateChange = (value: string) => {
    setBirthDate(value)
  }

  const handleFormSubmit = async (formData: FormData) => {
    if (!selectedAnimalType) {
      toast({
        title: "Error",
        description: "Please select an animal type first.",
        variant: "destructive",
      })
      return
    }

    try {
      const actionFunction = actionFunctions[selectedAnimalType as keyof typeof actionFunctions]
      const result = await actionFunction(formData)

      if (result.success) {
        toast({
          title: "Success",
          description: result.message,
        })
        const redirectPath = redirectPaths[selectedAnimalType as keyof typeof redirectPaths]
        router.push(redirectPath)
      } else {
        toast({
          title: "Error",
          description: result.message,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to add ${selectedAnimalType.toLowerCase()}. Please try again.`,
        variant: "destructive",
      })
    }
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gradient-primary">Register New Animal</h1>
            <p className="text-muted-foreground mt-1">Select animal type and enter the details to register a new animal</p>
          </div>
        </div>

        {/* Animal Type Selection */}
        <Card className="mb-6 border-t-4 border-t-blue-500 shadow-lg">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Plus className="h-5 w-5 text-blue-600" />
              Select Animal Type
            </CardTitle>
            <CardDescription>Choose the type of animal you want to register</CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.keys(breedOptions).map((animalType) => {
                const IconComponent = animalIcons[animalType as keyof typeof animalIcons]
                const isSelected = selectedAnimalType === animalType
                
                return (
                  <button
                    key={animalType}
                    type="button"
                    onClick={() => handleAnimalTypeChange(animalType)}
                    className={`p-4 rounded-lg border-2 transition-all duration-200 flex flex-col items-center gap-2 ${
                      isSelected 
                        ? 'border-emerald-500 bg-emerald-50 text-emerald-700' 
                        : 'border-gray-200 hover:border-emerald-300 hover:bg-emerald-25'
                    }`}
                  >
                    <IconComponent className={`h-8 w-8 ${isSelected ? 'text-emerald-600' : 'text-gray-500'}`} />
                    <span className="font-medium">{animalType}</span>
                  </button>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Animal Registration Form */}
        {selectedAnimalType && (
          <Card className="border-t-4 border-t-emerald-500 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardHeader className="bg-gradient-to-r from-emerald-50 to-teal-50">
              <CardTitle className="flex items-center gap-2 text-emerald-700">
                <Plus className="h-5 w-5 text-emerald-600" />
                {selectedAnimalType} Information
              </CardTitle>
              <CardDescription>Fill in the details below to register your new {selectedAnimalType.toLowerCase()}</CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <form action={handleFormSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Tag Number*</label>
                    <Input
                      name="tagNumber"
                      placeholder="Enter tag number"
                      required
                      className="focus:ring-2 focus:ring-emerald-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Name*</label>
                    <Input
                      name="name"
                      placeholder={`Enter ${selectedAnimalType.toLowerCase()} name`}
                      required
                      className="focus:ring-2 focus:ring-emerald-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Breed*</label>
                    <Select name="breed" required>
                      <SelectTrigger className="focus:ring-2 focus:ring-emerald-500">
                        <SelectValue placeholder="Select breed" />
                      </SelectTrigger>
                      <SelectContent>
                        {breedOptions[selectedAnimalType as keyof typeof breedOptions].map((breed) => (
                          <SelectItem key={breed} value={breed}>{breed}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Gender*</label>
                    <Select name="gender" required>
                      <SelectTrigger className="focus:ring-2 focus:ring-emerald-500">
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Female">Female</SelectItem>
                        <SelectItem value="Male">Male</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Birth Date</label>
                    <Input
                      type="date"
                      name="birthDate"
                      value={birthDate}
                      onChange={(e) => handleBirthDateChange(e.target.value)}
                      className="focus:ring-2 focus:ring-emerald-500"
                    />
                    {birthDate && (
                      <p className="text-xs text-emerald-600">
                        Birth date entered - Sire and Dam fields are now available. Purchase Price hidden (born on farm).
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Acquisition Date</label>
                    <Input
                      type="date"
                      name="acquisitionDate"
                      className="focus:ring-2 focus:ring-emerald-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Status</label>
                    <Select
                      name="status"
                      value={status}
                      onValueChange={setStatus}
                    >
                      <SelectTrigger className="focus:ring-2 focus:ring-emerald-500">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Healthy">Healthy</SelectItem>
                        <SelectItem value="Sick">Sick</SelectItem>
                        <SelectItem value="Injured">Injured</SelectItem>
                        <SelectItem value="Quarantined">Quarantined</SelectItem>
                        <SelectItem value="Deceased">Deceased</SelectItem>
                        <SelectItem value="Pregnant">Pregnant</SelectItem>
                        <SelectItem value="Lactating">Lactating</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Weight (kg)</label>
                    <Input
                      type="number"
                      step="0.01"
                      name="weight"
                      placeholder="Enter weight"
                      className="focus:ring-2 focus:ring-emerald-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Color</label>
                    <Input
                      name="color"
                      placeholder="Enter color"
                      className="focus:ring-2 focus:ring-emerald-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Markings</label>
                    <Input
                      name="markings"
                      placeholder="Enter markings"
                      className="focus:ring-2 focus:ring-emerald-500"
                    />
                  </div>
                  {birthDate && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Sire (Father)</label>
                      <Select name="sire" disabled={loadingParents}>
                        <SelectTrigger className="focus:ring-2 focus:ring-emerald-500">
                          <SelectValue placeholder={loadingParents ? "Loading sires..." : "Select sire (optional)"} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">None / Unknown</SelectItem>
                          {sires.map((sire) => (
                            <SelectItem key={sire.id} value={sire.tag_number}>
                              {sire.name} (#{sire.tag_number}) - {sire.breed}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {sires.length === 0 && !loadingParents && (
                        <p className="text-xs text-muted-foreground">
                          No male {selectedAnimalType.toLowerCase()}s found in database
                        </p>
                      )}
                    </div>
                  )}
                  {birthDate && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Dam (Mother)</label>
                      <Select name="dam" disabled={loadingParents}>
                        <SelectTrigger className="focus:ring-2 focus:ring-emerald-500">
                          <SelectValue placeholder={loadingParents ? "Loading dams..." : "Select dam (optional)"} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">None / Unknown</SelectItem>
                          {dams.map((dam) => (
                            <SelectItem key={dam.id} value={dam.tag_number}>
                              {dam.name} (#{dam.tag_number}) - {dam.breed}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {dams.length === 0 && !loadingParents && (
                        <p className="text-xs text-muted-foreground">
                          No female {selectedAnimalType.toLowerCase()}s found in database
                        </p>
                      )}
                    </div>
                  )}
                  {!birthDate && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Purchase Price</label>
                      <Input
                        type="number"
                        step="0.01"
                        name="purchasePrice"
                        placeholder="Enter purchase price"
                        className="focus:ring-2 focus:ring-emerald-500"
                      />
                      <p className="text-xs text-muted-foreground">
                        Hidden when birth date is entered (indicates born on farm)
                      </p>
                    </div>
                  )}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 h-full pt-6">
                      <input
                        type="checkbox"
                        id="isRegistered"
                        name="isRegistered"
                        onChange={(e) => setIsRegistered(e.target.checked)}
                        className="h-4 w-4 rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                      />
                      <label htmlFor="isRegistered" className="text-sm font-medium">
                        Is Registered
                      </label>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Registration Number</label>
                    <Input
                      name="registrationNumber"
                      disabled={!isRegistered}
                      placeholder="Enter registration number"
                      className="focus:ring-2 focus:ring-emerald-500"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Notes</label>
                  <Textarea
                    name="notes"
                    rows={4}
                    placeholder="Enter any additional notes"
                    className="w-full focus:ring-2 focus:ring-emerald-500"
                  />
                </div>

                <div className="flex justify-end space-x-4 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.back()}
                    className="border-emerald-500 text-emerald-600 hover:bg-emerald-50"
                  >
                    Cancel
                  </Button>
                  <SubmitButton animalType={selectedAnimalType} />
                </div>
              </form>
            </CardContent>
            <CardFooter className="bg-gradient-to-r from-emerald-50 to-teal-50 border-t border-emerald-100 flex justify-between items-center">
              <p className="text-xs text-emerald-700">* Required fields</p>
              <p className="text-xs text-emerald-700">Your {selectedAnimalType.toLowerCase()} will be added to the registry immediately</p>
            </CardFooter>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
