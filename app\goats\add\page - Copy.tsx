"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import DashboardLayout from "@/components/dashboard-layout"
import { addGoat } from "@/app/actions/goats"
import { useFormStatus } from "react-dom"

function SubmitButton() {
  const { pending } = useFormStatus()
  
  return (
    <Button type="submit" disabled={pending}>
      {pending ? "Adding..." : "Add Goat"}
    </Button>
  )
}

export default function AddGoatPage() {
  const router = useRouter()
  
  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold mb-6">Add New Goat</h1>
        <form action={async (formData) => {
          try {
            await addGoat(formData)
            toast({
              title: "Success",
              description: "Goat added successfully",
            })
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to add goat. Please try again.",
              variant: "destructive",
            })
          }
        }} className="space-y-6 max-w-2xl">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Tag Number*</label>
              <Input
                name="tagNumber"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Name*</label>
              <Input
                name="name"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Breed*</label>
              <Input
                name="breed"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Gender*</label>
              <select
                name="gender"
                required
                className="w-full rounded-md border border-input bg-background px-3 py-2"
              >
                <option value="">Select Gender</option>
                <option value="Female">Female</option>
                <option value="Male">Male</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Birth Date</label>
              <Input
                type="date"
                name="birthDate"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Acquisition Date</label>
              <Input
                type="date"
                name="acquisitionDate"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Status</label>
              <Select
                name="status"
              >
                <option value="Healthy">Healthy</option>
                <option value="Sick">Sick</option>
                <option value="Pregnant">Pregnant</option>
                <option value="Lactating">Lactating</option>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Weight (kg)</label>
              <Input
                type="number"
                step="0.01"
                name="weight"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Color</label>
              <Input
                name="color"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Markings</label>
              <Input
                name="markings"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Sire (Father)</label>
              <Input
                name="sire"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Dam (Mother)</label>
              <Input
                name="dam"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Purchase Price</label>
              <Input
                type="number"
                step="0.01"
                name="purchasePrice"
              />
            </div>
            <div className="flex items-center space-x-2 mt-4">
              <input
                type="checkbox"
                id="isRegistered"
                name="isRegistered"
                className="h-4 w-4 rounded border-gray-300"
              />
              <label htmlFor="isRegistered" className="text-sm font-medium">
                Is Registered
              </label>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Registration Number</label>
              <Input
                name="registrationNumber"
                disabled={!formData.isRegistered}
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Notes</label>
            <Textarea
              name="notes"
              rows={4}
              className="w-full"
            />
          </div>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <SubmitButton />
          </div>
        </form>
      </div>
    </DashboardLayout>
  )
}







