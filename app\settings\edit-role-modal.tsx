'use client'

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, <PERSON>alog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { LoadingButton } from "@/components/ui/loading-button"
import { useToast } from "@/components/ui/use-toast"
import { LucideAlertCircle, LucideLoader2, LucideShield, LucideCheck, LucideX } from "lucide-react"

interface Permission {
  name: string;
  admin: boolean;
  manager: boolean;
  staff: boolean;
}

interface EditRoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  roleType: 'Admin' | 'Manager' | 'Staff';
  permissions: Permission[];
}

export function EditRoleModal({ isOpen, onClose, onSuccess, roleType, permissions }: EditRoleModalProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [editedPermissions, setEditedPermissions] = useState<Permission[]>([])
  
  // Initialize edited permissions when modal opens or role changes
  useEffect(() => {
    if (isOpen && permissions) {
      // Deep clone the permissions array to avoid modifying the original
      setEditedPermissions(JSON.parse(JSON.stringify(permissions)))
    }
  }, [isOpen, permissions, roleType])
  
  // Get the role property name based on the role type
  const getRoleProperty = (): 'admin' | 'manager' | 'staff' => {
    switch (roleType) {
      case 'Admin': return 'admin'
      case 'Manager': return 'manager'
      case 'Staff': return 'staff'
      default: return 'staff'
    }
  }
  
  // Handle permission toggle
  const handleTogglePermission = (index: number) => {
    const roleProperty = getRoleProperty()
    
    setEditedPermissions(prev => {
      const updated = [...prev]
      updated[index] = {
        ...updated[index],
        [roleProperty]: !updated[index][roleProperty]
      }
      return updated
    })
  }
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError(null)
    
    try {
      // Prepare data for API
      const roleProperty = getRoleProperty()
      const permissionsData = editedPermissions.map(p => ({
        name: p.name,
        value: p[roleProperty]
      }))
      
      // Call API to update role permissions
      const response = await fetch(`/api/roles/${roleType.toLowerCase()}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ permissions: permissionsData }),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update role permissions')
      }
      
      // Show success message
      toast({
        title: "Success",
        description: `${roleType} role permissions have been updated successfully`,
        variant: "default",
      })
      
      // Close modal and refresh
      onSuccess()
      onClose()
      
    } catch (err: any) {
      console.error('Error updating role permissions:', err)
      setError(err.message)
      
      toast({
        title: "Error",
        description: err.message,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  // Get role color based on role type
  const getRoleColor = () => {
    switch (roleType) {
      case 'Admin': return 'text-purple-600'
      case 'Manager': return 'text-blue-600'
      case 'Staff': return 'text-green-600'
      default: return 'text-gray-600'
    }
  }
  
  // Get role background color based on role type
  const getRoleBgColor = () => {
    switch (roleType) {
      case 'Admin': return 'bg-purple-100'
      case 'Manager': return 'bg-blue-100'
      case 'Staff': return 'bg-green-100'
      default: return 'bg-gray-100'
    }
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className={`h-8 w-8 rounded-full ${getRoleBgColor()} flex items-center justify-center`}>
              <LucideShield className={`h-4 w-4 ${getRoleColor()}`} />
            </div>
            <span>Edit {roleType} Role Permissions</span>
          </DialogTitle>
          <DialogDescription>
            Configure which features and actions {roleType} users can access.
          </DialogDescription>
        </DialogHeader>
        
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-red-800 mb-4">
            <div className="flex items-center gap-2">
              <LucideAlertCircle className="h-4 w-4" />
              <span className="font-medium">Error</span>
            </div>
            <p className="text-sm mt-1">{error}</p>
          </div>
        )}
        
        {isLoading ? (
          <div className="py-8 flex justify-center items-center">
            <div className="flex flex-col items-center gap-2">
              <LucideLoader2 className="h-8 w-8 text-purple-500 animate-spin" />
              <p className="text-sm text-muted-foreground">Updating permissions...</p>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-4">
              {editedPermissions.map((permission, index) => {
                const roleProperty = getRoleProperty()
                const isEnabled = permission[roleProperty]
                
                return (
                  <div key={index} className="flex items-center justify-between rounded-lg border p-3">
                    <div className="flex items-center gap-2">
                      {isEnabled ? (
                        <LucideCheck className="h-4 w-4 text-green-500" />
                      ) : (
                        <LucideX className="h-4 w-4 text-red-500" />
                      )}
                      <Label htmlFor={`permission-${index}`} className="font-medium cursor-pointer">
                        {permission.name}
                      </Label>
                    </div>
                    <Switch
                      id={`permission-${index}`}
                      checked={isEnabled}
                      onCheckedChange={() => handleTogglePermission(index)}
                    />
                  </div>
                )
              })}
            </div>
            
            <DialogFooter className="mt-6">
              <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
                Cancel
              </Button>
              <LoadingButton type="submit" isLoading={isLoading}>
                Save Changes
              </LoadingButton>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}
