import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const gender = searchParams.get('gender');

    // Create a connection for this request
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    });

    // Build query with optional gender filter
    let query = `
      SELECT id, name, tag_number as tag, breed, gender, 
             TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) as age, status
      FROM animals
      WHERE animal_type = 'Pig'
    `;
    const queryParams = [];

    if (gender && gender !== 'all') {
      query += ' AND gender = ?';
      queryParams.push(gender);
    }

    query += ' ORDER BY name';

    const [rows] = await connection.execute(query, queryParams);

    // Calculate stats
    const stats = {
      total: rows.length,
      healthy: rows.filter(pig => pig.status === 'Healthy').length,
      sick: rows.filter(pig => pig.status === 'Sick').length,
      injured: rows.filter(pig => pig.status === 'Injured').length,
      quarantined: rows.filter(pig => pig.status === 'Quarantined').length,
      males: rows.filter(pig => pig.gender === 'Male').length,
      females: rows.filter(pig => pig.gender === 'Female').length
    };

    await connection.end();

    return NextResponse.json({
      pigs: rows,
      stats: stats
    });
  } catch (error) {
    console.error('Error fetching pigs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pigs' },
      { status: 500 }
    );
  }
}
