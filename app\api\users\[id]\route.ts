import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { hashPassword, isSecureHashingAvailable } from '@/lib/password-utils'

// GET /api/users/[id] - Get a specific user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate database connection
    if (!db || typeof db.query !== 'function') {
      console.error('Database connection not available');
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      );
    }

    // Ensure params is properly awaited
    const { id: userId } = params;

    // Validate userId
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    try {
      // Test database connection
      await db.testConnection();
    } catch (dbError) {
      console.error('Database connection test failed:', dbError);
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }

    const users = await db.query(
      `SELECT id, username, email, full_name, role, created_at, last_login, is_active
       FROM users
       WHERE id = ?`,
      [userId]
    );

    if (!Array.isArray(users) || users.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(users[0]);
  } catch (error: any) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: `Failed to fetch user: ${error.message || 'Unknown error'}` },
      { status: 500 }
    );
  }
}

// PUT /api/users/[id] - Update a user
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate database connection
    if (!db || typeof db.query !== 'function') {
      console.error('Database connection not available');
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      );
    }

    // Ensure params is properly awaited
    const { id: userId } = params;

    // Validate userId
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    let requestData;
    try {
      requestData = await request.json();
    } catch (parseError) {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    const { username, password, email, full_name, role, is_active } = requestData;

    try {
      // Test database connection
      await db.testConnection();
    } catch (dbError) {
      console.error('Database connection test failed:', dbError);
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }

    // Check if user exists
    const existingUser = await db.query(
      'SELECT id FROM users WHERE id = ?',
      [userId]
    )

    if (existingUser.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if username is already taken by another user
    if (username) {
      const usernameCheck = await db.query(
        'SELECT id FROM users WHERE username = ? AND id != ?',
        [username, userId]
      )

      if (usernameCheck.length > 0) {
        return NextResponse.json(
          { error: 'Username already exists' },
          { status: 400 }
        )
      }
    }

    // Prepare update fields
    const updateFields = []
    const updateValues = []

    if (username) {
      updateFields.push('username = ?')
      updateValues.push(username)
    }

    if (password) {
      const password_hash = await hashPassword(password)

      // Warn if secure hashing is not available
      if (!isSecureHashingAvailable()) {
        console.warn('Using fallback password hashing method - install bcrypt for secure hashing')
      }

      updateFields.push('password_hash = ?')
      updateValues.push(password_hash)
    }

    if (email !== undefined) {
      updateFields.push('email = ?')
      updateValues.push(email)
    }

    if (full_name !== undefined) {
      updateFields.push('full_name = ?')
      updateValues.push(full_name)
    }

    if (role) {
      updateFields.push('role = ?')
      updateValues.push(role)
    }

    if (is_active !== undefined) {
      updateFields.push('is_active = ?')
      updateValues.push(is_active ? 1 : 0)
    }

    // If no fields to update
    if (updateFields.length === 0) {
      return NextResponse.json(
        { error: 'No fields to update' },
        { status: 400 }
      )
    }

    // Add user ID to values array
    updateValues.push(userId)

    // Update user
    await db.query(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    )

    // Get updated user
    const updatedUser = await db.query(
      `SELECT id, username, email, full_name, role, created_at, last_login, is_active
       FROM users
       WHERE id = ?`,
      [userId]
    )

    return NextResponse.json(updatedUser[0])
  } catch (error: any) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { error: `Failed to update user: ${error.message || 'Unknown error'}` },
      { status: 500 }
    )
  }
}

// PATCH /api/users/[id] - Toggle user activation status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log('PATCH request received for user ID:', params.id);

  try {
    // Validate database connection
    if (!db || typeof db.query !== 'function') {
      console.error('Database connection not available');
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      );
    }

    // Ensure params is properly awaited
    const { id: userId } = params;
    console.log('Processing user ID:', userId);

    // Validate userId
    if (!userId) {
      console.error('User ID is missing');
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    let requestData;
    try {
      requestData = await request.json();
      console.log('Request data:', requestData);
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    const { is_active } = requestData;
    console.log('Requested activation status:', is_active);

    if (is_active === undefined) {
      console.error('is_active field is missing');
      return NextResponse.json(
        { error: 'is_active field is required' },
        { status: 400 }
      );
    }

    try {
      // Test database connection
      await db.testConnection();
      console.log('Database connection test successful');
    } catch (dbError) {
      console.error('Database connection test failed:', dbError);
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }

    // Check if user exists
    console.log('Checking if user exists...');
    const existingUser = await db.query(
      'SELECT id, username, is_active FROM users WHERE id = ?',
      [userId]
    )
    console.log('Query result:', existingUser);

    if (!Array.isArray(existingUser) || existingUser.length === 0) {
      console.error('User not found');
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const currentUser = existingUser[0];
    console.log('Current user:', currentUser);

    // Convert boolean to integer (1 or 0)
    const newStatus = is_active ? 1 : 0;
    console.log(`Changing status from ${currentUser.is_active} to ${newStatus}`);

    try {
      // Update user activation status
      console.log('Executing update query...');
      await db.query(
        'UPDATE users SET is_active = ? WHERE id = ?',
        [newStatus, userId]
      );
      console.log('Update successful');

      // Get updated user
      console.log('Fetching updated user data...');
      const updatedUser = await db.query(
        `SELECT id, username, email, full_name, role, created_at, last_login, is_active
         FROM users
         WHERE id = ?`,
        [userId]
      );

      if (!Array.isArray(updatedUser) || updatedUser.length === 0) {
        console.error('Failed to fetch updated user');
        return NextResponse.json(
          { error: 'Failed to fetch updated user' },
          { status: 500 }
        );
      }

      console.log('Updated user:', updatedUser[0]);

      const successMessage = `User ${currentUser.username} has been ${is_active ? 'activated' : 'deactivated'}`;
      console.log('Success:', successMessage);

      return NextResponse.json({
        success: true,
        message: successMessage,
        user: updatedUser[0]
      });
    } catch (queryError: any) {
      console.error('Database query error:', queryError);
      return NextResponse.json(
        { error: `Database query error: ${queryError.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Error toggling user activation:', error)
    return NextResponse.json(
      { error: `Failed to update user activation status: ${error.message || 'Unknown error'}` },
      { status: 500 }
    )
  }
}
