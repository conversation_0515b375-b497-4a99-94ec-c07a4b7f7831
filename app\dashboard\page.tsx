"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import ReportChart from "@/components/report-chart"
import { AddGoatDialog } from "@/components/add-goat-dialog"
import { QuickActionMenu } from "@/components/quick-action-menu"
import { usePermissions } from "@/hooks/usePermissions"
import { Permission } from "@/lib/permissions"
import {
  LucideArrowUpRight,
  LucideArrowDownRight,
  LucideUsers,
  LucideHeart,
  LucideCalendarClock,
  LucideCoins,
  LucideBarChart2,
  LucideAlertTriangle,
  LucideCalendarDays,
  LucideWheat,
  LucidePackage,
  LucideActivity,
  LucideClipboard,
  LucideArrowRight,
  LucideLoader2,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

// Default alerts for the alerts tab
const alerts = [
  {
    id: "alert-001",
    type: "inventory",
    title: "Low Feed Stock: Mineral Blocks",
    description: "Current stock: 5 blocks (below minimum level)",
    severity: "warning",
    module: "inventory",
  },
  {
    id: "alert-002",
    type: "health",
    title: "Vaccination Due",
    description: "Annual vaccination due for 3 goats",
    severity: "info",
    module: "health",
  },
  {
    id: "alert-003",
    type: "breeding",
    title: "Kidding Imminent: Ruby",
    description: "Expected to kid within 7 days",
    severity: "alert",
    module: "breeding",
  },
]

export default function DashboardPage() {
  const [activeTab, setActiveTab] = useState("overview")
  const { hasPermission } = usePermissions()
  const [dashboardData, setDashboardData] = useState({
    farmStats: {
      totalAnimals: 0,
      animalsByType: {
        goats: { total: 0, males: 0, females: 0, healthy: 0, sick: 0, pregnant: 0, lactating: 0 },
        sheep: { total: 0, males: 0, females: 0, healthy: 0, sick: 0, pregnant: 0, lactating: 0 },
        cattle: { total: 0, males: 0, females: 0, healthy: 0, sick: 0, pregnant: 0, lactating: 0 },
        pigs: { total: 0, males: 0, females: 0, healthy: 0, sick: 0, pregnant: 0, lactating: 0 }
      },
      // Legacy fields for backward compatibility
      totalGoats: 0,
      healthyGoats: 0,
      sickGoats: 0,
      pregnantGoats: 0,
      maleGoats: 0,
      femaleGoats: 0,
      activeBreedings: 0,
      upcomingKiddings: 0,
      upcomingBirths: 0,
      feedStock: {
        hay: 0,
        grain: 0,
        alfalfa: 0,
        minerals: 0,
      },
      financialMetrics: {
        monthlyIncome: 0,
        monthlyExpenses: 0,
        netProfit: 0,
        profitMargin: 0,
      },
      inventoryAlerts: 0,
      healthAlerts: 0,
    },
    recentActivities: [],
    upcomingEvents: [],
  })
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fetch dashboard data from API
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true)
        const response = await fetch('/api/dashboard')

        if (!response.ok) {
          throw new Error(`Failed to fetch dashboard data: ${response.status}`)
        }

        const data = await response.json()
        setDashboardData(data)
        setError(null)
      } catch (err) {
        console.error('Error fetching dashboard data:', err)
        setError(err.message)
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  // Get module icon
  const getModuleIcon = (module) => {
    switch (module) {
      case "goats":
        return <LucideUsers className="h-5 w-5 text-emerald-600" />
      case "health":
        return <LucideHeart className="h-5 w-5 text-red-600" />
      case "breeding":
        return <LucideCalendarClock className="h-5 w-5 text-pink-600" />
      case "feeding":
        return <LucideWheat className="h-5 w-5 text-amber-600" />
      case "finance":
        return <LucideCoins className="h-5 w-5 text-cyan-600" />
      case "inventory":
        return <LucidePackage className="h-5 w-5 text-teal-600" />
      case "reports":
        return <LucideBarChart2 className="h-5 w-5 text-purple-600" />
      default:
        return <LucideClipboard className="h-5 w-5" />
    }
  }

  // Get alert severity badge
  const getAlertSeverityBadge = (severity) => {
    switch (severity) {
      case "critical":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200 border-red-300">Critical</Badge>
      case "alert":
        return <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-200 border-orange-300">Alert</Badge>
      case "warning":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200 border-amber-300">Warning</Badge>
      case "info":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300">Info</Badge>
      default:
        return <Badge>{severity}</Badge>
    }
  }

  // Format date for display
  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch (error) {
      return dateString // Return the original string if parsing fails
    }
  }

  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp)
    return date.toLocaleString()
  }

  // Calculate time ago for activities
  const getTimeAgo = (timestamp) => {
    const now = new Date()
    const activityTime = new Date(timestamp)
    const diffMs = now - activityTime
    const diffSec = Math.floor(diffMs / 1000)
    const diffMin = Math.floor(diffSec / 60)
    const diffHour = Math.floor(diffMin / 60)
    const diffDay = Math.floor(diffHour / 24)

    if (diffDay > 0) {
      return `${diffDay} day${diffDay > 1 ? "s" : ""} ago`
    } else if (diffHour > 0) {
      return `${diffHour} hour${diffHour > 1 ? "s" : ""} ago`
    } else if (diffMin > 0) {
      return `${diffMin} minute${diffMin > 1 ? "s" : ""} ago`
    } else {
      return "Just now"
    }
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-3xl font-bold tracking-tight text-gradient-primary">Farm Dashboard</h1>
        <div className="flex gap-2">
          {hasPermission("manage_goats" as Permission) && <AddGoatDialog />}
          <QuickActionMenu />
        </div>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex flex-col items-center justify-center py-12">
          <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
          <p className="text-muted-foreground">Loading dashboard data...</p>
        </div>
      )}

      {/* Error state */}
      {error && !isLoading && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
          <h3 className="font-medium flex items-center gap-2">
            <LucideAlertTriangle className="h-5 w-5" />
            Error Loading Dashboard
          </h3>
          <p className="mt-1 text-sm">{error}</p>
          <Button
            variant="outline"
            className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </div>
      )}

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3 p-1 bg-gradient-to-r from-green-50 via-emerald-50 to-teal-50 rounded-xl">
          <TabsTrigger
            value="overview"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-500 data-[state=active]:text-white transition-all duration-300 hover:text-green-700"
          >
            Overview
          </TabsTrigger>
          <TabsTrigger
            value="analytics"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-teal-500 data-[state=active]:text-white transition-all duration-300 hover:text-emerald-700"
          >
            Analytics
          </TabsTrigger>
          <TabsTrigger
            value="alerts"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-teal-500 data-[state=active]:to-cyan-500 data-[state=active]:text-white transition-all duration-300 hover:text-teal-700"
          >
            Alerts
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab Content */}
        <TabsContent value="overview" className="space-y-6">
          {!isLoading && !error && (
            <>
              {/* Quick Stats */}
              <div className="grid gap-4 md:grid-cols-4">
                <Card className="dashboard-card-primary">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm text-muted-foreground">Total Animals</div>
                        <div className="text-2xl font-bold text-green-600">{dashboardData.farmStats.totalAnimals}</div>
                        <div className="text-xs text-muted-foreground mt-1">
                          🐐 {dashboardData.farmStats.goats?.total || 0} • 🐑 {dashboardData.farmStats.sheep?.total || 0} • 🐄 {dashboardData.farmStats.cattle?.total || 0} • 🐷 {dashboardData.farmStats.pigs?.total || 0}
                        </div>
                      </div>
                      <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                        <LucideUsers className="h-5 w-5 text-green-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-secondary">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm text-muted-foreground">Health Status</div>
                        <div className="text-2xl font-bold text-emerald-600">
                          {(dashboardData.farmStats.goats?.healthy || 0) +
                           (dashboardData.farmStats.sheep?.healthy || 0) +
                           (dashboardData.farmStats.cattle?.healthy || 0) +
                           (dashboardData.farmStats.pigs?.healthy || 0)} Healthy
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {(dashboardData.farmStats.goats?.sick || 0) +
                           (dashboardData.farmStats.sheep?.sick || 0) +
                           (dashboardData.farmStats.cattle?.sick || 0) +
                           (dashboardData.farmStats.pigs?.sick || 0)} sick • {(dashboardData.farmStats.goats?.pregnant || 0) +
                           (dashboardData.farmStats.sheep?.pregnant || 0) +
                           (dashboardData.farmStats.cattle?.pregnant || 0) +
                           (dashboardData.farmStats.pigs?.pregnant || 0)} pregnant
                        </div>
                      </div>
                      <div className="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
                        <LucideHeart className="h-5 w-5 text-emerald-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-accent">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm text-muted-foreground">Breeding</div>
                        <div className="text-2xl font-bold text-pink-600">{dashboardData.farmStats.activeBreedings} Active</div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {dashboardData.farmStats.upcomingKiddings} upcoming kiddings
                        </div>
                      </div>
                      <div className="h-10 w-10 rounded-full bg-pink-100 flex items-center justify-center">
                        <LucideCalendarClock className="h-5 w-5 text-pink-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-amber">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm text-muted-foreground">Financial</div>
                        <div className="text-2xl font-bold text-cyan-600">MWK {dashboardData.farmStats.financialMetrics.netProfit}</div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {dashboardData.farmStats.financialMetrics.profitMargin}% profit margin
                        </div>
                      </div>
                      <div className="h-10 w-10 rounded-full bg-cyan-100 flex items-center justify-center">
                        <LucideCoins className="h-5 w-5 text-cyan-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Animal Type Breakdown */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {/* Goats */}
                <Card className="border-green-200 bg-green-50/50">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg text-green-700">🐐 Goats</CardTitle>
                      <Badge className="bg-green-100 text-green-800">{dashboardData.farmStats.goats?.total || 0}</Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Males:</span>
                      <span className="font-medium">{dashboardData.farmStats.goats?.males || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Females:</span>
                      <span className="font-medium">{dashboardData.farmStats.goats?.females || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-green-600">Healthy:</span>
                      <span className="font-medium text-green-600">{dashboardData.farmStats.goats?.healthy || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-red-600">Sick:</span>
                      <span className="font-medium text-red-600">{dashboardData.farmStats.goats?.sick || 0}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Sheep */}
                <Card className="border-blue-200 bg-blue-50/50">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg text-blue-700">🐑 Sheep</CardTitle>
                      <Badge className="bg-blue-100 text-blue-800">{dashboardData.farmStats.sheep?.total || 0}</Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Males:</span>
                      <span className="font-medium">{dashboardData.farmStats.sheep?.males || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Females:</span>
                      <span className="font-medium">{dashboardData.farmStats.sheep?.females || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-green-600">Healthy:</span>
                      <span className="font-medium text-green-600">{dashboardData.farmStats.sheep?.healthy || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-red-600">Sick:</span>
                      <span className="font-medium text-red-600">{dashboardData.farmStats.sheep?.sick || 0}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Cattle */}
                <Card className="border-amber-200 bg-amber-50/50">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg text-amber-700">🐄 Cattle</CardTitle>
                      <Badge className="bg-amber-100 text-amber-800">{dashboardData.farmStats.cattle?.total || 0}</Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Males:</span>
                      <span className="font-medium">{dashboardData.farmStats.cattle?.males || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Females:</span>
                      <span className="font-medium">{dashboardData.farmStats.cattle?.females || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-green-600">Healthy:</span>
                      <span className="font-medium text-green-600">{dashboardData.farmStats.cattle?.healthy || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-red-600">Sick:</span>
                      <span className="font-medium text-red-600">{dashboardData.farmStats.cattle?.sick || 0}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Pigs */}
                <Card className="border-pink-200 bg-pink-50/50">
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg text-pink-700">🐷 Pigs</CardTitle>
                      <Badge className="bg-pink-100 text-pink-800">{dashboardData.farmStats.pigs?.total || 0}</Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Males:</span>
                      <span className="font-medium">{dashboardData.farmStats.pigs?.males || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Females:</span>
                      <span className="font-medium">{dashboardData.farmStats.pigs?.females || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-green-600">Healthy:</span>
                      <span className="font-medium text-green-600">{dashboardData.farmStats.pigs?.healthy || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-red-600">Sick:</span>
                      <span className="font-medium text-red-600">{dashboardData.farmStats.pigs?.sick || 0}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}

          {/* Feed Stock */}
          {!isLoading && !error && (
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle>Feed Stock Levels</CardTitle>
                  <LucideWheat className="h-5 w-5 text-amber-500" />
                </div>
                <CardDescription>Current feed inventory status</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 rounded-full bg-green-600"></div>
                      <span className="text-sm font-medium">Hay</span>
                    </div>
                    <span className="text-sm font-medium">{dashboardData.farmStats.feedStock.hay}%</span>
                  </div>
                  <div className="progress-primary">
                    <div className="indicator" style={{ width: `${dashboardData.farmStats.feedStock.hay}%` }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 rounded-full bg-emerald-600"></div>
                      <span className="text-sm font-medium">Grain Mix</span>
                    </div>
                    <span className="text-sm font-medium">{dashboardData.farmStats.feedStock.grain}%</span>
                  </div>
                  <div className="progress-secondary">
                    <div className="indicator" style={{ width: `${dashboardData.farmStats.feedStock.grain}%` }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 rounded-full bg-teal-600"></div>
                      <span className="text-sm font-medium">Alfalfa</span>
                    </div>
                    <span className="text-sm font-medium">{dashboardData.farmStats.feedStock.alfalfa}%</span>
                  </div>
                  <div className="progress-accent">
                    <div className="indicator" style={{ width: `${dashboardData.farmStats.feedStock.alfalfa}%` }}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 rounded-full bg-amber-500"></div>
                      <span className="text-sm font-medium">Mineral Blocks</span>
                    </div>
                    <span className="text-sm font-medium">{dashboardData.farmStats.feedStock.minerals}%</span>
                  </div>
                  <div className="progress-amber">
                    <div className="indicator" style={{ width: `${dashboardData.farmStats.feedStock.minerals}%` }}></div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Link href="/feeding" className="w-full">
                  <Button
                    variant="outline"
                    className="w-full border-amber-200 text-amber-600 hover:bg-amber-50 hover:text-amber-700"
                  >
                    <LucideArrowRight className="mr-2 h-4 w-4" />
                    Go to Feeding Management
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          )}

          {/* Recent Activities and Upcoming Events */}
          {!isLoading && !error && (
            <div className="grid gap-6 md:grid-cols-2">
              {/* Recent Activities */}
              <Card className="border-l-4 border-l-green-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle>Recent Activities</CardTitle>
                    <LucideActivity className="h-5 w-5 text-green-500" />
                  </div>
                  <CardDescription>Latest actions across all modules</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-[350px] overflow-y-auto pr-1">
                    {dashboardData.recentActivities && dashboardData.recentActivities.length > 0 ? (
                      dashboardData.recentActivities.map((activity) => (
                        <div
                          key={activity.id}
                          className="flex items-start justify-between p-3 bg-green-50/50 rounded-md border border-green-100 hover:bg-green-50 transition-colors"
                        >
                          <div className="flex items-start gap-3">
                            <div className="mt-0.5">{getModuleIcon(activity.module)}</div>
                            <div>
                              <div className="font-medium">{activity.title}</div>
                              <div className="text-xs text-muted-foreground">{getTimeAgo(activity.timestamp)}</div>
                            </div>
                          </div>
                          <div className="text-xs font-medium text-green-600">{getTimeAgo(activity.timestamp)}</div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-6 text-muted-foreground">
                        No recent activities found
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter>
                  <Link href="/reports/activities" className="w-full">
                    <Button
                      variant="outline"
                      className="w-full border-green-200 text-green-600 hover:bg-green-50 hover:text-green-700"
                    >
                      <LucideClipboard className="mr-2 h-4 w-4" />
                      View All Activities
                    </Button>
                  </Link>
                </CardFooter>
              </Card>

              {/* Upcoming Events */}
              <Card className="border-l-4 border-l-emerald-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle>Upcoming Events</CardTitle>
                    <LucideCalendarDays className="h-5 w-5 text-emerald-500" />
                  </div>
                  <CardDescription>Scheduled events and reminders</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData.upcomingEvents && dashboardData.upcomingEvents.length > 0 ? (
                      dashboardData.upcomingEvents.map((event) => (
                        <div
                          key={event.id}
                          className="flex items-start justify-between p-3 bg-emerald-50/50 rounded-md border border-emerald-100 hover:bg-emerald-50 transition-colors"
                        >
                          <div className="flex items-start gap-3">
                            <div className="mt-0.5">{getModuleIcon(event.module)}</div>
                            <div>
                              <div className="font-medium">{event.title}</div>
                              <div className="text-xs text-muted-foreground">{formatDate(event.date)}</div>
                            </div>
                          </div>
                          <Link href={`/${event.module}`}>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-emerald-600">
                              <LucideArrowUpRight className="h-4 w-4" />
                            </Button>
                          </Link>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-6 text-muted-foreground">
                        No upcoming events found
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter>
                  <Link href="/calendar" className="w-full">
                    <Button
                      variant="outline"
                      className="w-full border-emerald-200 text-emerald-600 hover:bg-emerald-50 hover:text-emerald-700"
                    >
                      <LucideCalendarDays className="mr-2 h-4 w-4" />
                      View Full Calendar
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            </div>
          )}

          {/* Financial Summary */}
          {!isLoading && !error && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Financial Summary</CardTitle>
                  <LucideCoins className="h-5 w-5 text-cyan-500" />
                </div>
                <CardDescription>Monthly income and expenses</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                          <LucideArrowUpRight className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <div className="text-sm font-medium">Income</div>
                          <div className="text-2xl font-bold text-green-700">
                            MWK {dashboardData.farmStats.financialMetrics.monthlyIncome}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                          <LucideArrowDownRight className="h-5 w-5 text-red-600" />
                        </div>
                        <div>
                          <div className="text-sm font-medium">Expenses</div>
                          <div className="text-2xl font-bold text-red-700">
                            MWK {dashboardData.farmStats.financialMetrics.monthlyExpenses}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="bg-gradient-to-br from-cyan-50 to-cyan-100 border-cyan-200">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-full bg-cyan-100 flex items-center justify-center">
                          <LucideCoins className="h-5 w-5 text-cyan-600" />
                        </div>
                        <div>
                          <div className="text-sm font-medium">Net Profit</div>
                          <div className="text-2xl font-bold text-cyan-700">
                            MWK {dashboardData.farmStats.financialMetrics.netProfit}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
                <div className="mt-6 h-[200px]">
                  <ReportChart
                    type="bar"
                    title="Monthly Financial Performance"
                    data={{
                      labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                      datasets: [
                        {
                          label: "Income",
                          data: [1800, 2200, 1900, 2500, 2700, 2900],
                          backgroundColor: "rgba(34, 197, 94, 0.5)",
                          borderColor: "rgb(34, 197, 94)",
                        },
                        {
                          label: "Expenses",
                          data: [1200, 1400, 1300, 1600, 1700, 1800],
                          backgroundColor: "rgba(239, 68, 68, 0.5)",
                          borderColor: "rgb(239, 68, 68)",
                        },
                        {
                          label: "Profit",
                          data: [600, 800, 600, 900, 1000, 1100],
                          backgroundColor: "rgba(6, 182, 212, 0.5)",
                          borderColor: "rgb(6, 182, 212)",
                        },
                      ],
                    }}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Link href="/finance" className="w-full">
                  <Button
                    variant="outline"
                    className="w-full border-cyan-200 text-cyan-600 hover:bg-cyan-50 hover:text-cyan-700"
                  >
                    <LucideArrowRight className="mr-2 h-4 w-4" />
                    Go to Financial Management
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          )}
        </TabsContent>

        {/* Analytics Tab Content */}
        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                <div>
                  <CardTitle>Farm Analytics</CardTitle>
                  <CardDescription>Performance metrics and trends</CardDescription>
                </div>
                <Link href="/reports">
                  <Button className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
                    <LucideBarChart2 className="mr-2 h-4 w-4" />
                    View Detailed Reports
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-emerald-700">Goat Population</h3>
                  <div className="h-[250px]">
                    <ReportChart
                      type="pie"
                      title="Goat Breeds"
                      data={{
                        labels: ["Boer", "Alpine", "Nubian", "Saanen", "Other"],
                        datasets: [
                          {
                            data: [40, 20, 15, 15, 10],
                            backgroundColor: [
                              "rgba(34, 197, 94, 0.7)",
                              "rgba(16, 185, 129, 0.7)",
                              "rgba(20, 184, 166, 0.7)",
                              "rgba(6, 182, 212, 0.7)",
                              "rgba(59, 130, 246, 0.7)",
                            ],
                            borderColor: [
                              "rgb(34, 197, 94)",
                              "rgb(16, 185, 129)",
                              "rgb(20, 184, 166)",
                              "rgb(6, 182, 212)",
                              "rgb(59, 130, 246)",
                            ],
                          },
                        ],
                      }}
                    />
                  </div>
                </div>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-emerald-700">Health Trends</h3>
                  <div className="h-[250px]">
                    <ReportChart
                      type="line"
                      title="Health Incidents"
                      data={{
                        labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                        datasets: [
                          {
                            label: "Incidents",
                            data: [5, 3, 4, 2, 3, 1],
                            borderColor: "rgb(239, 68, 68)",
                            backgroundColor: "rgba(239, 68, 68, 0.1)",
                            tension: 0.3,
                            fill: true,
                          },
                        ],
                      }}
                    />
                  </div>
                </div>
              </div>
              <div className="text-center py-4 text-muted-foreground">
                <p className="max-w-md mx-auto mb-6">
                  The analytics tab provides detailed insights into your farm's performance metrics, trends, and key
                  indicators.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Alerts Tab Content */}
        <TabsContent value="alerts" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                <div>
                  <CardTitle>System Alerts</CardTitle>
                  <CardDescription>Important notifications requiring attention</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.map((alert) => (
                  <div
                    key={alert.id}
                    className="flex items-start justify-between p-4 bg-teal-50/50 rounded-md border border-teal-100 hover:bg-teal-50 transition-colors"
                  >
                    <div className="flex items-start gap-3">
                      <div className="mt-0.5">
                        {alert.severity === "critical" || alert.severity === "alert" ? (
                          <LucideAlertTriangle className="h-5 w-5 text-red-500" />
                        ) : alert.severity === "warning" ? (
                          <LucideAlertTriangle className="h-5 w-5 text-amber-500" />
                        ) : (
                          getModuleIcon(alert.module)
                        )}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <div className="font-medium">{alert.title}</div>
                          {getAlertSeverityBadge(alert.severity)}
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">{alert.description}</div>
                      </div>
                    </div>
                    <Link href={`/${alert.module}`}>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-teal-600">
                        <LucideArrowUpRight className="h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                ))}
              </div>
              <div className="text-center py-4 text-muted-foreground">
                <p className="max-w-md mx-auto mb-6">
                  The alerts tab shows important notifications that require your attention across all farm management
                  modules.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Access Module Cards */}
      {!isLoading && !error && (() => {
        // Define quick access cards with their required permissions
        const quickAccessCards = [
          {
            href: "/goats",
            permission: "view_goats" as Permission,
            icon: LucideUsers,
            iconColor: "text-emerald-600",
            bgColor: "bg-emerald-100",
            title: "Goat Registry",
            description: `${dashboardData.farmStats.totalGoats} goats`
          },
          {
            href: "/health",
            permission: "add_health_records" as Permission,
            icon: LucideHeart,
            iconColor: "text-red-600",
            bgColor: "bg-red-100",
            title: "Health Records",
            description: `${dashboardData.farmStats.healthAlerts} alerts`
          },
          {
            href: "/breeding",
            permission: "manage_breeding" as Permission,
            icon: LucideCalendarClock,
            iconColor: "text-pink-600",
            bgColor: "bg-pink-100",
            title: "Breeding",
            description: `${dashboardData.farmStats.activeBreedings} active`
          },
          {
            href: "/feeding",
            permission: "manage_feeding" as Permission,
            icon: LucideWheat,
            iconColor: "text-amber-600",
            bgColor: "bg-amber-100",
            title: "Feeding",
            description: "Feed management"
          },
          {
            href: "/finance",
            permission: "manage_finances" as Permission,
            icon: LucideCoins,
            iconColor: "text-cyan-600",
            bgColor: "bg-cyan-100",
            title: "Finance",
            description: `MWK ${dashboardData.farmStats.financialMetrics.netProfit} profit`
          },
          {
            href: "/inventory",
            permission: "manage_inventory" as Permission,
            icon: LucidePackage,
            iconColor: "text-teal-600",
            bgColor: "bg-teal-100",
            title: "Inventory",
            description: `${dashboardData.farmStats.inventoryAlerts} alerts`
          }
        ]

        // Filter cards based on user permissions
        const visibleCards = quickAccessCards.filter(card =>
          hasPermission(card.permission)
        )

        return (
          <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
            {visibleCards.map((card, index) => (
              <Link key={index} href={card.href}>
                <Card className="hover-lift hover-glow-primary cursor-pointer transition-all duration-300">
                  <CardContent className="p-4 flex flex-col items-center justify-center text-center">
                    <div className={`h-12 w-12 rounded-full ${card.bgColor} flex items-center justify-center mb-3`}>
                      <card.icon className={`h-6 w-6 ${card.iconColor}`} />
                    </div>
                    <h3 className="font-medium">{card.title}</h3>
                    <p className="text-xs text-muted-foreground mt-1">{card.description}</p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        )
      })()}
    </div>
  )
}





