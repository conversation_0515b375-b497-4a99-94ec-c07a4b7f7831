# Database Migrations

This directory contains database migration scripts for the Goat Farm Management System.

## Migration: Add is_active to Users Table

### Purpose
This migration adds an `is_active` column to the `users` table to support user activation/deactivation instead of deletion.

### Changes
- Adds `is_active` column (TINYINT(1)) with a default value of 1 (active)
- Sets all existing users to active
- Adds an index on the column for faster queries
- Adds a comment to document the column's purpose

### Running the Migration

#### Option 1: Using the provided script
```bash
# Install dependencies if needed
npm install mysql2 dotenv

# Run the migration script
node scripts/run_migration.js
```

#### Option 2: Manual execution
You can run the SQL directly in your MySQL client:

```bash
mysql -u your_username -p your_database < migrations/add_is_active_to_users.sql
```

### Verification
After running the migration, you can verify it was successful by:

```sql
DESCRIBE users;
```

You should see the `is_active` column in the results.

## Post-Migration Notes

- The application now uses the `is_active` field to determine if a user can log in
- Users are no longer deleted; they are deactivated instead
- This preserves data integrity and allows for user reactivation when needed
