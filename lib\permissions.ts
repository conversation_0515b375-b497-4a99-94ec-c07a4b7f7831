/**
 * Role-based access control (RBAC) system
 * Defines permissions for each user role
 */

export type UserRole = 'Admin' | 'Manager' | 'Staff' | 'Viewer';

export type Permission = 
  | 'manage_users'
  | 'manage_goats'
  | 'view_goats'
  | 'manage_health_records'
  | 'add_health_records'
  | 'manage_breeding'
  | 'manage_finances'
  | 'system_settings'
  | 'view_reports'
  | 'manage_inventory'
  | 'manage_feeding';

// Define permissions for each role
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  Admin: [
    'manage_users',
    'manage_goats',
    'view_goats',
    'manage_health_records',
    'add_health_records',
    'manage_breeding',
    'manage_finances',
    'system_settings',
    'view_reports',
    'manage_inventory',
    'manage_feeding'
  ],
  Manager: [
    'manage_goats',
    'view_goats',
    'manage_health_records',
    'add_health_records',
    'manage_breeding',
    'manage_finances',
    'view_reports',
    'manage_inventory',
    'manage_feeding'
  ],
  Staff: [
    'view_goats',
    'add_health_records'
  ],
  Viewer: [
    'view_goats'
  ]
};

// Permission descriptions for UI display
export const PERMISSION_DESCRIPTIONS: Record<Permission, string> = {
  manage_users: 'Manage Users',
  manage_goats: 'Manage Goats',
  view_goats: 'View Goats',
  manage_health_records: 'Manage Health Records',
  add_health_records: 'Add Health Records',
  manage_breeding: 'Manage Breeding',
  manage_finances: 'Manage Finances',
  system_settings: 'System Settings',
  view_reports: 'View Reports',
  manage_inventory: 'Manage Inventory',
  manage_feeding: 'Manage Feeding'
};

/**
 * Check if a user role has a specific permission
 */
export function hasPermission(userRole: UserRole, permission: Permission): boolean {
  return ROLE_PERMISSIONS[userRole]?.includes(permission) || false;
}

/**
 * Check if a user role has any of the specified permissions
 */
export function hasAnyPermission(userRole: UserRole, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(userRole, permission));
}

/**
 * Check if a user role has all of the specified permissions
 */
export function hasAllPermissions(userRole: UserRole, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(userRole, permission));
}

/**
 * Get all permissions for a user role
 */
export function getRolePermissions(userRole: UserRole): Permission[] {
  return ROLE_PERMISSIONS[userRole] || [];
}

/**
 * Check if a user can access a specific route based on their role
 */
export function canAccessRoute(userRole: UserRole, route: string): boolean {
  // Define route permissions
  const routePermissions: Record<string, Permission[]> = {
    '/settings': ['system_settings'],
    '/settings/users': ['manage_users'],
    '/settings/roles': ['manage_users'],
    '/goats': ['view_goats'],
    '/goats/add': ['manage_goats'],
    '/goats/edit': ['manage_goats'],
    '/health': ['view_goats', 'add_health_records'],
    '/health/add': ['add_health_records'],
    '/health/edit': ['manage_health_records'],
    '/breeding': ['manage_breeding'],
    '/breeding/add': ['manage_breeding'],
    '/breeding/edit': ['manage_breeding'],
    '/finance': ['manage_finances'],
    '/finance/add': ['manage_finances'],
    '/finance/edit': ['manage_finances'],
    '/inventory': ['manage_inventory'],
    '/inventory/add': ['manage_inventory'],
    '/inventory/edit': ['manage_inventory'],
    '/feeding': ['manage_feeding'],
    '/feeding/add': ['manage_feeding'],
    '/feeding/edit': ['manage_feeding'],
    '/reports': ['view_reports']
  };

  // Check if route requires specific permissions
  const requiredPermissions = routePermissions[route];
  if (!requiredPermissions) {
    // If no specific permissions required, allow access
    return true;
  }

  // Check if user has any of the required permissions
  return hasAnyPermission(userRole, requiredPermissions);
}

/**
 * Get user role from user object or localStorage
 */
export function getUserRole(): UserRole | null {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      return user.role as UserRole;
    }
  } catch (error) {
    console.error('Error getting user role:', error);
  }

  return null;
}

/**
 * Check if current user has permission
 */
export function currentUserHasPermission(permission: Permission): boolean {
  const userRole = getUserRole();
  if (!userRole) return false;
  return hasPermission(userRole, permission);
}

/**
 * Check if current user can access route
 */
export function currentUserCanAccessRoute(route: string): boolean {
  const userRole = getUserRole();
  if (!userRole) return false;
  return canAccessRoute(userRole, route);
}
