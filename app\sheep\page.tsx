"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import DashboardLayout from "@/components/dashboard-layout"
import { Badge } from "@/components/ui/badge"
import {
  LucideFilter,
  LucidePlus,
  LucideSearch,
  LucideRefreshCw,
  LucideChevronDown,
  LucideX,
  LucideLoader2,
  LucideAlertTriangle
} from "lucide-react"
import { ErrorBoundary } from "react-error-boundary"
import { useSearch } from "@/hooks/use-search"

// Define the Sheep interface
interface Sheep {
  id: number;
  name: string;
  tag: string;
  breed: string;
  gender: string;
  age: string;
  status: string;
  [key: string]: any; // Allow additional properties
}

function ErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {
  return (
    <div className="p-6 bg-red-50 rounded-lg border border-red-200">
      <h2 className="text-lg font-semibold text-red-800 mb-2">Something went wrong:</h2>
      <pre className="text-sm text-red-600 overflow-auto p-2 bg-red-100 rounded">{error.message}</pre>
      <Button variant="outline" className="mt-4" onClick={resetErrorBoundary}>
        Try again
      </Button>
    </div>
  )
}

// Skeleton loader for sheep cards
function SheepCardSkeleton() {
  return (
    <div className="goat-profile-card animate-pulse">
      <div className="h-48 w-full bg-gray-200"></div>
      <div className="goat-info">
        <div className="h-5 w-24 bg-white/30 rounded mb-2"></div>
        <div className="h-4 w-32 bg-white/30 rounded mb-4"></div>
        <div className="flex items-center justify-between">
          <div className="h-6 w-16 bg-white/30 rounded"></div>
          <div className="h-4 w-10 bg-white/30 rounded"></div>
        </div>
      </div>
    </div>
  )
}

export default function SheepPage() {
  const [breedFilter, setBreedFilter] = useState("all")
  const [genderFilter, setGenderFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [activeView, setActiveView] = useState("grid")
  const [isLoading, setIsLoading] = useState(true)
  const [showFilters, setShowFilters] = useState(false)
  const [sheepData, setSheepData] = useState<Sheep[]>([])
  const [stats, setStats] = useState({
    total: 0,
    healthy: 0,
    sick: 0,
    injured: 0,
    quarantined: 0,
    males: 0,
    females: 0
  })
  const [error, setError] = useState<string | null>(null)

  // Fetch sheep data from API
  useEffect(() => {
    const fetchSheepData = async () => {
      try {
        setIsLoading(true)
        const response = await fetch('/api/sheep')

        if (!response.ok) {
          throw new Error(`Failed to fetch sheep data: ${response.status}`)
        }

        const data = await response.json()

        // Ensure we have valid data structure
        if (data && typeof data === 'object') {
          // Set sheep data with fallback to empty array
          setSheepData(Array.isArray(data.sheep) ? data.sheep : [])

          // Set stats with fallback to default values
          if (data.stats && typeof data.stats === 'object') {
            setStats({
              total: data.stats.total || 0,
              healthy: data.stats.healthy || 0,
              sick: data.stats.sick || 0,
              injured: data.stats.injured || 0,
              quarantined: data.stats.quarantined || 0,
              males: data.stats.males || 0,
              females: data.stats.females || 0
            })
          } else {
            // Calculate stats from sheep data if stats not provided
            const sheep: Sheep[] = Array.isArray(data.sheep) ? data.sheep : []
            const calculatedStats = {
              total: sheep.length,
              healthy: sheep.filter((s: Sheep) => s.status === 'Healthy').length,
              sick: sheep.filter((s: Sheep) => s.status === 'Sick').length,
              injured: sheep.filter((s: Sheep) => s.status === 'Injured').length,
              quarantined: sheep.filter((s: Sheep) => s.status === 'Quarantined').length,
              males: sheep.filter((s: Sheep) => s.gender === 'Male').length,
              females: sheep.filter((s: Sheep) => s.gender === 'Female').length
            }
            setStats(calculatedStats)
          }
        } else {
          // If data is invalid, set empty defaults
          setSheepData([])
          setStats({
            total: 0,
            healthy: 0,
            sick: 0,
            injured: 0,
            quarantined: 0,
            males: 0,
            females: 0
          })
        }

        setError(null)
      } catch (err) {
        console.error('Error fetching sheep data:', err)
        setError(err instanceof Error ? err.message : 'An unknown error occurred')

        // Set empty defaults on error
        setSheepData([])
        setStats({
          total: 0,
          healthy: 0,
          sick: 0,
          injured: 0,
          quarantined: 0,
          males: 0,
          females: 0
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchSheepData()
  }, [])

  // Use the custom search hook
  const {
    searchTerm,
    setSearchTerm,
    filteredItems: searchFilteredSheep,
    isSearching,
  } = useSearch<Sheep>({
    items: sheepData,
    searchFields: ["id", "name", "tag", "breed", "status"],
  })

  // Apply additional filters (breed, gender, status)
  const getFilteredSheep = (): Sheep[] => {
    return searchFilteredSheep.filter((sheep: Sheep) => {
      const matchesBreed = breedFilter === "all" || sheep.breed === breedFilter
      const matchesGender = genderFilter === "all" || sheep.gender === genderFilter
      const matchesStatus = statusFilter === "all" || sheep.status === statusFilter

      return matchesBreed && matchesGender && matchesStatus
    })
  }

  // Filter sheep based on search term and filters
  const filteredSheep = getFilteredSheep()

  const resetFilters = () => {
    setSearchTerm("")
    setBreedFilter("all")
    setGenderFilter("all")
    setStatusFilter("all")
  }

  // Handle search input change with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        {/* Header with stats */}
        <div className="flex flex-col gap-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <h1 className="text-3xl font-bold tracking-tight text-gradient-primary">Sheep Registry</h1>
            <Link href="/sheep/add">
              <Button className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-md hover:shadow-lg transition-all duration-300">
                <LucidePlus className="mr-2 h-4 w-4" />
                Add New Sheep
              </Button>
            </Link>
          </div>

          {/* Error state */}
          {error && !isLoading && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
              <h3 className="font-medium flex items-center gap-2">
                <LucideAlertTriangle className="h-5 w-5" />
                Error Loading Sheep
              </h3>
              <p className="mt-1 text-sm">{error}</p>
              <Button
                variant="outline"
                className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
                onClick={() => window.location.reload()}
              >
                Retry
              </Button>
            </div>
          )}

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            {isLoading ? (
              // Loading skeleton for stats cards
              Array(6).fill(0).map((_, i) => (
                <Card key={i} className="dashboard-card-primary">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Loading...</div>
                    <div className="h-8 w-12 bg-gray-200 animate-pulse rounded mt-1"></div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <>
                <Card className="dashboard-card-primary">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Total</div>
                    <div className="text-2xl font-bold">{stats?.total ?? 0}</div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-secondary">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Healthy</div>
                    <div className="text-2xl font-bold text-green-600">{stats?.healthy ?? 0}</div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-accent">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Sick</div>
                    <div className="text-2xl font-bold text-red-600">{stats?.sick ?? 0}</div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-amber">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Injured</div>
                    <div className="text-2xl font-bold text-orange-600">{stats?.injured ?? 0}</div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-secondary">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Males</div>
                    <div className="text-2xl font-bold text-purple-600">{stats?.males ?? 0}</div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-primary">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Females</div>
                    <div className="text-2xl font-bold text-pink-600">{stats?.females ?? 0}</div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </div>

        {/* Search and filters */}
        <Card className="overflow-hidden">
          <CardHeader className="p-4 pb-0">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
              <CardTitle>Sheep Registry</CardTitle>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="sm:hidden" onClick={() => setShowFilters(!showFilters)}>
                  <LucideFilter className="h-4 w-4 mr-2" />
                  Filters
                  <LucideChevronDown
                    className={`h-4 w-4 ml-1 transition-transform ${showFilters ? "rotate-180" : ""}`}
                  />
                </Button>
                <div className="flex border rounded-md overflow-hidden">
                  <Button
                    variant={activeView === "grid" ? "default" : "ghost"}
                    size="sm"
                    className={
                      activeView === "grid"
                        ? "rounded-none bg-gradient-to-r from-blue-500 to-indigo-500 text-white"
                        : "rounded-none text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                    }
                    onClick={() => setActiveView("grid")}
                  >
                    Grid
                  </Button>
                  <Button
                    variant={activeView === "table" ? "default" : "ghost"}
                    size="sm"
                    className={
                      activeView === "table"
                        ? "rounded-none bg-gradient-to-r from-indigo-500 to-purple-500 text-white"
                        : "rounded-none text-purple-600 hover:text-purple-700 hover:bg-purple-50"
                    }
                    onClick={() => setActiveView("table")}
                  >
                    Table
                  </Button>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="flex flex-col gap-4">
              <div className="relative">
                <LucideSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by ID, name, tag or breed..."
                  className="pl-10 pr-10 input-primary"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
                {searchTerm && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-700"
                    onClick={() => setSearchTerm("")}
                  >
                    <LucideX className="h-4 w-4" />
                  </Button>
                )}
              </div>

              <div className={`grid gap-4 md:grid-cols-4 ${showFilters ? "block" : "hidden sm:grid"}`}>
                <div>
                  <Select value={breedFilter} onValueChange={setBreedFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Breed" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Breeds</SelectItem>
                      <SelectItem value="Dorper">Dorper</SelectItem>
                      <SelectItem value="Merino">Merino</SelectItem>
                      <SelectItem value="Romney">Romney</SelectItem>
                      <SelectItem value="Suffolk">Suffolk</SelectItem>
                      <SelectItem value="Katahdin">Katahdin</SelectItem>
                      <SelectItem value="Barbados Blackbelly">Barbados Blackbelly</SelectItem>
                      <SelectItem value="Local Breed">Local Breed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Select value={genderFilter} onValueChange={setGenderFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Genders</SelectItem>
                      <SelectItem value="Male">Male</SelectItem>
                      <SelectItem value="Female">Female</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="Healthy">Healthy</SelectItem>
                      <SelectItem value="Sick">Sick</SelectItem>
                      <SelectItem value="Injured">Injured</SelectItem>
                      <SelectItem value="Quarantined">Quarantined</SelectItem>
                      <SelectItem value="Deceased">Deceased</SelectItem>
                      <SelectItem value="Pregnant">Pregnant</SelectItem>
                      <SelectItem value="Lactating">Lactating</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button
                  variant="outline"
                  className="flex items-center gap-2 border-blue-500 text-blue-600 hover:bg-blue-50 hover:text-blue-700"
                  onClick={resetFilters}
                  disabled={breedFilter === "all" && genderFilter === "all" && statusFilter === "all" && !searchTerm}
                >
                  <LucideRefreshCw className="h-4 w-4" />
                  Reset Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <ErrorBoundary FallbackComponent={ErrorFallback} onReset={() => window.location.reload()}>
          {activeView === "grid" ? (
            <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {isLoading ? (
                // Full loading state
                <div className="col-span-full flex flex-col items-center justify-center py-12">
                  <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                  <p className="text-muted-foreground">Loading sheep data...</p>
                </div>
              ) : isSearching ? (
                // Skeleton loading state for search
                Array(8)
                  .fill(0)
                  .map((_, i) => <SheepCardSkeleton key={i} />)
              ) : filteredSheep.length > 0 ? (
                filteredSheep.map((sheep) => {
                  // Function to get appropriate sheep image based on breed and gender
                  const getSheepImage = (breed: string, gender: string) => {
                    // Use a placeholder service that shows sheep-related content
                    const sheepText = encodeURIComponent(`${breed} ${gender} Sheep`)

                    // Dorper sheep - white with black head
                    if (breed.toLowerCase().includes('dorper')) {
                      return `https://via.placeholder.com/300x200/F5F5DC/000000?text=${sheepText}`
                    }
                    // Merino sheep - white wool
                    else if (breed.toLowerCase().includes('merino')) {
                      return `https://via.placeholder.com/300x200/FFFFFF/8B4513?text=${sheepText}`
                    }
                    // Local breeds - mixed colors
                    else if (breed.toLowerCase().includes('local')) {
                      return `https://via.placeholder.com/300x200/D2B48C/654321?text=${sheepText}`
                    }
                    // Other breeds - general sheep images
                    else {
                      return `https://via.placeholder.com/300x200/F0F8FF/4682B4?text=${sheepText}`
                    }
                  }

                  return (
                  <Link href={`/sheep/${sheep.id}`} key={sheep.id} className="block hover-lift hover-glow-primary">
                    <div className="goat-profile-card">
                      <img
                        src={getSheepImage(sheep.breed, sheep.gender)}
                        alt={`${sheep.name} - ${sheep.breed} ${sheep.gender}`}
                        className="goat-image"
                        onError={(e) => {
                          e.currentTarget.src = `/placeholder.svg?height=200&width=300&text=${encodeURIComponent(sheep.name)}`
                        }}
                      />
                      <div className="goat-info">
                        <h3 className="text-lg font-bold">{sheep.name}</h3>
                        <p className="text-sm opacity-90">
                          {sheep.breed} • {sheep.gender} • {sheep.age}
                        </p>
                        <div className="flex items-center justify-between mt-2">
                          <Badge
                            variant={
                              sheep.status === "Healthy"
                                ? "outline"
                                : sheep.status === "Sick"
                                  ? "destructive"
                                  : sheep.status === "Injured"
                                    ? "secondary"
                                    : sheep.status === "Quarantined"
                                      ? "default"
                                      : sheep.status === "Deceased"
                                        ? "outline"
                                        : sheep.status === "Pregnant"
                                          ? "default"
                                          : sheep.status === "Lactating"
                                            ? "default"
                                            : "default"
                            }
                            className={
                              sheep.status === "Healthy"
                                ? "badge-healthy"
                                : sheep.status === "Sick"
                                  ? "badge-sick"
                                  : sheep.status === "Injured"
                                    ? "badge-injured"
                                    : sheep.status === "Quarantined"
                                      ? "badge-quarantined"
                                      : sheep.status === "Deceased"
                                        ? "badge-deceased"
                                        : sheep.status === "Pregnant"
                                          ? "bg-pink-100 text-pink-800 hover:bg-pink-200 border-pink-300"
                                          : sheep.status === "Lactating"
                                            ? "bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300"
                                            : ""
                            }
                          >
                            {sheep.status}
                          </Badge>
                          <span className="text-xs">{sheep.tag}</span>
                        </div>
                      </div>
                    </div>
                  </Link>
                  )
                })
              ) : (
                <div className="col-span-full flex flex-col items-center justify-center p-8 text-center">
                  <div className="rounded-full bg-muted p-3 mb-4">
                    <LucideSearch className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-1">No sheep found</h3>
                  <p className="text-muted-foreground mb-4">Try adjusting your search or filters</p>
                  <Button variant="outline" onClick={resetFilters}>
                    Reset Filters
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="p-0 overflow-auto">
                {isLoading ? (
                  <div className="p-8 flex flex-col items-center justify-center">
                    <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                    <p className="text-muted-foreground">Loading sheep data...</p>
                  </div>
                ) : isSearching ? (
                  <div className="p-8 flex justify-center">
                    <LucideRefreshCw className="h-8 w-8 text-primary animate-spin" />
                  </div>
                ) : (
                  <Table className="responsive-table">
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Name</TableHead>
                        <TableHead>Breed</TableHead>
                        <TableHead>Gender</TableHead>
                        <TableHead>Age</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Tag</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredSheep.length > 0 ? (
                        filteredSheep.map((sheep) => (
                          <TableRow key={sheep.id} className="hover-lift">
                            <TableCell className="font-medium" data-label="ID">
                              {sheep.id}
                            </TableCell>
                            <TableCell data-label="Name">{sheep.name}</TableCell>
                            <TableCell data-label="Breed">{sheep.breed}</TableCell>
                            <TableCell data-label="Gender">{sheep.gender}</TableCell>
                            <TableCell data-label="Age">{sheep.age}</TableCell>
                            <TableCell data-label="Status">
                              <Badge
                                variant={
                                  sheep.status === "Healthy"
                                    ? "outline"
                                    : sheep.status === "Sick"
                                      ? "destructive"
                                      : sheep.status === "Injured"
                                        ? "secondary"
                                        : sheep.status === "Quarantined"
                                          ? "default"
                                          : sheep.status === "Deceased"
                                            ? "outline"
                                            : sheep.status === "Pregnant"
                                              ? "default"
                                              : sheep.status === "Lactating"
                                                ? "default"
                                                : "default"
                                }
                                className={
                                  sheep.status === "Healthy"
                                    ? "badge-healthy"
                                    : sheep.status === "Sick"
                                      ? "badge-sick"
                                      : sheep.status === "Injured"
                                        ? "badge-injured"
                                        : sheep.status === "Quarantined"
                                          ? "badge-quarantined"
                                          : sheep.status === "Deceased"
                                            ? "badge-deceased"
                                            : sheep.status === "Pregnant"
                                              ? "bg-pink-100 text-pink-800 hover:bg-pink-200 border-pink-300"
                                              : sheep.status === "Lactating"
                                                ? "bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300"
                                                : ""
                                }
                              >
                                {sheep.status}
                              </Badge>
                            </TableCell>
                            <TableCell data-label="Tag">{sheep.tag}</TableCell>
                            <TableCell className="text-right" data-label="Actions">
                              <Link href={`/sheep/${sheep.id}`}>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-600 hover:from-blue-100 hover:to-indigo-100 hover:text-blue-700"
                                >
                                  View
                                </Button>
                              </Link>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-6">
                            <div className="flex flex-col items-center justify-center p-8">
                              <div className="rounded-full bg-muted p-3 mb-4">
                                <LucideSearch className="h-6 w-6 text-muted-foreground" />
                              </div>
                              <h3 className="text-lg font-medium mb-1">No sheep found</h3>
                              <p className="text-muted-foreground mb-4">Try adjusting your search or filters</p>
                              <Button variant="outline" onClick={resetFilters}>
                                Reset Filters
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          )}
        </ErrorBoundary>
      </div>
    </DashboardLayout>
  )
}
