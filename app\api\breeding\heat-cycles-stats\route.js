import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0,
  connectTimeout: 10000,
  charset: 'utf8mb4',
});

export async function GET() {
  let connection;
  
  try {
    // Get a connection from the pool
    connection = await pool.getConnection();
    
    try {
      // Get count of total heat cycles - simple count of all rows
      const [totalCountResult] = await connection.execute(`
        SELECT COUNT(*) as count 
        FROM heat_cycles
      `);
      
      const totalCount = totalCountResult[0]?.count || 0;
      
      // Get count of does currently in heat - simple count of recent records
      const [doesInHeatResult] = await connection.execute(`
        SELECT COUNT(*) as count
        FROM heat_cycles
        WHERE heat_date >= DATE_SUB(CURDATE(), INTERVAL 2 DAY)
      `);
      
      const doesInHeat = doesInHeatResult[0]?.count || 0;
      
      // Return the data
      return NextResponse.json({
        totalHeatCycles: totalCount,
        doesInHeat: doesInHeat
      });
      
    } catch (queryError) {
      console.error('Error executing database query:', queryError);
      return NextResponse.json(
        { error: 'Database query error: ' + queryError.message },
        { status: 500 }
      );
    } finally {
      // Release the connection
      if (connection) connection.release();
    }
  } catch (error) {
    console.error('Error in heat cycles stats API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch heat cycles stats: ' + error.message },
      { status: 500 }
    );
  }
}
