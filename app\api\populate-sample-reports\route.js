import { NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

export async function POST() {
  let connection

  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management',
    })

    // Clear existing sample data
    await connection.execute('DELETE FROM report_generations WHERE generated_by = "Sample"')

    // Insert comprehensive sample report generation records
    const sampleReports = [
      // Recent reports (last few days)
      {
        report_type: 'financial',
        date_range: '30',
        generated_by: 'User',
        filters: JSON.stringify({ format: 'pdf' }),
        file_name: 'Financial_Report_2024-01-15.pdf',
        file_format: 'pdf',
        generated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
      },
      {
        report_type: 'health',
        date_range: '90',
        generated_by: 'User',
        filters: JSON.stringify({ format: 'csv' }),
        file_name: 'Health_Report_2024-01-13.csv',
        file_format: 'csv',
        generated_at: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000) // 4 days ago
      },
      {
        report_type: 'all',
        date_range: '7',
        generated_by: 'User',
        filters: JSON.stringify({ format: 'view' }),
        file_name: 'Complete_Report_2024-01-14.pdf',
        file_format: 'view',
        generated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) // 3 days ago
      },
      {
        report_type: 'breeding',
        date_range: '30',
        generated_by: 'User',
        filters: JSON.stringify({ format: 'excel' }),
        file_name: 'Breeding_Report_2024-01-12.xlsx',
        file_format: 'excel',
        generated_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000) // 5 days ago
      },
      {
        report_type: 'inventory',
        date_range: '30',
        generated_by: 'User',
        filters: JSON.stringify({ format: 'csv' }),
        file_name: 'Inventory_Report_2024-01-11.csv',
        file_format: 'csv',
        generated_at: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000) // 6 days ago
      },
      // Older reports (last few weeks)
      {
        report_type: 'financial',
        date_range: '90',
        generated_by: 'User',
        filters: JSON.stringify({ format: 'print' }),
        file_name: 'Financial_Report_2024-01-08.pdf',
        file_format: 'print',
        generated_at: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000) // 9 days ago
      },
      {
        report_type: 'health',
        date_range: '30',
        generated_by: 'System',
        filters: JSON.stringify({ format: 'pdf' }),
        file_name: 'Health_Report_2024-01-05.pdf',
        file_format: 'pdf',
        generated_at: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000) // 12 days ago
      },
      {
        report_type: 'all',
        date_range: '365',
        generated_by: 'User',
        filters: JSON.stringify({ format: 'excel' }),
        file_name: 'Annual_Report_2024-01-01.xlsx',
        file_format: 'excel',
        generated_at: new Date(Date.now() - 16 * 24 * 60 * 60 * 1000) // 16 days ago
      },
      {
        report_type: 'breeding',
        date_range: '7',
        generated_by: 'User',
        filters: JSON.stringify({ format: 'view' }),
        file_name: 'Weekly_Breeding_Report_2024-01-07.pdf',
        file_format: 'view',
        generated_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000) // 10 days ago
      },
      {
        report_type: 'inventory',
        date_range: '90',
        generated_by: 'User',
        filters: JSON.stringify({ format: 'csv' }),
        file_name: 'Quarterly_Inventory_2023-12-31.csv',
        file_format: 'csv',
        generated_at: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000) // 20 days ago
      }
    ]

    // Insert all sample reports
    for (const report of sampleReports) {
      await connection.execute(`
        INSERT INTO report_generations (
          report_type, 
          date_range, 
          generated_by, 
          filters,
          file_name,
          file_format,
          generated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        report.report_type,
        report.date_range,
        report.generated_by,
        report.filters,
        report.file_name,
        report.file_format,
        report.generated_at
      ])
    }

    // Get count of inserted records
    const [countResult] = await connection.execute('SELECT COUNT(*) as count FROM report_generations')
    const totalReports = countResult[0].count

    return NextResponse.json({
      success: true,
      message: `Successfully populated ${sampleReports.length} sample reports`,
      total_reports: totalReports,
      sample_reports: sampleReports.length
    })

  } catch (error) {
    console.error('Error populating sample reports:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      message: 'Failed to populate sample reports'
    }, { status: 500 })
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}
