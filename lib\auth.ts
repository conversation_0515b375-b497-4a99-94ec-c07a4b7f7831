/**
 * Authentication utilities
 */

/**
 * Logout function that can be used throughout the application
 */
export async function logout(): Promise<void> {
  try {
    // Call logout API
    const response = await fetch('/api/auth/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Log the response for debugging
    if (response.ok) {
      console.log('Logout API call successful')
    } else {
      console.warn('Logout API call failed, but proceeding with client-side logout')
    }
  } catch (error) {
    console.error('Logout API error:', error)
    // Continue with client-side logout even if API fails
  }

  // Always perform client-side cleanup regardless of API response
  try {
    // Clear local storage
    localStorage.removeItem('user')
    localStorage.removeItem('token')
    
    // Clear session storage
    sessionStorage.clear()
    
    // Dispatch custom event to notify components of user change
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new Event('userChanged'))
    }
    
    console.log('Client-side logout completed')
  } catch (error) {
    console.error('Error during client-side logout:', error)
  }
}

/**
 * Check if user is authenticated
 */
export function isAuthenticated(): boolean {
  if (typeof window === 'undefined') {
    return false
  }

  try {
    const user = localStorage.getItem('user')
    const token = localStorage.getItem('token')
    return !!(user && token)
  } catch (error) {
    console.error('Error checking authentication status:', error)
    return false
  }
}

/**
 * Get current user information
 */
export function getCurrentUser(): { username: string; role: string; userId: number } | null {
  if (typeof window === 'undefined') {
    return null
  }

  try {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      return JSON.parse(userStr)
    }
  } catch (error) {
    console.error('Error getting current user:', error)
  }

  return null
}

/**
 * Get current user role
 */
export function getCurrentUserRole(): string | null {
  const user = getCurrentUser()
  return user?.role || null
}

/**
 * Check if current user has a specific role
 */
export function hasRole(role: string): boolean {
  const currentRole = getCurrentUserRole()
  return currentRole === role
}

/**
 * Check if current user is admin
 */
export function isAdmin(): boolean {
  return hasRole('Admin')
}

/**
 * Check if current user is manager
 */
export function isManager(): boolean {
  return hasRole('Manager')
}

/**
 * Check if current user is staff
 */
export function isStaff(): boolean {
  return hasRole('Staff')
}

/**
 * Redirect to login page
 */
export function redirectToLogin(): void {
  if (typeof window !== 'undefined') {
    window.location.href = '/login'
  }
}

/**
 * Redirect to logout page
 */
export function redirectToLogout(): void {
  if (typeof window !== 'undefined') {
    window.location.href = '/logout'
  }
}

/**
 * Perform logout and redirect
 */
export async function logoutAndRedirect(): Promise<void> {
  await logout()
  redirectToLogout()
}
