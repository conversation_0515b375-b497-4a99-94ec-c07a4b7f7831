import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

export async function POST(request: Request) {
  try {
    const { username, password } = await request.json();

    // Validate input
    if (!username || !password) {
      return NextResponse.json(
        { error: 'Username and password are required' },
        { status: 400 }
      );
    }

    console.log('Login attempt for username:', username);

    // Check if user exists in database
    const [rows] = await pool.execute(
      'SELECT id, username, password_hash, email, full_name, role, is_active FROM users WHERE username = ?',
      [username]
    );

    const users = rows as any[];

    console.log('Database query result:', JSON.stringify(users, null, 2));

    if (users.length === 0) {
      console.log('User not found:', username);
      return NextResponse.json(
        { error: 'Invalid username or password' },
        { status: 401 }
      );
    }

    const user = users[0];

    // Check if user is active
    if (!user.is_active) {
      console.log('User account is deactivated:', username);
      return NextResponse.json(
        { error: 'Account is deactivated. Please contact administrator.' },
        { status: 401 }
      );
    }

    // Verify password
    console.log('Attempting password verification for user:', username);
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    console.log('Password verification result:', isPasswordValid);

    if (!isPasswordValid) {
      console.log('Invalid password for user:', username);
      return NextResponse.json(
        { error: 'Invalid username or password' },
        { status: 401 }
      );
    }

    // Update last login
    await pool.execute(
      'UPDATE users SET last_login = NOW() WHERE id = ?',
      [user.id]
    );

    // Create JWT token
    console.log('Using JWT_SECRET for token creation:', JWT_SECRET.substring(0, 5) + '...');

    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    console.log('Generated JWT token for user:', username, 'Token starts with:', token.substring(0, 20) + '...');

    // Prepare user data (exclude password)
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.full_name,
      role: user.role
    };

    console.log('Login successful for user:', username, 'Role:', user.role);

    // Create response with token in httpOnly cookie and response body
    const response = NextResponse.json({
      success: true,
      message: 'Login successful',
      user: userData,
      token: token // Include token in response for localStorage storage
    });

    // Set httpOnly cookie with JWT token
    response.cookies.set('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60 // 24 hours
    });

    console.log('Auth cookie and token set successfully');

    return response;

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}



