import { NextResponse } from 'next/server';
import pool from '@/lib/db';

// Helper function to generate a unique inventory item ID
function generateInventoryItemId() {
  const timestamp = Date.now();
  const randomPart = Math.floor(Math.random() * 10000);
  return `INV-${timestamp}${randomPart}`;
}

// Check if inventory_items table exists, create it if it doesn't
async function ensureTableExists() {
  try {
    // Check if table exists
    const [tables] = await pool.execute(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'inventory_items'
    `);

    if (tables.length === 0) {
      console.log('Creating inventory_items table...');

      // Create the table
      await pool.execute(`
        CREATE TABLE IF NOT EXISTS inventory_items (
          id int NOT NULL AUTO_INCREMENT,
          name varchar(100) NOT NULL,
          category varchar(50) NOT NULL,
          quantity decimal(10,3) NOT NULL DEFAULT '0.000',
          unit varchar(20) DEFAULT NULL,
          min_level decimal(10,3) DEFAULT NULL,
          max_level decimal(10,3) DEFAULT NULL,
          location varchar(100) DEFAULT NULL,
          expiry_date date DEFAULT NULL,
          price_per_unit decimal(10,2) DEFAULT NULL,
          supplier varchar(100) DEFAULT NULL,
          notes text,
          created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          UNIQUE KEY name (name)
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
      `);

      console.log('inventory_items table created successfully');
    } else {
      console.log('inventory_items table already exists');
    }

    return true;
  } catch (error) {
    console.error('Error ensuring table exists:', error);
    return false;
  }
}

export async function POST(request) {
  console.log('=== INVENTORY ITEM API POST REQUEST RECEIVED ===');

  // Check if pool is initialized
  if (!pool) {
    console.error('Database pool is not initialized');
    return NextResponse.json(
      { error: 'Database connection not available' },
      { status: 500 }
    );
  }

  // Ensure the table exists
  const tableExists = await ensureTableExists();
  if (!tableExists) {
    return NextResponse.json(
      { error: 'Failed to ensure database table exists' },
      { status: 500 }
    );
  }

  try {
    const data = await request.json();
    console.log('Received inventory item data:', data);

    // Validate required fields
    if (!data.name || !data.category) {
      return NextResponse.json(
        { error: 'Missing required fields: name and category are required' },
        { status: 400 }
      );
    }

    // Process the data
    const processedData = {
      name: data.name,
      category: data.category,
      quantity: data.quantity ? parseFloat(data.quantity) : 0,
      unit: data.unit || null,
      min_level: data.minLevel ? parseFloat(data.minLevel) : null,
      max_level: data.maxLevel ? parseFloat(data.maxLevel) : null,
      location: data.location || null,
      // The date should already be in YYYY-MM-DD format from the client
      expiry_date: data.expiryDate || null,
      price_per_unit: data.price ? parseFloat(data.price) : null,
      supplier: data.supplier || null,
      notes: data.notes || null
    };

    console.log('Processed data for database insertion:', processedData);

    // Log the values being inserted, especially the date
    console.log('Expiry date value being inserted:', processedData.expiry_date);

    // Prepare the SQL parameters
    const sqlParams = [
      processedData.name,
      processedData.category,
      processedData.quantity,
      processedData.unit,
      processedData.min_level,
      processedData.max_level,
      processedData.location,
      processedData.expiry_date,
      processedData.price_per_unit,
      processedData.supplier,
      processedData.notes
    ];

    console.log('SQL parameters:', sqlParams);

    // Insert data into the inventory_items table
    const [result] = await pool.execute(
      `INSERT INTO inventory_items (
        name,
        category,
        quantity,
        unit,
        min_level,
        max_level,
        location,
        expiry_date,
        price_per_unit,
        supplier,
        notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      sqlParams
    );

    // Get the inserted ID
    const inventoryItemId = result.insertId;
    console.log('Inventory item inserted with ID:', inventoryItemId);

    return NextResponse.json({
      success: true,
      message: 'Inventory item created successfully',
      id: inventoryItemId
    });

  } catch (error) {
    console.error('Error creating inventory item:', error);

    // Check for duplicate entry error
    if (error.code === 'ER_DUP_ENTRY') {
      return NextResponse.json(
        { error: 'An item with this name already exists' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: `Failed to create inventory item: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Ensure the table exists
    await ensureTableExists();

    // Query to get all inventory items
    const [rows] = await pool.execute(`
      SELECT
        id, name, category, quantity, unit,
        min_level, max_level, location, expiry_date,
        price_per_unit, supplier, notes,
        created_at, updated_at
      FROM inventory_items
      ORDER BY created_at DESC
    `);

    return NextResponse.json(rows);
  } catch (error) {
    console.error('Error fetching inventory items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch inventory items: ' + error.message },
      { status: 500 }
    );
  }
}
