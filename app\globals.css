@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 143 30% 96%;
    --secondary-foreground: 142.1 76.2% 26.3%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 142.1 76.2% 94%;
    --accent-foreground: 142.1 76.2% 26.3%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142.1 76.2% 36.3%;
    --radius: 0.5rem;

    /* Custom color palette */
    --color-primary-50: 142 76% 97%;
    --color-primary-100: 142 76% 94%;
    --color-primary-200: 142 76% 85%;
    --color-primary-300: 142 76% 75%;
    --color-primary-400: 142 76% 55%;
    --color-primary-500: 142 76% 36.3%;
    --color-primary-600: 142 76% 30%;
    --color-primary-700: 142 76% 25%;
    --color-primary-800: 142 76% 20%;
    --color-primary-900: 142 76% 15%;

    --color-emerald-50: 152 76% 97%;
    --color-emerald-100: 152 76% 94%;
    --color-emerald-200: 152 76% 85%;
    --color-emerald-300: 152 76% 75%;
    --color-emerald-400: 152 76% 55%;
    --color-emerald-500: 152 76% 45%;
    --color-emerald-600: 152 76% 35%;
    --color-emerald-700: 152 76% 30%;
    --color-emerald-800: 152 76% 25%;
    --color-emerald-900: 152 76% 20%;

    --color-teal-50: 162 76% 97%;
    --color-teal-100: 162 76% 94%;
    --color-teal-200: 162 76% 85%;
    --color-teal-300: 162 76% 75%;
    --color-teal-400: 162 76% 55%;
    --color-teal-500: 162 76% 45%;
    --color-teal-600: 162 76% 35%;
    --color-teal-700: 162 76% 30%;
    --color-teal-800: 162 76% 25%;
    --color-teal-900: 162 76% 20%;

    --color-amber-50: 38 92% 97%;
    --color-amber-100: 38 92% 94%;
    --color-amber-200: 38 92% 85%;
    --color-amber-300: 38 92% 75%;
    --color-amber-400: 38 92% 65%;
    --color-amber-500: 38 92% 55%;
    --color-amber-600: 38 92% 45%;
    --color-amber-700: 38 92% 35%;
    --color-amber-800: 38 92% 25%;
    --color-amber-900: 38 92% 15%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Add these enhanced button styles */
  .btn-primary {
    @apply bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 shadow-md hover:shadow-lg transition-all duration-300;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-emerald-500 to-teal-500 text-white hover:from-emerald-600 hover:to-teal-600 shadow-md hover:shadow-lg transition-all duration-300;
  }

  .btn-accent {
    @apply bg-gradient-to-r from-teal-500 to-cyan-500 text-white hover:from-teal-600 hover:to-cyan-600 shadow-md hover:shadow-lg transition-all duration-300;
  }

  .btn-amber {
    @apply bg-gradient-to-r from-amber-500 to-orange-500 text-white hover:from-amber-600 hover:to-orange-600 shadow-md hover:shadow-lg transition-all duration-300;
  }

  .btn-outline-primary {
    @apply border-2 border-green-500 text-green-600 hover:bg-green-50 hover:text-green-700 transition-all duration-300;
  }

  .btn-outline-secondary {
    @apply border-2 border-emerald-500 text-emerald-600 hover:bg-emerald-50 hover:text-emerald-700 transition-all duration-300;
  }

  .btn-outline-accent {
    @apply border-2 border-teal-500 text-teal-600 hover:bg-teal-50 hover:text-teal-700 transition-all duration-300;
  }

  .btn-outline-amber {
    @apply border-2 border-amber-500 text-amber-600 hover:bg-amber-50 hover:text-amber-700 transition-all duration-300;
  }

  /* Enhanced card styles */
  .card-primary {
    @apply border-l-4 border-l-green-500 shadow-md hover:shadow-lg transition-all duration-300;
  }

  .card-secondary {
    @apply border-l-4 border-l-emerald-500 shadow-md hover:shadow-lg transition-all duration-300;
  }

  .card-accent {
    @apply border-l-4 border-l-teal-500 shadow-md hover:shadow-lg transition-all duration-300;
  }

  .card-amber {
    @apply border-l-4 border-l-amber-500 shadow-md hover:shadow-lg transition-all duration-300;
  }

  .card-gradient-primary {
    @apply bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 transition-all duration-300;
  }

  .card-gradient-secondary {
    @apply bg-gradient-to-br from-emerald-50 to-emerald-100 hover:from-emerald-100 hover:to-emerald-200 transition-all duration-300;
  }

  .card-gradient-accent {
    @apply bg-gradient-to-br from-teal-50 to-teal-100 hover:from-teal-100 hover:to-teal-200 transition-all duration-300;
  }

  .card-gradient-amber {
    @apply bg-gradient-to-br from-amber-50 to-amber-100 hover:from-amber-100 hover:to-amber-200 transition-all duration-300;
  }

  /* Enhanced tab styles */
  .tabs-gradient {
    @apply p-1 bg-gradient-to-r from-green-50 via-emerald-50 to-teal-50 rounded-xl;
  }

  .tab-primary {
    @apply data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-green-600 data-[state=active]:text-white transition-all duration-300 hover:text-green-700;
  }

  .tab-secondary {
    @apply data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white transition-all duration-300 hover:text-emerald-700;
  }

  .tab-accent {
    @apply data-[state=active]:bg-gradient-to-r data-[state=active]:from-teal-500 data-[state=active]:to-teal-600 data-[state=active]:text-white transition-all duration-300 hover:text-teal-700;
  }

  .tab-amber {
    @apply data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-500 data-[state=active]:to-amber-600 data-[state=active]:text-white transition-all duration-300 hover:text-amber-700;
  }

  /* Enhanced link styles */
  .link-primary {
    @apply text-green-600 hover:text-green-700 hover:underline transition-colors duration-300;
  }

  .link-secondary {
    @apply text-emerald-600 hover:text-emerald-700 hover:underline transition-colors duration-300;
  }

  .link-accent {
    @apply text-teal-600 hover:text-teal-700 hover:underline transition-colors duration-300;
  }

  .link-amber {
    @apply text-amber-600 hover:text-amber-700 hover:underline transition-colors duration-300;
  }

  /* Enhanced badge styles */
  .badge-primary {
    @apply bg-green-100 text-green-800 hover:bg-green-200 border-green-200;
  }

  .badge-secondary {
    @apply bg-emerald-100 text-emerald-800 hover:bg-emerald-200 border-emerald-200;
  }

  .badge-accent {
    @apply bg-teal-100 text-teal-800 hover:bg-teal-200 border-teal-200;
  }

  .badge-amber {
    @apply bg-amber-100 text-amber-800 hover:bg-amber-200 border-amber-200;
  }

  .badge-purple {
    @apply bg-purple-100 text-purple-800 hover:bg-purple-200 border-purple-200;
  }

  .badge-blue {
    @apply bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-200;
  }

  /* Enhanced status badges */
  .badge-healthy {
    @apply bg-green-100 text-green-800 hover:bg-green-200 border-green-300;
  }

  .badge-sick {
    @apply bg-red-100 text-red-800 hover:bg-red-200 border-red-300;
  }

  .badge-injured {
    @apply bg-orange-100 text-orange-800 hover:bg-orange-200 border-orange-300;
  }

  .badge-quarantined {
    @apply bg-amber-100 text-amber-800 hover:bg-amber-200 border-amber-300;
  }

  .badge-deceased {
    @apply bg-gray-100 text-gray-800 hover:bg-gray-200 border-gray-300;
  }

  /* Enhanced input styles */
  .input-primary {
    @apply border-green-200 focus-visible:ring-green-500 focus-visible:border-green-500;
  }

  .input-secondary {
    @apply border-emerald-200 focus-visible:ring-emerald-500 focus-visible:border-emerald-500;
  }

  .input-accent {
    @apply border-teal-200 focus-visible:ring-teal-500 focus-visible:border-teal-500;
  }

  /* Enhanced section styles */
  .section-primary {
    @apply border-t-4 border-t-green-500 rounded-lg overflow-hidden;
  }

  .section-secondary {
    @apply border-t-4 border-t-emerald-500 rounded-lg overflow-hidden;
  }

  .section-accent {
    @apply border-t-4 border-t-teal-500 rounded-lg overflow-hidden;
  }

  .section-amber {
    @apply border-t-4 border-t-amber-500 rounded-lg overflow-hidden;
  }

  /* Enhanced animation effects */
  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
  }

  .hover-glow-primary {
    @apply transition-all duration-300 hover:shadow-[0_0_15px_rgba(34,197,94,0.5)];
  }

  .hover-glow-secondary {
    @apply transition-all duration-300 hover:shadow-[0_0_15px_rgba(16,185,129,0.5)];
  }

  .hover-glow-accent {
    @apply transition-all duration-300 hover:shadow-[0_0_15px_rgba(20,184,166,0.5)];
  }

  /* Enhanced navigation styles */
  .nav-link-animated {
    @apply relative overflow-hidden;
  }

  .nav-link-animated::after {
    @apply absolute bottom-0 left-0 h-0.5 w-0 bg-gradient-to-r from-green-400 to-emerald-500 transition-all duration-300 content-[''];
  }

  .nav-link-animated:hover::after {
    @apply w-full;
  }

  .nav-link-animated.active::after {
    @apply w-full;
  }

  /* Enhanced dashboard card styles */
  .dashboard-card {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1 bg-gradient-to-br from-white to-secondary/30 rounded-xl border border-border/50;
  }

  .dashboard-card-primary {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1 bg-gradient-to-br from-white to-green-100/50 rounded-xl border border-green-200/50;
  }

  .dashboard-card-secondary {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1 bg-gradient-to-br from-white to-emerald-100/50 rounded-xl border border-emerald-200/50;
  }

  .dashboard-card-accent {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1 bg-gradient-to-br from-white to-teal-100/50 rounded-xl border border-teal-200/50;
  }

  .dashboard-card-amber {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1 bg-gradient-to-br from-white to-amber-100/50 rounded-xl border border-amber-200/50;
  }

  /* Enhanced stat card styles */
  .stat-card-primary {
    @apply border-l-4 border-l-green-500 bg-gradient-to-br from-white to-green-50 transition-all duration-300 hover:shadow-lg;
  }

  .stat-card-secondary {
    @apply border-l-4 border-l-emerald-500 bg-gradient-to-br from-white to-emerald-50 transition-all duration-300 hover:shadow-lg;
  }

  .stat-card-accent {
    @apply border-l-4 border-l-teal-500 bg-gradient-to-br from-white to-teal-50 transition-all duration-300 hover:shadow-lg;
  }

  .stat-card-amber {
    @apply border-l-4 border-l-amber-500 bg-gradient-to-br from-white to-amber-50 transition-all duration-300 hover:shadow-lg;
  }

  /* Enhanced text gradient styles */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-green-600 to-green-500 bg-clip-text text-transparent;
  }

  .text-gradient-secondary {
    @apply bg-gradient-to-r from-emerald-600 to-emerald-500 bg-clip-text text-transparent;
  }

  .text-gradient-accent {
    @apply bg-gradient-to-r from-teal-600 to-teal-500 bg-clip-text text-transparent;
  }

  .text-gradient-amber {
    @apply bg-gradient-to-r from-amber-600 to-amber-500 bg-clip-text text-transparent;
  }

  /* Enhanced animal profile cards */
  .goat-profile-card,
  .sheep-profile-card,
  .cattle-profile-card,
  .pig-profile-card {
    @apply relative overflow-hidden rounded-xl border bg-card text-card-foreground shadow-sm transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  .goat-profile-card .goat-image,
  .sheep-profile-card .sheep-image,
  .cattle-profile-card .cattle-image,
  .pig-profile-card .pig-image {
    @apply h-48 w-full object-cover transition-transform duration-500;
  }

  .goat-profile-card:hover .goat-image,
  .sheep-profile-card:hover .sheep-image,
  .cattle-profile-card:hover .cattle-image,
  .pig-profile-card:hover .pig-image {
    @apply scale-110;
  }

  .goat-profile-card .goat-info,
  .sheep-profile-card .sheep-info,
  .cattle-profile-card .cattle-info,
  .pig-profile-card .pig-info {
    @apply absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent p-4 text-white;
  }

  /* Enhanced search bar styles */
  .search-bar-container {
    @apply relative;
  }

  .search-input {
    @apply h-9 w-full rounded-full bg-muted px-10 text-sm focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary transition-all duration-300;
  }

  .search-input:focus {
    @apply ring-2 ring-green-500 ring-opacity-50;
  }

  .search-results {
    @apply absolute top-full mt-1 w-full z-50 max-h-[400px] overflow-auto shadow-lg border-green-100 animate-in fade-in-50 zoom-in-95 duration-100 bg-white rounded-lg;
  }

  .search-result-item {
    @apply flex items-start gap-3 p-2 rounded-md hover:bg-green-50 transition-colors cursor-pointer;
  }

  /* Enhanced progress bar styles */
  .progress-primary {
    @apply h-2 bg-green-100 rounded-full overflow-hidden;
  }

  .progress-primary .indicator {
    @apply bg-green-500 h-full rounded-full;
  }

  .progress-secondary {
    @apply h-2 bg-emerald-100 rounded-full overflow-hidden;
  }

  .progress-secondary .indicator {
    @apply bg-emerald-500 h-full rounded-full;
  }

  .progress-accent {
    @apply h-2 bg-teal-100 rounded-full overflow-hidden;
  }

  .progress-accent .indicator {
    @apply bg-teal-500 h-full rounded-full;
  }

  .progress-amber {
    @apply h-2 bg-amber-100 rounded-full overflow-hidden;
  }

  .progress-amber .indicator {
    @apply bg-amber-500 h-full rounded-full;
  }

  .progress-destructive {
    @apply h-2 bg-red-100 rounded-full overflow-hidden;
  }

  .progress-destructive .indicator {
    @apply bg-red-500 h-full rounded-full;
  }

  .progress-success {
    @apply h-2 bg-green-100 rounded-full overflow-hidden;
  }

  .progress-success .indicator {
    @apply bg-green-500 h-full rounded-full;
  }

  /* Enhanced form styles */
  .form-section-primary {
    @apply space-y-4 rounded-lg border border-green-200 p-4 bg-green-50/50;
  }

  .form-section-secondary {
    @apply space-y-4 rounded-lg border border-emerald-200 p-4 bg-emerald-50/50;
  }

  .form-section-accent {
    @apply space-y-4 rounded-lg border border-teal-200 p-4 bg-teal-50/50;
  }

  /* Enhanced table styles */
  .table-row-hover {
    @apply hover:bg-green-50 transition-colors duration-200;
  }

  .table-header-primary {
    @apply bg-green-50 text-green-700;
  }

  .table-header-secondary {
    @apply bg-emerald-50 text-emerald-700;
  }

  .table-header-accent {
    @apply bg-teal-50 text-teal-700;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

