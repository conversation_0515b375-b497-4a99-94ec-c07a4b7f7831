import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const gender = searchParams.get('gender');

    console.log('GET /api/goats - Gender filter:', gender);

    // Query to get all goats with calculated age
    let query = `
      SELECT
        id,
        name,
        breed,
        gender,
        status,
        birth_date,
        tag_number as tag,
        CASE
          WHEN birth_date IS NOT NULL THEN
            CONCAT(
              FLOOR(DATEDIFF(CURDATE(), birth_date) / 365),
              ' years, ',
              FLOOR((DATEDIFF(CURDATE(), birth_date) % 365) / 30),
              ' months'
            )
          ELSE 'Unknown'
        END as age
      FROM animals
      WHERE animal_type = 'Goat'
    `;
    let params: any[] = [];

    // Filter by gender if specified
    if (gender) {
      query += ' AND gender = ?';
      params.push(gender);
    }

    query += ' ORDER BY name ASC';

    console.log('Executing query:', query, 'with params:', params);
    const [rows] = await pool.execute(query, params);
    console.log(`Found ${(rows as any[]).length} goats matching criteria`);

    // Calculate statistics
    const goats = rows as any[];
    const stats = {
      total: goats.length,
      healthy: goats.filter(g => g.status === 'Healthy').length,
      sick: goats.filter(g => g.status === 'Sick').length,
      injured: goats.filter(g => g.status === 'Injured').length,
      quarantined: goats.filter(g => g.status === 'Quarantined').length,
      males: goats.filter(g => g.gender === 'Male').length,
      females: goats.filter(g => g.gender === 'Female').length
    };

    console.log('Calculated stats:', stats);

    // Return both goats and stats
    return NextResponse.json({
      goats: goats,
      stats: stats
    });
  } catch (error) {
    console.error('Error fetching goats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch goats' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();

    const {
      name,
      tagNumber,
      breed,
      gender,
      birthDate,
      acquisitionDate,
      status = 'Healthy',
      weight,
      color,
      markings,
      sire,
      dam,
      purchasePrice,
      isRegistered,
      registrationNumber,
      notes,
    } = body;

    const [result] = await pool.execute(
      `INSERT INTO animals (
        tag_number,
        name,
        animal_type,
        breed,
        gender,
        birth_date,
        acquisition_date,
        status,
        weight,
        color,
        markings,
        sire,
        dam,
        purchase_price,
        is_registered,
        registration_number,
        notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        tagNumber,
        name,
        'Goat',
        breed,
        gender,
        birthDate || null,
        acquisitionDate || null,
        status,
        weight || null,
        color || null,
        markings || null,
        sire || null,
        dam || null,
        purchasePrice || null,
        isRegistered ? 1 : 0,
        registrationNumber || null,
        notes || null,
      ]
    );

    return NextResponse.json({
      message: 'Goat added successfully',
      goatId: (result as any).insertId
    }, { status: 201 });

  } catch (error) {
    console.error('Error adding goat:', error);
    return NextResponse.json(
      { error: 'Failed to add goat' },
      { status: 500 }
    );
  }
}


