# Goat Farm Management System

A comprehensive Progressive Web App (PWA) for managing goat farms, built with Next.js.

## Dependencies

### Core Dependencies

- **Next.js**: React framework for building the application
- **React**: UI library
- **TypeScript**: Type-safe JavaScript
- **MySQL**: Database for storing farm data

### Security Dependencies

- **bcrypt**: Used for secure password hashing
  - Required for secure user authentication
  - Install with: `npm install bcrypt`
  - If not installed, the system will use a fallback hashing method (not recommended for production)

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   # If you encounter peer dependency issues, use:
   npm install --legacy-peer-deps
   ```

3. Install bcrypt for secure password hashing:
   ```bash
   npm install bcrypt
   npm install --save-dev @types/bcrypt
   ```

4. Set up environment variables:
   ```
   DB_HOST=localhost
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   DB_NAME=goat_management
   ```

5. Run database migrations:
   ```bash
   node scripts/run_migration.js
   ```

6. Start the development server:
   ```bash
   npm run dev
   ```

## Production Build

To build and run the production version with PWA features:

```bash
npm run build
npm start
```

**Note**: PWA features (service worker) are disabled in development mode to avoid caching issues during development. To test PWA functionality, you must build and run the production version.

## Features

### Core Features
- User management with role-based access control
- Multi-animal management (Goats, Sheep, Cattle, Pigs)
- Health record tracking
- Breeding management
- Financial transaction tracking
- Feeding management
- Inventory management
- Reporting and analytics

### Progressive Web App (PWA) Features
- **Installable**: Install on desktop and mobile devices
- **Offline Support**: Works without internet connection
- **Fast Loading**: Cached resources for instant access
- **App-like Experience**: Standalone mode with custom splash screen
- **Push Notifications Ready**: Infrastructure for future notifications

See [PWA_SETUP.md](PWA_SETUP.md) for detailed PWA documentation.

## User Management

The system uses a role-based access control system with the following roles:

- **Admin**: Full access to all features
- **Manager**: Access to most features except user management
- **Staff**: Limited access to daily operations
- **Viewer**: Read-only access

Users can be activated or deactivated rather than deleted, preserving data integrity.

## Database Schema

See [database_schema.md](docs/database_schema.md) for details on the database structure.
