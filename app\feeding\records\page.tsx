"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePicker } from "@/components/ui/date-picker"
import {
  LucideClipboardList,
  LucidePlus,
  LucideSearch,
  LucideFilter,
  LucideRefreshCw,
  LucideX,
  LucideWheat,
  LucideCalendarClock,
  LucideEye,
} from "lucide-react"


// Sample feeding records data
const feedingRecords = [
  {
    id: "FR-001",
    date: "2024-06-01",
    time: "07:15",
    goatGroups: ["All Goats"],
    feedItems: [
      { name: "Alfalfa Hay", amount: "2.5", unit: "kg" },
      { name: "Grain Mix", amount: "0.5", unit: "kg" },
    ],
    scheduleName: "Morning Feed - All Goats",
    recordedBy: "John Doe",
    notes: "All goats ate well",
  },
  {
    id: "FR-002",
    date: "2024-06-01",
    time: "17:05",
    goatGroups: ["All Goats"],
    feedItems: [
      { name: "Alfalfa Hay", amount: "2.5", unit: "kg" },
      { name: "Grain Mix", amount: "0.5", unit: "kg" },
    ],
    scheduleName: "Evening Feed - All Goats",
    recordedBy: "Jane Smith",
    notes: "Increased hay slightly due to colder weather",
  },
  {
    id: "FR-003",
    date: "2024-05-31",
    time: "07:10",
    goatGroups: ["All Goats"],
    feedItems: [
      { name: "Alfalfa Hay", amount: "2.5", unit: "kg" },
      { name: "Grain Mix", amount: "0.5", unit: "kg" },
    ],
    scheduleName: "Morning Feed - All Goats",
    recordedBy: "John Doe",
    notes: "",
  },
  {
    id: "FR-004",
    date: "2024-05-31",
    time: "12:00",
    goatGroups: ["Pregnant Does"],
    feedItems: [
      { name: "Protein Supplement", amount: "0.25", unit: "kg" },
      { name: "Alfalfa Pellets", amount: "0.5", unit: "kg" },
    ],
    scheduleName: "Pregnant Does - Supplement",
    recordedBy: "Jane Smith",
    notes: "One doe didn't eat much, monitoring",
  },
  {
    id: "FR-005",
    date: "2024-05-31",
    time: "17:00",
    goatGroups: ["All Goats"],
    feedItems: [
      { name: "Alfalfa Hay", amount: "2.5", unit: "kg" },
      { name: "Grain Mix", amount: "0.5", unit: "kg" },
    ],
    scheduleName: "Evening Feed - All Goats",
    recordedBy: "John Doe",
    notes: "",
  },
  {
    id: "FR-006",
    date: "2024-05-30",
    time: "07:05",
    goatGroups: ["All Goats"],
    feedItems: [
      { name: "Alfalfa Hay", amount: "2.5", unit: "kg" },
      { name: "Grain Mix", amount: "0.5", unit: "kg" },
    ],
    scheduleName: "Morning Feed - All Goats",
    recordedBy: "Jane Smith",
    notes: "",
  },
  {
    id: "FR-007",
    date: "2024-05-30",
    time: "12:00",
    goatGroups: ["Lactating Does"],
    feedItems: [
      { name: "Alfalfa Hay", amount: "1", unit: "kg" },
      { name: "Grain Mix", amount: "0.75", unit: "kg" },
    ],
    scheduleName: "Lactating Does - Extra Feed",
    recordedBy: "John Doe",
    notes: "All does eating well, good milk production",
  },
  {
    id: "FR-008",
    date: "2024-05-30",
    time: "17:10",
    goatGroups: ["All Goats"],
    feedItems: [
      { name: "Alfalfa Hay", amount: "2.5", unit: "kg" },
      { name: "Grain Mix", amount: "0.5", unit: "kg" },
    ],
    scheduleName: "Evening Feed - All Goats",
    recordedBy: "Jane Smith",
    notes: "",
  },
]

// Sample goat groups
const goatGroups = [
  { id: "GG-001", name: "All Goats", count: 10 },
  { id: "GG-002", name: "Pregnant Does", count: 2 },
  { id: "GG-003", name: "Lactating Does", count: 3 },
  { id: "GG-004", name: "Kids", count: 4 },
  { id: "GG-005", name: "Bucks", count: 1 },
]

export default function FeedingRecordsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [dateRange, setDateRange] = useState({ from: null, to: null })
  const [groupFilter, setGroupFilter] = useState("all")

  // Filter records based on search, date range, and group
  const filteredRecords = feedingRecords
    .filter((record) => {
      // Search filter
      const matchesSearch =
        searchTerm === "" ||
        record.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.scheduleName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.recordedBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.notes.toLowerCase().includes(searchTerm.toLowerCase())

      // Date range filter
      const recordDate = new Date(record.date)
      const matchesDateFrom = !dateRange.from || recordDate >= dateRange.from
      const matchesDateTo = !dateRange.to || recordDate <= dateRange.to

      // Group filter
      const matchesGroup = groupFilter === "all" || record.goatGroups.some((group) => group === groupFilter)

      return matchesSearch && matchesDateFrom && matchesDateTo && matchesGroup
    })
    .sort((a, b) => {
      // Sort by date and time (newest first)
      const dateA = new Date(`${a.date}T${a.time}`)
      const dateB = new Date(`${b.date}T${b.time}`)
      return dateB - dateA
    })

  // Reset filters
  const resetFilters = () => {
    setSearchTerm("")
    setDateRange({ from: null, to: null })
    setGroupFilter("all")
  }

  // Format date for display
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString()
  }

  // Format time for display
  const formatTime = (timeString) => {
    return timeString
  }

  // Calculate feed consumption statistics
  const calculateFeedConsumption = () => {
    const consumption = {}

    feedingRecords.forEach((record) => {
      record.feedItems.forEach((item) => {
        const key = item.name
        if (!consumption[key]) {
          consumption[key] = {
            total: 0,
            unit: item.unit,
          }
        }

        // Only add if amount is a number
        if (!isNaN(Number.parseFloat(item.amount))) {
          consumption[key].total += Number.parseFloat(item.amount)
        }
      })
    })

    return consumption
  }

  const feedConsumption = calculateFeedConsumption()

  return (
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <Link href="/feeding">
              <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-4 w-4"
                >
                  <path d="m12 19-7-7 7-7" />
                  <path d="M19 12H5" />
                </svg>
                <span className="sr-only">Back</span>
              </Button>
            </Link>
            <h1 className="text-3xl font-bold tracking-tight text-gradient-secondary">Feeding Records</h1>
          </div>
          <Link href="/feeding/record">
            <Button className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
              <LucidePlus className="mr-2 h-4 w-4" />
              Record Feeding
            </Button>
          </Link>
        </div>



        {/* Summary Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Total Records</div>
                  <div className="text-2xl font-bold text-amber-600">{feedingRecords.length}</div>
                </div>
                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                  <LucideClipboardList className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Today's Feedings</div>
                  <div className="text-2xl font-bold text-amber-600">
                    {feedingRecords.filter((r) => r.date === new Date().toISOString().split("T")[0]).length}
                  </div>
                </div>
                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                  <LucideCalendarClock className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Hay Used (Total)</div>
                  <div className="text-2xl font-bold text-amber-600">
                    {feedConsumption["Alfalfa Hay"]?.total || 0} kg
                  </div>
                </div>
                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                  <LucideWheat className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Grain Used (Total)</div>
                  <div className="text-2xl font-bold text-amber-600">{feedConsumption["Grain Mix"]?.total || 0} kg</div>
                </div>
                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                  <LucideWheat className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="overflow-hidden">
          <CardHeader className="p-4 pb-0">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
              <CardTitle>Feeding Records</CardTitle>
              <div className="flex items-center gap-2">
                <LucideFilter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Filter records</span>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="flex flex-col gap-4">
              <div className="relative">
                <LucideSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by ID, schedule name, or notes..."
                  className="pl-10 pr-10 input-primary"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                {searchTerm && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-amber-50 hover:text-amber-700"
                    onClick={() => setSearchTerm("")}
                  >
                    <LucideX className="h-4 w-4" />
                  </Button>
                )}
              </div>

              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Date Range</label>
                  <div className="flex items-center gap-2">
                    <DatePicker
                      selected={dateRange.from}
                      onSelect={(date) => setDateRange((prev) => ({ ...prev, from: date }))}
                      placeholder="From date"
                    />
                    <span className="text-muted-foreground">to</span>
                    <DatePicker
                      selected={dateRange.to}
                      onSelect={(date) => setDateRange((prev) => ({ ...prev, to: date }))}
                      placeholder="To date"
                    />
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Goat Group</label>
                  <Select value={groupFilter} onValueChange={setGroupFilter}>
                    <SelectTrigger className="mt-2">
                      <SelectValue placeholder="Select goat group" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Groups</SelectItem>
                      {goatGroups.map((group) => (
                        <SelectItem key={group.id} value={group.name}>
                          {group.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end">
                  <Button
                    variant="outline"
                    className="w-full flex items-center gap-2 border-amber-500 text-amber-600 hover:bg-amber-50 hover:text-amber-700"
                    onClick={resetFilters}
                    disabled={!searchTerm && !dateRange.from && !dateRange.to && groupFilter === "all"}
                  >
                    <LucideRefreshCw className="h-4 w-4" />
                    Reset Filters
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Feeding Records Table */}
        <Card>
          <CardContent className="p-0 overflow-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Date & Time</TableHead>
                  <TableHead>Schedule</TableHead>
                  <TableHead>Goat Groups</TableHead>
                  <TableHead>Feed Items</TableHead>
                  <TableHead>Recorded By</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRecords.length > 0 ? (
                  filteredRecords.map((record) => (
                    <TableRow key={record.id} className="hover-lift">
                      <TableCell className="font-medium">{record.id}</TableCell>
                      <TableCell>
                        <div>{formatDate(record.date)}</div>
                        <div className="text-xs text-muted-foreground">{formatTime(record.time)}</div>
                      </TableCell>
                      <TableCell>{record.scheduleName}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {record.goatGroups.map((group, index) => (
                            <Badge key={index} variant="outline" className="badge-amber">
                              {group}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {record.feedItems.map((item, index) => (
                            <div key={index} className="flex items-center gap-1">
                              <LucideWheat className="h-3 w-3 text-amber-500" />
                              <span>
                                {item.name}: {item.amount} {item.unit}
                              </span>
                            </div>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>{record.recordedBy}</TableCell>
                      <TableCell className="text-right">
                        <Link href={`/feeding/records/${record.id}`}>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <LucideEye className="h-4 w-4" />
                            <span className="sr-only">View</span>
                          </Button>
                        </Link>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6">
                      <div className="flex flex-col items-center justify-center p-8">
                        <div className="rounded-full bg-muted p-3 mb-4">
                          <LucideSearch className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <h3 className="text-lg font-medium mb-1">No feeding records found</h3>
                        <p className="text-muted-foreground mb-4">Try adjusting your search or filters</p>
                        <Button variant="outline" onClick={resetFilters}>
                          Reset Filters
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
          <CardFooter className="flex justify-between py-4">
            <div className="text-sm text-muted-foreground">
              Showing {filteredRecords.length} of {feedingRecords.length} records
            </div>
            <Link href="/feeding/record">
              <Button className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white">
                <LucidePlus className="mr-2 h-4 w-4" />
                Record Feeding
              </Button>
            </Link>
          </CardFooter>
        </Card>

        {/* Feed Consumption Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Feed Consumption Summary</CardTitle>
            <CardDescription>Total feed used across all feeding records</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Object.entries(feedConsumption).map(([feedName, data]) => (
                <Card key={feedName} className="bg-amber-50/50 border-amber-200">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                        <LucideWheat className="h-5 w-5 text-amber-600" />
                      </div>
                      <div>
                        <div className="text-sm font-medium">{feedName}</div>
                        <div className="text-xl font-bold text-amber-700">
                          {data.total.toFixed(1)} {data.unit}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
  )
}

