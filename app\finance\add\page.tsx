"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { LoadingButton } from "@/components/ui/loading-button"
import {
  LucideArrowUpRight,
  LucideArrowDownRight,
  LucideSave,
  LucideCoins,
  LucideCalendarClock,
  LucideUpload,
} from "lucide-react"

import { toast } from "@/components/ui/use-toast"
import { BackButton } from "@/components/ui/back-button"

// Sample data for categories
const incomeCategories = [
  { id: "income-001", name: "Goat Sales" },
  { id: "income-002", name: "Milk Sales" },
  { id: "income-003", name: "Breeding Fees" },
  { id: "income-004", name: "Cheese Sales" },
  { id: "income-005", name: "Manure Sales" },
  { id: "income-006", name: "Other Income" },
]

const expenseCategories = [
  { id: "expense-001", name: "Feed" },
  { id: "expense-002", name: "Veterinary" },
  { id: "expense-003", name: "Equipment" },
  { id: "expense-004", name: "Supplies" },
  { id: "expense-005", name: "Labor" },
  { id: "expense-006", name: "Utilities" },
  { id: "expense-007", name: "Maintenance" },
  { id: "expense-008", name: "Transportation" },
  { id: "expense-009", name: "Marketing" },
  { id: "expense-010", name: "Other Expenses" },
]

const paymentMethods = [
  { id: "payment-001", name: "Cash" },
  { id: "payment-002", name: "Credit Card" },
  { id: "payment-003", name: "Debit Card" },
  { id: "payment-004", name: "Bank Transfer" },
  { id: "payment-005", name: "Check" },
  { id: "payment-006", name: "Mobile Payment" },
  { id: "payment-007", name: "Other" },
]

// Sample goats for association
const goats = [
  { id: "G-2023-01", name: "Bella", breed: "Boer", gender: "Female" },
  { id: "G-2023-02", name: "Max", breed: "Alpine", gender: "Male" },
  { id: "G-2023-03", name: "Luna", breed: "Nubian", gender: "Female" },
  { id: "G-2023-04", name: "Rocky", breed: "Boer", gender: "Male" },
  { id: "G-2023-05", name: "Daisy", breed: "Saanen", gender: "Female" },
]

export default function AddTransactionPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedGoats, setSelectedGoats] = useState<string[]>([])
  const [goats, setGoats] = useState([])
  const [isLoadingGoats, setIsLoadingGoats] = useState(true)
  const [formData, setFormData] = useState({
    transactionType: "income", // This becomes transaction_type in API
    amount: "",
    date: new Date().toISOString().split("T")[0], // This becomes transaction_date in API
    description: "",
    category: "",
    paymentMethod: "", // This should match what you're sending to API (payment_method)
    reference: "", // This becomes reference_number in API
    notes: "",
    isRecurring: false, // This becomes is_recurring in API
    attachReceipt: false, // This becomes receipt_path in API
  })

  // Add this debugging function to your component
  const debugFormFields = () => {
    console.log('Current form field names:');
    Object.keys(formData).forEach(key => {
      console.log(`- ${key}: ${formData[key]}`);
    });
  }

  // Fetch goats from the database
  useEffect(() => {
    async function fetchGoats() {
      try {
        setIsLoadingGoats(true)
        const response = await fetch('/api/goats')
        if (response.ok) {
          const data = await response.json()
          setGoats(data)
        } else {
          toast({
            title: "Error",
            description: "Failed to fetch goats. Please refresh the page.",
            variant: "destructive",
          })
        }
      } catch (error) {
        console.error('Error fetching goats:', error)
        toast({
          title: "Error",
          description: "Failed to fetch goats. Please refresh the page.",
          variant: "destructive",
        })
      } finally {
        setIsLoadingGoats(false)
      }
    }

    fetchGoats()
  }, [])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))
  }

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleGoatSelection = (goatId: string) => {
    setSelectedGoats((prev) => {
      if (prev.includes(goatId)) {
        return prev.filter((id) => id !== goatId)
      } else {
        return [...prev, goatId]
      }
    })
  }

  // Add this function to debug form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    console.log('=== FORM SUBMISSION STARTED ===');

    try {
      // Debug form data before processing
      console.log('Raw form data:', formData);

      // Check for field name consistency
      const formFieldMap = {
        'transactionType': 'transaction_type',
        'amount': 'amount',
        'date': 'transaction_date',
        'description': 'description',
        'category': 'category',
        'paymentMethod': 'payment_method',
        'reference': 'reference_number',
        'notes': 'notes',
        'isRecurring': 'is_recurring',
        'attachReceipt': 'receipt_path'
      };

      console.log('Form field mapping check:');
      Object.entries(formFieldMap).forEach(([formField, apiField]) => {
        console.log(`Form field '${formField}' (${formData[formField]}) → API field '${apiField}'`);
      });

      // Ensure date is in YYYY-MM-DD format regardless of browser locale
      let formattedDate;
      try {
        // Create a new Date object and extract the date part in YYYY-MM-DD format
        const dateObj = new Date(formData.date);
        formattedDate = dateObj.toISOString().split('T')[0];
        console.log(`Date conversion: ${formData.date} → ${formattedDate}`);
      } catch (dateError) {
        console.error('Date conversion error:', dateError);
        formattedDate = formData.date; // Fallback to original
      }

      // Prepare the data to be sent to the API
      const transactionData = {
        // Ensure transaction_type is lowercase
        transaction_type: formData.transactionType.toLowerCase(),
        amount: parseFloat(formData.amount),
        transaction_date: formattedDate,
        description: formData.description,
        category: formData.category || "Uncategorized",
        payment_method: formData.paymentMethod || "Other",
        reference_number: formData.reference || null,
        notes: formData.notes || null,
        is_recurring: formData.isRecurring ? 1 : 0,
        receipt_path: formData.attachReceipt ? 'pending_upload' : null
      };

      // Add debug logging to verify the value
      console.log(`Transaction type before sending: "${formData.transactionType}" → "${transactionData.transaction_type}"`);

      // Check for data type issues
      const dataTypeChecks = {
        'transaction_type': typeof transactionData.transaction_type === 'string',
        'amount': !isNaN(transactionData.amount),
        'transaction_date': /^\d{4}-\d{2}-\d{2}$/.test(transactionData.transaction_date),
        'description': typeof transactionData.description === 'string'
      };

      const dataTypeIssues = Object.entries(dataTypeChecks)
        .filter(([field, isValid]) => !isValid)
        .map(([field]) => field);

      if (dataTypeIssues.length > 0) {
        console.error('Data type issues detected:', dataTypeIssues);
        dataTypeIssues.forEach(field => {
          console.error(`Field '${field}' has invalid data type: ${JSON.stringify(transactionData[field])}`);
        });
      }

      console.log('Processed transaction data to send:', JSON.stringify(transactionData, null, 2));

      // Send the data to the API
      console.log('Sending request to /api/finance...');
      const response = await fetch('/api/finance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(transactionData),
        cache: 'no-store',
      });

      console.log('Response status:', response.status);
      const responseData = await response.json();
      console.log('API response:', responseData);

      if (!response.ok) {
        // Log more details about the validation error
        console.error('API error response:', responseData);

        // Create a more informative error message
        let errorMessage = responseData.error || `Failed with status ${response.status}`;

        // If there are validation issues, include them in the error message
        if (responseData.issues && Array.isArray(responseData.issues)) {
          errorMessage += ': ' + responseData.issues.join(', ');
        }

        // Display toast with detailed error
        toast({
          title: "Error",
          description: `Failed to record transaction: ${errorMessage}`,
          variant: "destructive",
        });

        // Don't throw error, just return to prevent unhandled rejection
        return;
      }

      // Success handling
      toast({
        title: "Success",
        description: "Financial transaction recorded successfully!",
      });

      // Add a small delay before redirecting to ensure the toast is seen
      setTimeout(() => {
        router.push('/finance');
      }, 1000);

    } catch (error) {
      console.error('Error submitting form:', error);
      toast({
        title: "Error",
        description: `Failed to record transaction: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
      console.log('=== FORM SUBMISSION ENDED ===');
    }
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-3xl font-bold tracking-tight text-gradient-amber">Add Transaction</h1>
        <BackButton href="/finance" label="Back to Finance" />
      </div>

      {/* Form */}
      <Card className="border-t-4 border-t-cyan-500">
        <CardHeader>
          <div className="flex items-center gap-2">
            <LucideCoins className="h-6 w-6 text-cyan-500" />
            <CardTitle>New Financial Transaction</CardTitle>
          </div>
          <CardDescription>Record income or expenses for your farm</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            {/* Transaction Type */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-cyan-700">Transaction Type</h3>
                <RadioGroup
                  value={formData.transactionType}
                  onValueChange={(value) => handleSelectChange("transactionType", value)}
                  className="flex flex-col sm:flex-row gap-4"
                >
                  <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-green-50 cursor-pointer transition-colors duration-200 border-green-200 data-[state=checked]:bg-green-50 data-[state=checked]:border-green-500">
                    <RadioGroupItem value="income" id="income" className="text-green-600" />
                    <Label htmlFor="income" className="flex items-center cursor-pointer">
                      <LucideArrowUpRight className="h-5 w-5 text-green-600 mr-2" />
                      <div>
                        <div className="font-medium">Income</div>
                        <div className="text-sm text-muted-foreground">Money coming in</div>
                      </div>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-red-50 cursor-pointer transition-colors duration-200 border-red-200 data-[state=checked]:bg-red-50 data-[state=checked]:border-red-500">
                    <RadioGroupItem value="expense" id="expense" className="text-red-600" />
                    <Label htmlFor="expense" className="flex items-center cursor-pointer">
                      <LucideArrowDownRight className="h-5 w-5 text-red-600 mr-2" />
                      <div>
                        <div className="font-medium">Expense</div>
                        <div className="text-sm text-muted-foreground">Money going out</div>
                      </div>
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Transaction Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-cyan-700">Transaction Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">
                      Amount <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">MWK</span>
                      <Input
                        id="amount"
                        name="amount"
                        type="number"
                        min="0.01"
                        step="0.01"
                        placeholder="0.00"
                        value={formData.amount}
                        onChange={handleChange}
                        required
                        className="input-accent pl-12"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="date">
                      Date <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="date"
                        name="date"
                        type="date"
                        value={formData.date}
                        onChange={handleChange}
                        required
                        className="input-accent pl-10"
                      />
                      <LucideCalendarClock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-cyan-500" />
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">
                    Description <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="description"
                    name="description"
                    placeholder="Brief description of the transaction"
                    value={formData.description}
                    onChange={handleChange}
                    required
                    className="input-accent"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">
                      Category <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => handleSelectChange("category", value)}
                      required
                    >
                      <SelectTrigger id="category" className="input-accent">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {formData.transactionType === "income"
                          ? incomeCategories.map((category) => (
                              <SelectItem key={category.id} value={category.name}>
                                {category.name}
                              </SelectItem>
                            ))
                          : expenseCategories.map((category) => (
                              <SelectItem key={category.id} value={category.name}>
                                {category.name}
                              </SelectItem>
                            ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="paymentMethod">
                      Payment Method <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.paymentMethod}
                      onValueChange={(value) => handleSelectChange("paymentMethod", value)}
                      required
                    >
                      <SelectTrigger id="paymentMethod" className="input-accent">
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        {paymentMethods.map((method) => (
                          <SelectItem key={method.id} value={method.name}>
                            {method.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reference">Reference/Invoice Number</Label>
                  <Input
                    id="reference"
                    name="reference"
                    placeholder="Optional reference or invoice number"
                    value={formData.reference}
                    onChange={handleChange}
                    className="input-accent"
                  />
                </div>
              </div>

              {/* Associated Goats (Optional) */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-cyan-700">Associated Goats</h3>
                  <span className="text-sm text-muted-foreground">(Optional)</span>
                </div>
                <div className="border rounded-md p-4 max-h-60 overflow-y-auto bg-cyan-50/30">
                  {isLoadingGoats ? (
                    <div className="flex justify-center items-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-cyan-700"></div>
                      <span className="ml-2 text-sm text-cyan-700">Loading goats...</span>
                    </div>
                  ) : goats.length > 0 ? (
                    <div className="space-y-2">
                      {goats.map((goat) => (
                        <div key={goat.id} className="flex items-center space-x-2 p-2 hover:bg-cyan-100/50 rounded-md">
                          <Checkbox
                            id={`goat-${goat.id}`}
                            checked={selectedGoats.includes(goat.id.toString())}
                            onCheckedChange={() => handleGoatSelection(goat.id.toString())}
                          />
                          <Label htmlFor={`goat-${goat.id}`} className="flex-1 cursor-pointer">
                            <div className="font-medium">{goat.name}</div>
                            <div className="text-xs text-muted-foreground">
                              {goat.breed}, {goat.gender}, Tag: {goat.tag_number || 'N/A'}
                            </div>
                          </Label>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      <p>No goats found. Please add goats first.</p>
                      <Button
                        variant="link"
                        className="text-cyan-600 mt-2"
                        onClick={() => router.push('/goats/add')}
                      >
                        Add a goat
                      </Button>
                    </div>
                  )}
                </div>
                <div className="text-xs text-muted-foreground">
                  {!isLoadingGoats && goats.length > 0 &&
                    `${selectedGoats.length} of ${goats.length} goats selected`
                  }
                </div>
              </div>

              {/* Additional Options */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-cyan-700">Additional Options</h3>
                <div className="flex flex-col gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="attachReceipt"
                      name="attachReceipt"
                      checked={formData.attachReceipt}
                      onCheckedChange={(checked) =>
                        setFormData((prev) => ({ ...prev, attachReceipt: checked === true }))
                      }
                    />
                    <div className="grid gap-1.5 leading-none">
                      <Label htmlFor="attachReceipt" className="cursor-pointer">
                        Attach Receipt
                      </Label>
                      {formData.attachReceipt && (
                        <div className="mt-2">
                          <div className="flex items-center justify-center w-full">
                            <label
                              htmlFor="receipt-upload"
                              className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-cyan-50 border-cyan-300 hover:bg-cyan-100"
                            >
                              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                <LucideUpload className="w-8 h-8 mb-3 text-cyan-500" />
                                <p className="mb-2 text-sm text-cyan-700">
                                  <span className="font-semibold">Click to upload</span> or drag and drop
                                </p>
                                <p className="text-xs text-cyan-600">PNG, JPG or PDF (max. 5MB)</p>
                              </div>
                              <input id="receipt-upload" type="file" className="hidden" />
                            </label>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isRecurring"
                      name="isRecurring"
                      checked={formData.isRecurring}
                      onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, isRecurring: checked === true }))}
                    />
                    <div className="grid gap-1.5 leading-none">
                      <Label htmlFor="isRecurring" className="cursor-pointer">
                        Recurring Transaction
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        Mark this transaction as recurring for future reference
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Notes */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-cyan-700">Notes</h3>
                <div className="space-y-2">
                  <Label htmlFor="notes">Additional Notes</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    placeholder="Enter any additional notes about this transaction"
                    value={formData.notes}
                    onChange={handleChange}
                    className="input-accent min-h-[100px]"
                  />
                </div>
              </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.push("/finance")}>
              Cancel
            </Button>
            <LoadingButton
              type="submit"
              isLoading={isSubmitting}
              loadingText="Saving..."
              className={
                formData.transactionType === "income"
                  ? "bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
                  : "bg-gradient-to-r from-cyan-500 to-sky-500 hover:from-cyan-600 hover:to-sky-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
              }
            >
              <LucideSave className="mr-2 h-4 w-4" />
              Save Transaction
            </LoadingButton>
          </CardFooter>
        </form>
      </Card>

      <Button
        type="button"
        variant="outline"
        onClick={debugFormFields}
        className="mt-2"
      >
        Debug Form Fields
      </Button>
    </div>
  )
}




