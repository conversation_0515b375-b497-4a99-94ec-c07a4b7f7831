import { NextRequest, NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
}


export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const dateRange = searchParams.get('dateRange') || '30'
  const reportType = searchParams.get('reportType') || 'all'
  const startDateParam = searchParams.get('startDate')
  const endDateParam = searchParams.get('endDate')

  let connection

  try {
    connection = await mysql.createConnection(dbConfig)

    const response = {
      summary: {
        totalRevenue: 0,
        totalExpenses: 0,
        netProfit: 0,
        profitMargin: 0,
        healthIssues: 0,
        birthRate: 0,
        inventoryValue: 0,
        feedCosts: 0,
      },
      financial: { monthlyData: [], expenseBreakdown: [], revenueSources: [] },
      health: { monthlyData: [], issuesBreakdown: [] },
      breeding: { monthlyData: [], breedingByBreed: [], kidsPerBirth: [] },
      animals: { breedDistribution: [], genderDistribution: [], typeDistribution: [] },
      inventory: {}
    }

    // Calculate date range
    let startDate: Date
    let endDate: Date

    if (dateRange === 'custom' && startDateParam && endDateParam) {
      // Use custom date range
      startDate = new Date(startDateParam)
      endDate = new Date(endDateParam)
    } else {
      // Use predefined period
      endDate = new Date()
      startDate = new Date()
      startDate.setDate(endDate.getDate() - parseInt(dateRange))
    }

    const startDateStr = startDate.toISOString().split('T')[0]
    const endDateStr = endDate.toISOString().split('T')[0]

    // Get summary data with error handling
    try {
      const [summaryResult] = await connection.execute(`
        SELECT
          COALESCE(SUM(CASE WHEN type = 'Income' THEN amount ELSE 0 END), 0) as totalRevenue,
          COALESCE(SUM(CASE WHEN type = 'Expense' THEN amount ELSE 0 END), 0) as totalExpenses
        FROM transactions
        WHERE DATE(date) BETWEEN ? AND ?
      `, [startDateStr, endDateStr])

      const summary = (summaryResult as any[])[0] || {}
      response.summary.totalRevenue = summary.totalRevenue || 0
      response.summary.totalExpenses = summary.totalExpenses || 0
      response.summary.netProfit = response.summary.totalRevenue - response.summary.totalExpenses
      response.summary.profitMargin = response.summary.totalRevenue ?
        ((response.summary.totalRevenue - response.summary.totalExpenses) / response.summary.totalRevenue * 100) : 0
    } catch (error) {
      console.log('Summary query failed, using defaults:', error)
    }

    // Get financial data
    if (reportType === 'financial' || reportType === 'all') {
      try {
        const [monthlyData] = await connection.execute(`
          SELECT
            DATE_FORMAT(date, '%Y-%m') as month,
            SUM(CASE WHEN type = 'Income' THEN amount ELSE 0 END) as revenue,
            SUM(CASE WHEN type = 'Expense' THEN amount ELSE 0 END) as expenses,
            SUM(CASE WHEN type = 'Income' THEN amount ELSE -amount END) as profit
          FROM transactions
          WHERE DATE(date) BETWEEN ? AND ?
          GROUP BY DATE_FORMAT(date, '%Y-%m')
          ORDER BY month
        `, [startDateStr, endDateStr])

        response.financial.monthlyData = monthlyData as any[]
      } catch (error) {
        console.log('Financial query failed:', error)
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching dashboard reports:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dashboard reports' },
      { status: 500 }
    )
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}