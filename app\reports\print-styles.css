/* Print styles for reports */
@media print {
  /* Hide everything by default */
  body * {
    visibility: hidden;
  }
  
  /* Show only the report content */
  #report-content,
  #report-content * {
    visibility: visible;
  }
  
  /* Position the report content at the top of the page */
  #report-content {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 20px;
  }
  
  /* Hide elements with no-print class */
  .no-print {
    display: none !important;
  }
  
  /* Force page breaks */
  .print-break-after {
    page-break-after: always;
  }
  
  /* Ensure full width for elements */
  .print-full-width {
    width: 100% !important;
  }
  
  /* Adjust card styles for printing */
  .card {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
    break-inside: avoid;
  }
  
  /* Ensure charts are visible */
  canvas {
    max-width: 100%;
    height: auto !important;
  }
  
  /* Adjust grid layouts for printing */
  .grid {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  /* Ensure text is readable */
  p, h1, h2, h3, h4, h5, h6, span, div {
    color: black !important;
  }
  
  /* Hide navigation and UI elements */
  nav, header, footer, button, .tabs-list {
    display: none !important;
  }
  
  /* Show tabs content */
  .tabs-content {
    display: block !important;
    visibility: visible !important;
  }
  
  /* Add page numbers */
  @page {
    margin: 1cm;
  }
  
  @page :first {
    margin-top: 2cm;
  }
  
  /* Ensure background colors print */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
}
