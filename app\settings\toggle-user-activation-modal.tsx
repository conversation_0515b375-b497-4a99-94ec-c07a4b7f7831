'use client'

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTit<PERSON> } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { LoadingButton } from "@/components/ui/loading-button"
import { useToast } from "@/components/ui/use-toast"
import { LucideAlertCircle, LucideAlertTriangle, LucideUserCheck, LucideUserX } from "lucide-react"

interface ToggleUserActivationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  userId: number | null;
  username: string;
  isCurrentlyActive: boolean;
}

export function ToggleUserActivationModal({
  isOpen,
  onClose,
  onSuccess,
  userId,
  username,
  isCurrentlyActive
}: ToggleUserActivationModalProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Handle toggle user activation
  const handleToggleActivation = async () => {
    if (!userId) return

    setIsLoading(true)
    setError(null)

    try {
      console.log(`Attempting to ${isCurrentlyActive ? 'deactivate' : 'activate'} user with ID: ${userId}`)

      // Call API to toggle user activation
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_active: !isCurrentlyActive }),
      })

      console.log('API response status:', response.status)

      // Get the response text first for debugging
      const responseText = await response.text()
      console.log('API response text:', responseText)

      if (!response.ok) {
        // Try to parse the error as JSON
        let errorMessage = `Failed to ${isCurrentlyActive ? 'deactivate' : 'activate'} user`
        try {
          const errorData = JSON.parse(responseText)
          if (errorData.error) {
            errorMessage = errorData.error
          }
        } catch (e) {
          // If parsing fails, use the response text
          if (responseText) {
            errorMessage = `${errorMessage}: ${responseText}`
          }
        }
        throw new Error(errorMessage)
      }

      // Parse the response text as JSON
      let data
      try {
        data = JSON.parse(responseText)
      } catch (e) {
        console.error('Error parsing response JSON:', e)
        data = { message: `User has been ${isCurrentlyActive ? 'deactivated' : 'activated'} successfully` }
      }

      // Show success message
      toast({
        title: "Success",
        description: data.message || `User has been ${isCurrentlyActive ? 'deactivated' : 'activated'} successfully`,
        variant: "default",
      })

      // Close modal and refresh user list
      onSuccess()
      onClose()

    } catch (err: any) {
      console.error('Error toggling user activation:', err)

      // Create a detailed error message
      const errorMessage = err.message || 'Unknown error occurred'
      console.error('Error details:', errorMessage)

      setError(errorMessage)

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className={`flex items-center gap-2 ${isCurrentlyActive ? 'text-amber-600' : 'text-green-600'}`}>
            {isCurrentlyActive ? (
              <LucideUserX className="h-5 w-5" />
            ) : (
              <LucideUserCheck className="h-5 w-5" />
            )}
            {isCurrentlyActive ? 'Deactivate User' : 'Activate User'}
          </DialogTitle>
          <DialogDescription>
            {isCurrentlyActive
              ? 'Deactivating a user will prevent them from logging in to the system.'
              : 'Activating a user will allow them to log in to the system.'}
          </DialogDescription>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-red-800 mb-4">
            <div className="flex items-center gap-2">
              <LucideAlertCircle className="h-4 w-4" />
              <span className="font-medium">Error</span>
            </div>
            <p className="text-sm mt-1">{error}</p>
          </div>
        )}

        <div className="py-4">
          <p className="mb-4">
            Are you sure you want to {isCurrentlyActive ? 'deactivate' : 'activate'} the user <span className="font-semibold">{username}</span>?
          </p>

          <div className={`${isCurrentlyActive ? 'bg-amber-50 border-amber-200 text-amber-800' : 'bg-green-50 border-green-200 text-green-800'} border rounded-lg p-3`}>
            <div className="flex items-center gap-2">
              <LucideAlertTriangle className="h-4 w-4" />
              <span className="font-medium">Note</span>
            </div>
            <p className="text-sm mt-1">
              {isCurrentlyActive
                ? 'Deactivating this user will prevent them from logging in, but all their data will be preserved. You can reactivate the user at any time.'
                : 'Activating this user will allow them to log in to the system with their existing credentials.'}
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <LoadingButton
            variant={isCurrentlyActive ? "destructive" : "default"}
            onClick={handleToggleActivation}
            isLoading={isLoading}
            className={!isCurrentlyActive ? "bg-green-600 hover:bg-green-700" : ""}
          >
            {isCurrentlyActive ? 'Deactivate User' : 'Activate User'}
          </LoadingButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
