const mysql = require('mysql2/promise');
require('dotenv').config();

async function migrateToMultiAnimal() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management',
      multipleStatements: true
    });

    console.log('Connected to database');

    // Step 1: Create the new animals table
    console.log('Creating animals table...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS animals (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tag_number VARCHAR(50) NOT NULL UNIQUE,
        name VARCHAR(100) NOT NULL,
        animal_type ENUM('Goat', 'Sheep', 'Cattle', 'Pig') NOT NULL,
        breed VARCHAR(100) NOT NULL,
        gender ENUM('Male', 'Female') NOT NULL,
        birth_date DATE,
        acquisition_date DATE,
        acquisition_price DECIMAL(10, 2),
        status ENUM('Healthy', 'Sick', 'Pregnant', 'Lactating', 'Deceased', 'Injured', 'Quarantined') NOT NULL DEFAULT 'Healthy',
        weight DECIMAL(8, 2),
        color VARCHAR(100),
        markings TEXT,
        sire VARCHAR(50),
        dam VARCHAR(50),
        purchase_price DECIMAL(10, 2),
        is_registered BOOLEAN DEFAULT FALSE,
        registration_number VARCHAR(100),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_animal_type (animal_type),
        INDEX idx_tag_number (tag_number),
        INDEX idx_status (status),
        INDEX idx_gender (gender)
      )
    `);

    // Step 2: Check if goats table exists and migrate data
    const [tables] = await connection.execute(`
      SELECT COUNT(*) as count FROM information_schema.tables 
      WHERE table_schema = DATABASE() AND table_name = 'goats'
    `);

    if (tables[0].count > 0) {
      console.log('Migrating existing goats data...');
      
      // Migrate goats data to animals table
      await connection.execute(`
        INSERT IGNORE INTO animals (
          tag_number, name, animal_type, breed, gender, birth_date,
          acquisition_date, status, weight, color,
          markings, sire, dam, purchase_price, is_registered,
          registration_number, notes, created_at, updated_at
        )
        SELECT
          COALESCE(tag_number, CONCAT('G-', id)) as tag_number,
          name,
          'Goat' as animal_type,
          breed,
          gender,
          birth_date,
          acquisition_date,
          status,
          weight,
          color,
          markings,
          sire,
          dam,
          purchase_price,
          COALESCE(is_registered, 0),
          registration_number,
          notes,
          COALESCE(created_at, NOW()),
          COALESCE(updated_at, NOW())
        FROM goats
      `);

      console.log('Goats data migrated successfully');
    }

    // Step 3: Update health_records table structure
    console.log('Updating health_records table...');
    
    // Add animal_id column if it doesn't exist
    try {
      await connection.execute(`
        ALTER TABLE health_records 
        ADD COLUMN animal_id INT AFTER id,
        ADD INDEX idx_animal_id (animal_id)
      `);
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
        throw error;
      }
    }

    // Update health_records to reference animals table
    if (tables[0].count > 0) {
      await connection.execute(`
        UPDATE health_records hr
        JOIN goats g ON hr.goat_id = g.id
        JOIN animals a ON a.tag_number = COALESCE(g.tag_number, CONCAT('G-', g.id))
        SET hr.animal_id = a.id
        WHERE hr.animal_id IS NULL OR hr.animal_id = 0
      `);
    }

    // Step 4: Update breeding_records table structure
    console.log('Updating breeding_records table...');
    
    // Add new columns for multi-animal support
    try {
      await connection.execute(`
        ALTER TABLE breeding_records 
        ADD COLUMN female_id INT AFTER id,
        ADD COLUMN male_id INT AFTER female_id,
        ADD INDEX idx_female_id (female_id),
        ADD INDEX idx_male_id (male_id)
      `);
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
        throw error;
      }
    }

    // Update breeding_records to reference animals table
    if (tables[0].count > 0) {
      await connection.execute(`
        UPDATE breeding_records br
        JOIN goats g1 ON br.doe_id = g1.id
        JOIN animals a1 ON a1.tag_number = COALESCE(g1.tag_number, CONCAT('G-', g1.id))
        SET br.female_id = a1.id
        WHERE br.female_id IS NULL OR br.female_id = 0
      `);

      await connection.execute(`
        UPDATE breeding_records br
        JOIN goats g2 ON br.buck_id = g2.id
        JOIN animals a2 ON a2.tag_number = COALESCE(g2.tag_number, CONCAT('G-', g2.id))
        SET br.male_id = a2.id
        WHERE br.buck_id IS NOT NULL AND (br.male_id IS NULL OR br.male_id = 0)
      `);
    }

    // Step 5: Update heat_cycles table structure
    console.log('Updating heat_cycles table...');
    
    try {
      await connection.execute(`
        ALTER TABLE heat_cycles 
        ADD COLUMN animal_id INT AFTER id,
        ADD INDEX idx_animal_id (animal_id)
      `);
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
        throw error;
      }
    }

    // Update heat_cycles to reference animals table
    if (tables[0].count > 0) {
      await connection.execute(`
        UPDATE heat_cycles hc
        JOIN goats g ON hc.goat_id = g.id
        JOIN animals a ON a.tag_number = COALESCE(g.tag_number, CONCAT('G-', g.id))
        SET hc.animal_id = a.id
        WHERE hc.animal_id IS NULL OR hc.animal_id = 0
      `);
    }

    // Step 6: Update financial_transactions table structure
    console.log('Updating financial_transactions table...');
    
    try {
      await connection.execute(`
        ALTER TABLE financial_transactions 
        ADD COLUMN animal_type ENUM('Goat', 'Sheep', 'Cattle', 'Pig', 'General') DEFAULT 'General' AFTER category,
        ADD COLUMN related_animal_id INT AFTER animal_type,
        ADD INDEX idx_animal_type (animal_type),
        ADD INDEX idx_related_animal_id (related_animal_id)
      `);
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
        throw error;
      }
    }

    // Update financial_transactions to reference animals table (if related_goat_id column exists)
    if (tables[0].count > 0) {
      // Check if related_goat_id column exists
      const [ftColumns] = await connection.execute(`
        SELECT COUNT(*) as count FROM information_schema.columns
        WHERE table_schema = DATABASE() AND table_name = 'financial_transactions' AND column_name = 'related_goat_id'
      `);

      if (ftColumns[0].count > 0) {
        await connection.execute(`
          UPDATE financial_transactions ft
          JOIN goats g ON ft.related_goat_id = g.id
          JOIN animals a ON a.tag_number = COALESCE(g.tag_number, CONCAT('G-', g.id))
          SET ft.related_animal_id = a.id, ft.animal_type = 'Goat'
          WHERE ft.related_goat_id IS NOT NULL AND (ft.related_animal_id IS NULL OR ft.related_animal_id = 0)
        `);
      } else {
        console.log('related_goat_id column not found in financial_transactions, skipping update');
      }
    }

    // Step 7: Update feeding_records table structure
    console.log('Updating feeding_records table...');

    try {
      await connection.execute(`
        ALTER TABLE feeding_records
        ADD COLUMN animal_type ENUM('Goat', 'Sheep', 'Cattle', 'Pig', 'All') NOT NULL DEFAULT 'All' AFTER time,
        ADD INDEX idx_animal_type (animal_type)
      `);
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
        throw error;
      }
    }

    // Step 8: Update feed_items table structure
    console.log('Updating feed_items table...');
    
    try {
      await connection.execute(`
        ALTER TABLE feed_items 
        ADD COLUMN suitable_for SET('Goat', 'Sheep', 'Cattle', 'Pig') DEFAULT 'Goat,Sheep,Cattle,Pig' AFTER moisture
      `);
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
        throw error;
      }
    }

    // Step 9: Update inventory_items table structure
    console.log('Updating inventory_items table...');
    
    try {
      await connection.execute(`
        ALTER TABLE inventory_items 
        ADD COLUMN applicable_to SET('Goat', 'Sheep', 'Cattle', 'Pig', 'General') DEFAULT 'General' AFTER supplier
      `);
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
        throw error;
      }
    }

    // Step 10: Add sample data for other animal types
    console.log('Adding sample data for other animal types...');
    
    await connection.execute(`
      INSERT IGNORE INTO animals (tag_number, name, animal_type, breed, gender, birth_date, acquisition_date, status, weight, color, notes) VALUES
      -- Sheep
      ('S-001', 'Woolly', 'Sheep', 'Dorper', 'Female', '2021-04-12', '2021-07-15', 'Healthy', 55.0, 'White with Black Head', 'Excellent wool quality'),
      ('S-002', 'Ram', 'Sheep', 'Merino', 'Male', '2020-01-08', '2020-04-20', 'Healthy', 75.3, 'White', 'Prime breeding ram'),
      ('S-003', 'Daisy', 'Sheep', 'Local Breed', 'Female', '2022-03-15', '2022-06-10', 'Lactating', 48.2, 'Brown', 'Twin lambs'),
      
      -- Cattle
      ('C-001', 'Bessie', 'Cattle', 'Holstein', 'Female', '2019-08-22', '2020-01-15', 'Healthy', 450.0, 'Black and White', 'High milk yield'),
      ('C-002', 'Bull', 'Cattle', 'Angus', 'Male', '2018-05-10', '2018-10-05', 'Healthy', 650.5, 'Black', 'Breeding bull'),
      ('C-003', 'Rosie', 'Cattle', 'Zebu Cross', 'Female', '2020-12-03', '2021-03-20', 'Pregnant', 380.2, 'Brown', 'Local adapted breed'),
      
      -- Pigs
      ('P-001', 'Porky', 'Pig', 'Large White', 'Male', '2022-01-15', '2022-04-10', 'Healthy', 180.0, 'Pink', 'Breeding boar'),
      ('P-002', 'Piglet', 'Pig', 'Landrace', 'Female', '2022-03-20', '2022-06-15', 'Lactating', 150.5, 'White', 'Good mother'),
      ('P-003', 'Bacon', 'Pig', 'Duroc', 'Male', '2022-05-08', '2022-08-12', 'Healthy', 95.2, 'Red', 'Fast growing')
    `);

    console.log('Migration completed successfully!');
    console.log('Database now supports Goats, Sheep, Cattle, and Pigs');

  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the migration
if (require.main === module) {
  migrateToMultiAnimal()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateToMultiAnimal };
