# Troubleshooting: Failed to Fetch Kids Data Error

## Problem
The breeding page is showing an error: **"Failed to fetch kids data"** when trying to load the Kidding Records tab.

## Root Causes
Based on the investigation, the issue could be one of the following:

### 1. **Database Connection Issue** (Most Likely)
The API endpoint `/api/breeding/kids` is returning a 500 Internal Server Error, which typically indicates a database connection problem.

### 2. **Next.js Route Not Loaded**
The route file exists but may not have been picked up by the Next.js development server.

### 3. **MySQL Server Not Running**
The MySQL database server might not be running or accessible.

## Solutions

### Solution 1: Verify Database Connection

1. **Test the database connection** by visiting:
   ```
   http://localhost:3000/api/test-db
   ```
   
   This will show you:
   - Whether the database connection is working
   - If the `animals` table exists
   - Your current database configuration

2. **Check the response:**
   - ✅ If `success: true` → Database is working, proceed to Solution 2
   - ❌ If `success: false` → Follow the database troubleshooting steps below

### Solution 2: Restart the Development Server

The Next.js development server needs to be restarted to pick up the route:

1. **Stop the current server:**
   - Press `Ctrl + C` in the terminal running the dev server
   - Or find and kill the process:
     ```powershell
     # Find the process
     netstat -ano | findstr :3000
     
     # Kill it (replace PID with the actual process ID)
     taskkill /F /PID <PID>
     ```

2. **Start the server again:**
   ```bash
   npm run dev
   ```

3. **Wait for the server to fully start** (you should see "Ready" in the terminal)

4. **Test the API endpoint:**
   ```
   http://localhost:3000/api/breeding/kids?max_age_months=6
   ```

### Solution 3: Database Troubleshooting

If the database connection test fails, follow these steps:

#### Step 1: Check if MySQL is Running

**On Windows:**
```powershell
# Check MySQL service status
Get-Service -Name *mysql* | Select-Object Name, Status

# Start MySQL if it's stopped
net start MySQL80  # or MySQL57, depending on your version
```

**Alternative:** Open Services (services.msc) and look for MySQL service

#### Step 2: Verify Database Credentials

Check your `.env.local` file in the project root:

```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=goat_management
```

**Test the credentials manually:**
```bash
# If MySQL is in your PATH
mysql -u root -p -h localhost goat_management

# Or use MySQL Workbench or another GUI tool
```

#### Step 3: Verify Database and Tables Exist

Connect to MySQL and run:

```sql
-- Show all databases
SHOW DATABASES;

-- Use the goat_management database
USE goat_management;

-- Check if animals table exists
SHOW TABLES;

-- Verify animals table structure
DESCRIBE animals;

-- Check if there's any data
SELECT COUNT(*) FROM animals;
```

#### Step 4: Check MySQL Configuration

If you're getting connection errors, check your MySQL configuration:

1. **Find your MySQL config file:**
   - Windows: `C:\ProgramData\MySQL\MySQL Server 8.0\my.ini`
   - Or: `C:\Program Files\MySQL\MySQL Server 8.0\my.ini`

2. **Verify the bind-address:**
   ```ini
   [mysqld]
   bind-address = 0.0.0.0
   # or
   bind-address = 127.0.0.1
   ```

3. **Restart MySQL after any config changes**

### Solution 4: Check for Port Conflicts

Make sure port 3000 is not being used by another application:

```powershell
# Check what's using port 3000
netstat -ano | findstr :3000

# If another app is using it, either:
# 1. Stop that application
# 2. Or change Next.js port in package.json:
#    "dev": "next dev -p 3001"
```

### Solution 5: Verify the API Route File

Check that the file `app/api/breeding/kids/route.js` exists and has the correct structure:

```javascript
import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

export async function GET(request) {
  // ... rest of the code
}
```

## Testing the Fix

After applying the solutions:

1. **Restart the development server**
2. **Open the browser console** (F12)
3. **Navigate to** `/breeding`
4. **Click on the "Kidding Records" tab**
5. **Check for:**
   - ✅ No console errors
   - ✅ Statistics cards showing data
   - ✅ Table with kids data

## Common Error Messages and Solutions

| Error Message | Cause | Solution |
|---------------|-------|----------|
| `ECONNREFUSED` | MySQL server not running | Start MySQL service |
| `ER_ACCESS_DENIED_ERROR` | Wrong credentials | Check `.env.local` file |
| `ER_BAD_DB_ERROR` | Database doesn't exist | Create `goat_management` database |
| `ER_NO_SUCH_TABLE` | Table doesn't exist | Run database migrations |
| `404 Not Found` | Route not loaded | Restart Next.js server |
| `500 Internal Server Error` | Database connection issue | Check MySQL and credentials |

## Additional Debugging

### Enable Detailed Logging

Add console logs to the API route to see what's happening:

```javascript
export async function GET(request) {
  console.log('=== Kids API Called ===');
  console.log('Environment:', {
    DB_HOST: process.env.DB_HOST,
    DB_USER: process.env.DB_USER,
    DB_NAME: process.env.DB_NAME
  });
  
  let connection;
  try {
    console.log('Getting connection from pool...');
    connection = await pool.getConnection();
    console.log('Connection successful!');
    
    // ... rest of the code
  } catch (error) {
    console.error('=== Kids API Error ===');
    console.error('Error:', error);
    console.error('Error code:', error.code);
    console.error('Error message:', error.message);
    // ... error handling
  }
}
```

### Check Server Logs

Look at the terminal where `npm run dev` is running for error messages.

## Prevention

To prevent this issue in the future:

1. **Always restart the dev server** after creating new API routes
2. **Keep MySQL running** during development
3. **Use environment variables** for all database credentials
4. **Test API endpoints** directly before using them in the UI
5. **Add proper error handling** with detailed error messages

## Need More Help?

If none of these solutions work:

1. Check the server terminal logs for specific error messages
2. Check the browser console for detailed error information
3. Test the database connection using the `/api/test-db` endpoint
4. Verify that all dependencies are installed: `npm install`
5. Try deleting `.next` folder and rebuilding: `rm -rf .next && npm run dev`

## Files Involved

- `app/api/breeding/kids/route.js` - The API endpoint
- `app/breeding/page.tsx` - The breeding page that calls the API
- `.env.local` - Database configuration
- `lib/db.ts` - Database connection utility

## Related Documentation

- [Next.js API Routes](https://nextjs.org/docs/app/building-your-application/routing/route-handlers)
- [MySQL2 Documentation](https://github.com/sidorares/node-mysql2)
- [Database Setup Guide](./docs/database_setup.md)

