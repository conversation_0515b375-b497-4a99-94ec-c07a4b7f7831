"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import DashboardLayout from "@/components/dashboard-layout"
import { Badge } from "@/components/ui/badge"
import {
  LucideFilter,
  LucidePlus,
  LucideSearch,
  LucideRefreshCw,
  LucideChevronDown,
  LucideX,
  LucideLoader2,
  LucideAlertTriangle
} from "lucide-react"
import { ErrorBoundary } from "react-error-boundary"
import { useSearch } from "@/hooks/use-search"

// Define the Pig interface
interface Pig {
  id: number;
  name: string;
  tag: string;
  breed: string;
  gender: string;
  age: string;
  status: string;
  [key: string]: any; // Allow additional properties
}

function ErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {
  return (
    <div className="p-6 bg-red-50 rounded-lg border border-red-200">
      <h2 className="text-lg font-semibold text-red-800 mb-2">Something went wrong:</h2>
      <pre className="text-sm text-red-600 overflow-auto p-2 bg-red-100 rounded">{error.message}</pre>
      <Button variant="outline" className="mt-4" onClick={resetErrorBoundary}>
        Try again
      </Button>
    </div>
  )
}

// Skeleton loader for pig cards
function PigCardSkeleton() {
  return (
    <div className="goat-profile-card animate-pulse">
      <div className="h-48 w-full bg-gray-200"></div>
      <div className="goat-info">
        <div className="h-5 w-24 bg-white/30 rounded mb-2"></div>
        <div className="h-4 w-32 bg-white/30 rounded mb-4"></div>
        <div className="flex items-center justify-between">
          <div className="h-6 w-16 bg-white/30 rounded"></div>
          <div className="h-4 w-10 bg-white/30 rounded"></div>
        </div>
      </div>
    </div>
  )
}

export default function PigsPage() {
  const [breedFilter, setBreedFilter] = useState("all")
  const [genderFilter, setGenderFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [activeView, setActiveView] = useState("grid")
  const [isLoading, setIsLoading] = useState(true)
  const [showFilters, setShowFilters] = useState(false)
  const [pigsData, setPigsData] = useState<Pig[]>([])
  const [stats, setStats] = useState({
    total: 0,
    healthy: 0,
    sick: 0,
    injured: 0,
    quarantined: 0,
    males: 0,
    females: 0
  })
  const [error, setError] = useState<string | null>(null)

  // Fetch pigs data from API
  useEffect(() => {
    const fetchPigsData = async () => {
      try {
        setIsLoading(true)
        const response = await fetch('/api/pigs')

        if (!response.ok) {
          throw new Error(`Failed to fetch pigs data: ${response.status}`)
        }

        const data = await response.json()

        // Ensure we have valid data structure
        if (data && typeof data === 'object') {
          // Set pigs data with fallback to empty array
          setPigsData(Array.isArray(data.pigs) ? data.pigs : [])

          // Set stats with fallback to default values
          if (data.stats && typeof data.stats === 'object') {
            setStats({
              total: data.stats.total || 0,
              healthy: data.stats.healthy || 0,
              sick: data.stats.sick || 0,
              injured: data.stats.injured || 0,
              quarantined: data.stats.quarantined || 0,
              males: data.stats.males || 0,
              females: data.stats.females || 0
            })
          } else {
            // Calculate stats from pigs data if stats not provided
            const pigs: Pig[] = Array.isArray(data.pigs) ? data.pigs : []
            const calculatedStats = {
              total: pigs.length,
              healthy: pigs.filter((p: Pig) => p.status === 'Healthy').length,
              sick: pigs.filter((p: Pig) => p.status === 'Sick').length,
              injured: pigs.filter((p: Pig) => p.status === 'Injured').length,
              quarantined: pigs.filter((p: Pig) => p.status === 'Quarantined').length,
              males: pigs.filter((p: Pig) => p.gender === 'Male').length,
              females: pigs.filter((p: Pig) => p.gender === 'Female').length
            }
            setStats(calculatedStats)
          }
        } else {
          // If data is invalid, set empty defaults
          setPigsData([])
          setStats({
            total: 0,
            healthy: 0,
            sick: 0,
            injured: 0,
            quarantined: 0,
            males: 0,
            females: 0
          })
        }

        setError(null)
      } catch (err) {
        console.error('Error fetching pigs data:', err)
        setError(err instanceof Error ? err.message : 'An unknown error occurred')

        // Set empty defaults on error
        setPigsData([])
        setStats({
          total: 0,
          healthy: 0,
          sick: 0,
          injured: 0,
          quarantined: 0,
          males: 0,
          females: 0
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchPigsData()
  }, [])

  // Use the custom search hook
  const {
    searchTerm,
    setSearchTerm,
    filteredItems: searchFilteredPigs,
    isSearching,
  } = useSearch<Pig>({
    items: pigsData,
    searchFields: ["id", "name", "tag", "breed", "status"],
  })

  // Apply additional filters (breed, gender, status)
  const getFilteredPigs = (): Pig[] => {
    return searchFilteredPigs.filter((pig: Pig) => {
      const matchesBreed = breedFilter === "all" || pig.breed === breedFilter
      const matchesGender = genderFilter === "all" || pig.gender === genderFilter
      const matchesStatus = statusFilter === "all" || pig.status === statusFilter

      return matchesBreed && matchesGender && matchesStatus
    })
  }

  // Filter pigs based on search term and filters
  const filteredPigs = getFilteredPigs()

  const resetFilters = () => {
    setSearchTerm("")
    setBreedFilter("all")
    setGenderFilter("all")
    setStatusFilter("all")
  }

  // Handle search input change with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        {/* Header with stats */}
        <div className="flex flex-col gap-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <h1 className="text-3xl font-bold tracking-tight text-gradient-primary">Pig Registry</h1>
            <Link href="/pigs/add">
              <Button className="bg-gradient-to-r from-pink-500 to-rose-600 hover:from-pink-600 hover:to-rose-700 text-white shadow-md hover:shadow-lg transition-all duration-300">
                <LucidePlus className="mr-2 h-4 w-4" />
                Add New Pig
              </Button>
            </Link>
          </div>

          {/* Error state */}
          {error && !isLoading && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
              <h3 className="font-medium flex items-center gap-2">
                <LucideAlertTriangle className="h-5 w-5" />
                Error Loading Pigs
              </h3>
              <p className="mt-1 text-sm">{error}</p>
              <Button
                variant="outline"
                className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
                onClick={() => window.location.reload()}
              >
                Retry
              </Button>
            </div>
          )}

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            {isLoading ? (
              // Loading skeleton for stats cards
              Array(6).fill(0).map((_, i) => (
                <Card key={i} className="dashboard-card-primary">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Loading...</div>
                    <div className="h-8 w-12 bg-gray-200 animate-pulse rounded mt-1"></div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <>
                <Card className="dashboard-card-primary">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Total</div>
                    <div className="text-2xl font-bold">{stats?.total ?? 0}</div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-secondary">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Healthy</div>
                    <div className="text-2xl font-bold text-green-600">{stats?.healthy ?? 0}</div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-accent">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Sick</div>
                    <div className="text-2xl font-bold text-red-600">{stats?.sick ?? 0}</div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-amber">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Injured</div>
                    <div className="text-2xl font-bold text-orange-600">{stats?.injured ?? 0}</div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-secondary">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Males</div>
                    <div className="text-2xl font-bold text-purple-600">{stats?.males ?? 0}</div>
                  </CardContent>
                </Card>
                <Card className="dashboard-card-primary">
                  <CardContent className="p-4">
                    <div className="text-sm text-muted-foreground">Females</div>
                    <div className="text-2xl font-bold text-pink-600">{stats?.females ?? 0}</div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </div>

        {/* Search and filters */}
        <Card className="overflow-hidden">
          <CardHeader className="p-4 pb-0">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
              <CardTitle>Pig Registry</CardTitle>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="sm:hidden" onClick={() => setShowFilters(!showFilters)}>
                  <LucideFilter className="h-4 w-4 mr-2" />
                  Filters
                  <LucideChevronDown
                    className={`h-4 w-4 ml-1 transition-transform ${showFilters ? "rotate-180" : ""}`}
                  />
                </Button>
                <div className="flex border rounded-md overflow-hidden">
                  <Button
                    variant={activeView === "grid" ? "default" : "ghost"}
                    size="sm"
                    className={
                      activeView === "grid"
                        ? "rounded-none bg-gradient-to-r from-pink-500 to-rose-500 text-white"
                        : "rounded-none text-pink-600 hover:text-pink-700 hover:bg-pink-50"
                    }
                    onClick={() => setActiveView("grid")}
                  >
                    Grid
                  </Button>
                  <Button
                    variant={activeView === "table" ? "default" : "ghost"}
                    size="sm"
                    className={
                      activeView === "table"
                        ? "rounded-none bg-gradient-to-r from-rose-500 to-red-500 text-white"
                        : "rounded-none text-red-600 hover:text-red-700 hover:bg-red-50"
                    }
                    onClick={() => setActiveView("table")}
                  >
                    Table
                  </Button>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="flex flex-col gap-4">
              <div className="relative">
                <LucideSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by ID, name, tag or breed..."
                  className="pl-10 pr-10 input-primary"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
                {searchTerm && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-pink-50 hover:text-pink-700"
                    onClick={() => setSearchTerm("")}
                  >
                    <LucideX className="h-4 w-4" />
                  </Button>
                )}
              </div>

              <div className={`grid gap-4 md:grid-cols-4 ${showFilters ? "block" : "hidden sm:grid"}`}>
                <div>
                  <Select value={breedFilter} onValueChange={setBreedFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Breed" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Breeds</SelectItem>
                      <SelectItem value="Large White">Large White</SelectItem>
                      <SelectItem value="Landrace">Landrace</SelectItem>
                      <SelectItem value="Duroc">Duroc</SelectItem>
                      <SelectItem value="Yorkshire">Yorkshire</SelectItem>
                      <SelectItem value="Hampshire">Hampshire</SelectItem>
                      <SelectItem value="Pietrain">Pietrain</SelectItem>
                      <SelectItem value="Local Breed">Local Breed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Select value={genderFilter} onValueChange={setGenderFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Genders</SelectItem>
                      <SelectItem value="Male">Male</SelectItem>
                      <SelectItem value="Female">Female</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="Healthy">Healthy</SelectItem>
                      <SelectItem value="Sick">Sick</SelectItem>
                      <SelectItem value="Injured">Injured</SelectItem>
                      <SelectItem value="Quarantined">Quarantined</SelectItem>
                      <SelectItem value="Deceased">Deceased</SelectItem>
                      <SelectItem value="Pregnant">Pregnant</SelectItem>
                      <SelectItem value="Lactating">Lactating</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button
                  variant="outline"
                  className="flex items-center gap-2 border-pink-500 text-pink-600 hover:bg-pink-50 hover:text-pink-700"
                  onClick={resetFilters}
                  disabled={breedFilter === "all" && genderFilter === "all" && statusFilter === "all" && !searchTerm}
                >
                  <LucideRefreshCw className="h-4 w-4" />
                  Reset Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <ErrorBoundary FallbackComponent={ErrorFallback} onReset={() => window.location.reload()}>
          {activeView === "grid" ? (
            <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {isLoading ? (
                // Full loading state
                <div className="col-span-full flex flex-col items-center justify-center py-12">
                  <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                  <p className="text-muted-foreground">Loading pigs data...</p>
                </div>
              ) : isSearching ? (
                // Skeleton loading state for search
                Array(8)
                  .fill(0)
                  .map((_, i) => <PigCardSkeleton key={i} />)
              ) : filteredPigs.length > 0 ? (
                filteredPigs.map((pig) => {
                  // Function to get appropriate pig image based on breed and gender
                  const getPigImage = (breed: string, gender: string) => {
                    // Use a placeholder service that shows pig-related content
                    const pigText = encodeURIComponent(`${breed} ${gender} Pig`)

                    // Large White pigs - pink/white
                    if (breed.toLowerCase().includes('large white')) {
                      return `https://via.placeholder.com/300x200/FFC0CB/8B4513?text=${pigText}`
                    }
                    // Landrace pigs - white
                    else if (breed.toLowerCase().includes('landrace')) {
                      return `https://via.placeholder.com/300x200/FFFFFF/8B4513?text=${pigText}`
                    }
                    // Duroc pigs - red/brown
                    else if (breed.toLowerCase().includes('duroc')) {
                      return `https://via.placeholder.com/300x200/CD853F/FFFFFF?text=${pigText}`
                    }
                    // Yorkshire pigs - white
                    else if (breed.toLowerCase().includes('yorkshire')) {
                      return `https://via.placeholder.com/300x200/F5F5DC/8B4513?text=${pigText}`
                    }
                    // Other breeds - general pig images
                    else {
                      return `https://via.placeholder.com/300x200/FFB6C1/8B4513?text=${pigText}`
                    }
                  }

                  return (
                  <Link href={`/pigs/${pig.id}`} key={pig.id} className="block hover-lift hover-glow-primary">
                    <div className="goat-profile-card">
                      <img
                        src={getPigImage(pig.breed, pig.gender)}
                        alt={`${pig.name} - ${pig.breed} ${pig.gender}`}
                        className="goat-image"
                        onError={(e) => {
                          e.currentTarget.src = `/placeholder.svg?height=200&width=300&text=${encodeURIComponent(pig.name)}`
                        }}
                      />
                      <div className="goat-info">
                        <h3 className="text-lg font-bold">{pig.name}</h3>
                        <p className="text-sm opacity-90">
                          {pig.breed} • {pig.gender === 'Male' ? 'Boar' : 'Sow'} • {pig.age}
                        </p>
                        <div className="flex items-center justify-between mt-2">
                          <Badge
                            variant={
                              pig.status === "Healthy"
                                ? "outline"
                                : pig.status === "Sick"
                                  ? "destructive"
                                  : pig.status === "Injured"
                                    ? "secondary"
                                    : pig.status === "Quarantined"
                                      ? "default"
                                      : pig.status === "Deceased"
                                        ? "outline"
                                        : pig.status === "Pregnant"
                                          ? "default"
                                          : pig.status === "Lactating"
                                            ? "default"
                                            : "default"
                            }
                            className={
                              pig.status === "Healthy"
                                ? "badge-healthy"
                                : pig.status === "Sick"
                                  ? "badge-sick"
                                  : pig.status === "Injured"
                                    ? "badge-injured"
                                    : pig.status === "Quarantined"
                                      ? "badge-quarantined"
                                      : pig.status === "Deceased"
                                        ? "badge-deceased"
                                        : pig.status === "Pregnant"
                                          ? "bg-pink-100 text-pink-800 hover:bg-pink-200 border-pink-300"
                                          : pig.status === "Lactating"
                                            ? "bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300"
                                            : ""
                            }
                          >
                            {pig.status}
                          </Badge>
                          <span className="text-xs">{pig.tag}</span>
                        </div>
                      </div>
                    </div>
                  </Link>
                  )
                })
              ) : (
                <div className="col-span-full flex flex-col items-center justify-center p-8 text-center">
                  <div className="rounded-full bg-muted p-3 mb-4">
                    <LucideSearch className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-1">No pigs found</h3>
                  <p className="text-muted-foreground mb-4">Try adjusting your search or filters</p>
                  <Button variant="outline" onClick={resetFilters}>
                    Reset Filters
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="p-0 overflow-auto">
                {isLoading ? (
                  <div className="p-8 flex flex-col items-center justify-center">
                    <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                    <p className="text-muted-foreground">Loading pigs data...</p>
                  </div>
                ) : isSearching ? (
                  <div className="p-8 flex justify-center">
                    <LucideRefreshCw className="h-8 w-8 text-primary animate-spin" />
                  </div>
                ) : (
                  <Table className="responsive-table">
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Name</TableHead>
                        <TableHead>Breed</TableHead>
                        <TableHead>Gender</TableHead>
                        <TableHead>Age</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Tag</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPigs.length > 0 ? (
                        filteredPigs.map((pig) => (
                          <TableRow key={pig.id} className="hover-lift">
                            <TableCell className="font-medium" data-label="ID">
                              {pig.id}
                            </TableCell>
                            <TableCell data-label="Name">{pig.name}</TableCell>
                            <TableCell data-label="Breed">{pig.breed}</TableCell>
                            <TableCell data-label="Gender">{pig.gender}</TableCell>
                            <TableCell data-label="Age">{pig.age}</TableCell>
                            <TableCell data-label="Status">
                              <Badge
                                variant={
                                  pig.status === "Healthy"
                                    ? "outline"
                                    : pig.status === "Sick"
                                      ? "destructive"
                                      : pig.status === "Injured"
                                        ? "secondary"
                                        : pig.status === "Quarantined"
                                          ? "default"
                                          : pig.status === "Deceased"
                                            ? "outline"
                                            : pig.status === "Pregnant"
                                              ? "default"
                                              : pig.status === "Lactating"
                                                ? "default"
                                                : "default"
                                }
                                className={
                                  pig.status === "Healthy"
                                    ? "badge-healthy"
                                    : pig.status === "Sick"
                                      ? "badge-sick"
                                      : pig.status === "Injured"
                                        ? "badge-injured"
                                        : pig.status === "Quarantined"
                                          ? "badge-quarantined"
                                          : pig.status === "Deceased"
                                            ? "badge-deceased"
                                            : pig.status === "Pregnant"
                                              ? "bg-pink-100 text-pink-800 hover:bg-pink-200 border-pink-300"
                                              : pig.status === "Lactating"
                                                ? "bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300"
                                                : ""
                                }
                              >
                                {pig.status}
                              </Badge>
                            </TableCell>
                            <TableCell data-label="Tag">{pig.tag}</TableCell>
                            <TableCell className="text-right" data-label="Actions">
                              <Link href={`/pigs/${pig.id}`}>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="bg-gradient-to-r from-pink-50 to-rose-50 text-pink-600 hover:from-pink-100 hover:to-rose-100 hover:text-pink-700"
                                >
                                  View
                                </Button>
                              </Link>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-6">
                            <div className="flex flex-col items-center justify-center p-8">
                              <div className="rounded-full bg-muted p-3 mb-4">
                                <LucideSearch className="h-6 w-6 text-muted-foreground" />
                              </div>
                              <h3 className="text-lg font-medium mb-1">No pigs found</h3>
                              <p className="text-muted-foreground mb-4">Try adjusting your search or filters</p>
                              <Button variant="outline" onClick={resetFilters}>
                                Reset Filters
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          )}
        </ErrorBoundary>
      </div>
    </DashboardLayout>
  )
}
