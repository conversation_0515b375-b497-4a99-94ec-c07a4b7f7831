import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a number as Malawi Kwacha (MWK)
 * @param amount - The amount to format
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, options: { decimals?: number } = {}) {
  const { decimals = 2 } = options
  return `MWK ${amount.toFixed(decimals)}`
}
