import { useState, useEffect } from 'react';
import { 
  UserRole, 
  Permission, 
  hasPermission, 
  hasAnyPermission, 
  hasAllPermissions,
  canAccessRoute,
  getUserRole,
  getRolePermissions
} from '@/lib/permissions';

interface UsePermissionsReturn {
  userRole: UserRole | null;
  permissions: Permission[];
  hasPermission: (permission: Permission) => boolean;
  hasAnyPermission: (permissions: Permission[]) => boolean;
  hasAllPermissions: (permissions: Permission[]) => boolean;
  canAccessRoute: (route: string) => boolean;
  isLoading: boolean;
}

/**
 * Hook for managing user permissions
 */
export function usePermissions(): UsePermissionsReturn {
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Get user role from localStorage
    const role = getUserRole();
    setUserRole(role);
    setIsLoading(false);

    // Listen for storage changes (when user logs in/out)
    const handleStorageChange = () => {
      const newRole = getUserRole();
      setUserRole(newRole);
    };

    window.addEventListener('storage', handleStorageChange);
    
    // Also listen for custom events when user data changes
    window.addEventListener('userChanged', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('userChanged', handleStorageChange);
    };
  }, []);

  const permissions = userRole ? getRolePermissions(userRole) : [];

  return {
    userRole,
    permissions,
    hasPermission: (permission: Permission) => 
      userRole ? hasPermission(userRole, permission) : false,
    hasAnyPermission: (permissions: Permission[]) => 
      userRole ? hasAnyPermission(userRole, permissions) : false,
    hasAllPermissions: (permissions: Permission[]) => 
      userRole ? hasAllPermissions(userRole, permissions) : false,
    canAccessRoute: (route: string) => 
      userRole ? canAccessRoute(userRole, route) : false,
    isLoading
  };
}

/**
 * Hook for checking a specific permission
 */
export function useHasPermission(permission: Permission): boolean {
  const { hasPermission } = usePermissions();
  return hasPermission(permission);
}

/**
 * Hook for checking multiple permissions
 */
export function useHasAnyPermission(permissions: Permission[]): boolean {
  const { hasAnyPermission } = usePermissions();
  return hasAnyPermission(permissions);
}

/**
 * Hook for checking route access
 */
export function useCanAccessRoute(route: string): boolean {
  const { canAccessRoute } = usePermissions();
  return canAccessRoute(route);
}
