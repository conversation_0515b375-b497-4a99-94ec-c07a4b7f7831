'use server'

import { revalidatePath } from 'next/cache'
import { db } from '@/lib/db'

export async function addCattle(formData: FormData) {
  const data = {
    tag_number: formData.get('tagNumber'),
    name: formData.get('name'),
    animal_type: 'Cattle', // Set animal type to 'Cattle'
    breed: formData.get('breed'),
    gender: formData.get('gender'),
    birth_date: formData.get('birthDate') || null,
    acquisition_date: formData.get('acquisitionDate') || null,
    status: formData.get('status') || 'Healthy',
    weight: formData.get('weight') || null,
    color: formData.get('color') || null,
    markings: formData.get('markings') || null,
    sire: formData.get('sire') || null,
    dam: formData.get('dam') || null,
    purchase_price: formData.get('purchasePrice') || null,
    is_registered: formData.get('isRegistered') === 'on' ? 1 : 0,
    registration_number: formData.get('registrationNumber') || null,
    notes: formData.get('notes') || null
  }

  try {
    const connection = await db.getConnection()

    const [result] = await connection.execute(
      `INSERT INTO animals (
        tag_number, name, animal_type, breed, gender, birth_date, acquisition_date,
        status, weight, color, markings, sire, dam, purchase_price,
        is_registered, registration_number, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        data.tag_number, data.name, data.animal_type, data.breed, data.gender, data.birth_date,
        data.acquisition_date, data.status, data.weight, data.color, data.markings,
        data.sire, data.dam, data.purchase_price, data.is_registered,
        data.registration_number, data.notes
      ]
    )

    connection.release()

    revalidatePath('/cattle')
    return { success: true, message: "Cattle successfully added to animals table" }
  } catch (error) {
    console.error('Error adding cattle:', error)

    // Check for duplicate entry error
    if (error.code === 'ER_DUP_ENTRY' && error.sqlMessage &&
        error.sqlMessage.includes('tag_number')) {
      return {
        success: false,
        message: "An animal with this tag number already exists. Please use a different tag number."
      }
    }

    return { success: false, message: "Failed to add cattle. Please try again." }
  }
}
