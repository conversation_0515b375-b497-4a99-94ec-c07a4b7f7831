'use client'

import { ReactNode } from 'react';
import { usePermissions } from '@/hooks/usePermissions';
import { Permission } from '@/lib/permissions';
import { Card, CardContent } from '@/components/ui/card';
import { LucideLock, LucideShield } from 'lucide-react';

interface PermissionGuardProps {
  children: ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean;
  fallback?: ReactNode;
  showFallback?: boolean;
}

/**
 * Component that conditionally renders children based on user permissions
 */
export function PermissionGuard({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback,
  showFallback = true
}: PermissionGuardProps) {
  const { hasPermission, hasAnyPermission, hasAllPermissions, isLoading } = usePermissions();

  // Show loading state
  if (isLoading) {
    return null;
  }

  let hasAccess = false;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (permissions.length > 0) {
    hasAccess = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);
  } else {
    // If no permissions specified, allow access
    hasAccess = true;
  }

  if (hasAccess) {
    return <>{children}</>;
  }

  // Show fallback if access denied
  if (fallback) {
    return <>{fallback}</>;
  }

  if (showFallback) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="flex items-center gap-3 p-6">
          <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
            <LucideLock className="h-5 w-5 text-red-600" />
          </div>
          <div>
            <h3 className="font-medium text-red-900">Access Denied</h3>
            <p className="text-sm text-red-700">
              You don't have permission to access this feature.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return null;
}

/**
 * Component for protecting entire routes
 */
interface RouteGuardProps {
  children: ReactNode;
  route: string;
  fallback?: ReactNode;
}

export function RouteGuard({ children, route, fallback }: RouteGuardProps) {
  const { canAccessRoute, isLoading } = usePermissions();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <LucideShield className="h-8 w-8 mx-auto mb-2 text-gray-400 animate-pulse" />
          <p className="text-sm text-gray-500">Checking permissions...</p>
        </div>
      </div>
    );
  }

  if (canAccessRoute(route)) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <Card className="border-red-200 bg-red-50 max-w-md">
        <CardContent className="text-center p-8">
          <div className="h-16 w-16 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-4">
            <LucideLock className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-red-900 mb-2">Access Denied</h2>
          <p className="text-red-700 mb-4">
            You don't have permission to access this page.
          </p>
          <p className="text-sm text-red-600">
            Please contact your administrator if you believe this is an error.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Higher-order component for protecting components
 */
export function withPermission<T extends object>(
  Component: React.ComponentType<T>,
  permission: Permission
) {
  return function ProtectedComponent(props: T) {
    return (
      <PermissionGuard permission={permission}>
        <Component {...props} />
      </PermissionGuard>
    );
  };
}

/**
 * Higher-order component for protecting routes
 */
export function withRouteGuard<T extends object>(
  Component: React.ComponentType<T>,
  route: string
) {
  return function ProtectedRoute(props: T) {
    return (
      <RouteGuard route={route}>
        <Component {...props} />
      </RouteGuard>
    );
  };
}
