"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { LoadingButton } from "@/components/ui/loading-button"
import { LucideArrowLeft, LucideArrowDownRight, LucideArrowUpRight, LucideSave } from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import { toast } from "@/components/ui/use-toast"
import { BackButton } from "@/components/ui/back-button"

// Sample inventory items for the dropdown
const inventoryItems = [
  { id: "INV-001", name: "Alfalfa Hay", category: "Feed", unit: "kg" },
  { id: "INV-002", name: "Grain Mix", category: "Feed", unit: "kg" },
  { id: "INV-003", name: "Mineral Blocks", category: "Feed", unit: "blocks" },
  { id: "INV-004", name: "Dewormer", category: "Medication", unit: "bottles" },
  { id: "INV-005", name: "Antibiotics", category: "Medication", unit: "boxes" },
  { id: "INV-006", name: "Hoof Trimmers", category: "Equipment", unit: "pieces" },
  { id: "INV-007", name: "Milking Buckets", category: "Equipment", unit: "pieces" },
  { id: "INV-008", name: "Ear Tags", category: "Supplies", unit: "pieces" },
  { id: "INV-009", name: "Bedding Straw", category: "Supplies", unit: "kg" },
  { id: "INV-010", name: "Vaccines", category: "Medication", unit: "doses" },
]

export default function RecordTransactionPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedItem, setSelectedItem] = useState<{ id: string, name: string, category: string, unit: string } | null>(null)
  const [formData, setFormData] = useState({
    item: "",
    transactionType: "in",
    quantity: "",
    unit: "",
    date: new Date().toISOString().split("T")[0],
    notes: "",
  })

  // Debug current state
  useEffect(() => {
    console.log("Current form state:", formData);
    console.log("Selected item:", selectedItem);
  }, [formData, selectedItem]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleItemSelect = (value: string) => {
    console.log("Item selected with value:", value);

    // Find the selected item
    const item = inventoryItems.find(item => item.id === value);
    console.log("Found item object:", item);

    if (item) {
      // Update both the selected item and the form data
      setSelectedItem(item);
      setFormData(prev => ({
        ...prev,
        item: value,
        unit: item.unit
      }));
    }
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Prepare the data to be sent to the API
      const transactionData = {
        item: selectedItem ? selectedItem.name : formData.item, // Use the selected item name
        transaction_type: formData.transactionType.toLowerCase(),
        quantity: parseFloat(formData.quantity),
        unit: formData.unit,
        transaction_date: formData.date,
        notes: formData.notes || null
      };

      console.log('Sending transaction data to API:', transactionData);

      // Send the data to the API
      const response = await fetch('/api/inventory/transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(transactionData),
        cache: 'no-store',
      });

      // Log the raw response for debugging
      console.log('Raw response status:', response.status);
      const responseText = await response.text();
      console.log('Raw response text:', responseText);

      // Parse the response text back to JSON
      const responseData = responseText ? JSON.parse(responseText) : {};
      console.log('API response:', responseData);

      if (!response.ok) {
        // Create a more informative error message
        let errorMessage = responseData.error || `Failed with status ${response.status}`;

        // If there are validation issues, include them in the error message
        if (responseData.issues && Array.isArray(responseData.issues)) {
          errorMessage += ': ' + responseData.issues.join(', ');
        }

        throw new Error(errorMessage);
      }

      // Success handling
      toast({
        title: "Success",
        description: "Inventory transaction recorded successfully!",
      });

      // Add a small delay before redirecting to ensure the toast is seen
      setTimeout(() => {
        router.push('/inventory');
      }, 1000);

    } catch (error: any) {
      console.error('Error submitting form:', error);
      toast({
        title: "Error",
        description: `Failed to record transaction: ${error.message || 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-accent">Record Inventory Transaction</h1>
          <BackButton href="/inventory" label="Back to Inventory" />
        </div>

        {/* Form */}
        <Card className="border-t-4 border-t-cyan-500">
          <CardHeader>
            <div className="flex items-center gap-2">
              <div className="flex">
                <LucideArrowUpRight className="h-6 w-6 text-green-500" />
                <LucideArrowDownRight className="h-6 w-6 text-red-500 -ml-2" />
              </div>
              <CardTitle>Inventory Transaction</CardTitle>
            </div>
            <CardDescription>Record stock movement in or out of inventory</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              {/* Transaction Type */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-cyan-700">Transaction Type</h3>
                <RadioGroup
                  value={formData.transactionType}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, transactionType: value }))}
                  className="flex flex-col sm:flex-row gap-4"
                >
                  <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-green-50 cursor-pointer transition-colors duration-200 border-green-200 data-[state=checked]:bg-green-50 data-[state=checked]:border-green-500">
                    <RadioGroupItem value="in" id="in" className="text-green-600" />
                    <Label htmlFor="in" className="flex items-center cursor-pointer">
                      <LucideArrowUpRight className="h-5 w-5 text-green-600 mr-2" />
                      <div>
                        <div className="font-medium">Stock In</div>
                        <div className="text-sm text-muted-foreground">Add items to inventory</div>
                      </div>
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-red-50 cursor-pointer transition-colors duration-200 border-red-200 data-[state=checked]:bg-red-50 data-[state=checked]:border-red-500">
                    <RadioGroupItem value="out" id="out" className="text-red-600" />
                    <Label htmlFor="out" className="flex items-center cursor-pointer">
                      <LucideArrowDownRight className="h-5 w-5 text-red-600 mr-2" />
                      <div>
                        <div className="font-medium">Stock Out</div>
                        <div className="text-sm text-muted-foreground">Remove items from inventory</div>
                      </div>
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Item Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-cyan-700">Item Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="item">
                      Select Item <span className="text-red-500">*</span>
                    </Label>
                    <select
                      id="item"
                      value={formData.item}
                      onChange={(e) => handleItemSelect(e.target.value)}
                      className="w-full rounded-md border border-input bg-background px-3 py-2"
                      required
                    >
                      <option value="">Select inventory item</option>
                      {inventoryItems.map((item) => (
                        <option key={item.id} value={item.id}>
                          {item.name} ({item.category})
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="date">
                      Transaction Date <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="date"
                      name="date"
                      type="date"
                      value={formData.date}
                      onChange={handleChange}
                      required
                      className="input-accent"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="quantity">
                      Quantity <span className="text-red-500">*</span>
                    </Label>
                    <div className="flex items-center">
                      <Input
                        id="quantity"
                        name="quantity"
                        type="number"
                        min="0.01"
                        step="0.01"
                        placeholder="Enter quantity"
                        value={formData.quantity}
                        onChange={handleChange}
                        required
                        className="input-accent rounded-r-none"
                      />
                      <div className="bg-muted px-3 py-2 border border-l-0 rounded-r-md text-sm text-muted-foreground">
                        {formData.unit || "units"}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-cyan-700">Additional Information</h3>
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    placeholder="Enter any additional notes about this transaction"
                    value={formData.notes}
                    onChange={handleChange}
                    className="input-accent min-h-[100px]"
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" onClick={() => router.push("/inventory")}>
                Cancel
              </Button>
              <LoadingButton
                type="submit"
                isLoading={isSubmitting}
                loadingText="Saving..."
                className={
                  formData.transactionType === "in"
                    ? "bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
                    : "bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
                }
              >
                <LucideSave className="mr-2 h-4 w-4" />
                Record Transaction
              </LoadingButton>
            </CardFooter>
          </form>
        </Card>
      </div>
    </DashboardLayout>
  )
}









