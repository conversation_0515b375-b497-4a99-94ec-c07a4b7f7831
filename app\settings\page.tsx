'use client'

import { use<PERSON>tate, useEffect } from "react"
import Link from "next/link"
import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { LoadingButton } from "@/components/ui/loading-button"
import {
  LucideCheck,
  LucideDownload,
  LucideUpload,
  LucideUsers,
  LucideX,
  LucideLoader2,
  LucideAlertCircle,
  LucideRefreshCw,
  LucideEdit,
  LucideShield,
  LucideUserPlus,
  LucideUserCog,
  LucideUserCheck,
  LucideUserX,
  LucideToggleLeft,
  LucideToggleRight,
  LucideLock,
  LucideSettings
} from "lucide-react"
import { AddUserModal } from "./add-user-modal"
import { EditUserModal } from "./edit-user-modal"
import { ToggleUserActivationModal } from "./toggle-user-activation-modal"
import { EditRoleModal } from "./edit-role-modal"
import { useToast } from "@/components/ui/use-toast"
import { RouteGuard, PermissionGuard } from "@/components/PermissionGuard"

// Metadata can't be used in client components
const metadata = {
  title: "Settings | Goat Farm Management",
  description: "Configure your farm management system settings",
}

interface User {
  id: number;
  username: string;
  email: string | null;
  full_name: string | null;
  role: string;
  created_at: string;
  last_login: string | null;
  is_active: boolean;
}

interface Permission {
  name: string;
  admin: boolean;
  manager: boolean;
  staff: boolean;
}

export default function SettingsPage() {
  const { toast } = useToast()
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false)
  const [isEditUserModalOpen, setIsEditUserModalOpen] = useState(false)
  const [isToggleActivationModalOpen, setIsToggleActivationModalOpen] = useState(false)
  const [isEditRoleModalOpen, setIsEditRoleModalOpen] = useState(false)
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null)
  const [selectedUsername, setSelectedUsername] = useState<string>('')
  const [selectedUserActive, setSelectedUserActive] = useState<boolean>(false)
  const [selectedRoleType, setSelectedRoleType] = useState<'Admin' | 'Manager' | 'Staff'>('Admin')
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isRoleLoading, setIsRoleLoading] = useState(false)
  const [roleError, setRoleError] = useState<string | null>(null)

  // Define permissions for each role
  const [permissions] = useState<Permission[]>([
    { name: "Manage Users", admin: true, manager: false, staff: false },
    { name: "Manage Goats", admin: true, manager: true, staff: false },
    { name: "View Goats", admin: true, manager: true, staff: true },
    { name: "Manage Health Records", admin: true, manager: true, staff: false },
    { name: "Add Health Records", admin: true, manager: true, staff: true },
    { name: "Manage Breeding", admin: true, manager: true, staff: false },
    { name: "Manage Finances", admin: true, manager: true, staff: false },
    { name: "System Settings", admin: true, manager: false, staff: false },
  ])

  // Fetch users
  const fetchUsers = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/users')

      if (!response.ok) {
        const errorText = await response.text()
        let errorMessage = `Failed to fetch users: ${response.status}`

        try {
          // Try to parse the error as JSON
          const errorJson = JSON.parse(errorText)
          if (errorJson.error) {
            errorMessage = errorJson.error
          }
        } catch (e) {
          // If parsing fails, use the error text if available
          if (errorText) {
            errorMessage = `${errorMessage} - ${errorText}`
          }
        }

        throw new Error(errorMessage)
      }

      const data = await response.json()

      // Validate that data is an array
      if (!Array.isArray(data)) {
        throw new Error('Invalid response format: expected an array of users')
      }

      setUsers(data)
    } catch (err: any) {
      console.error('Error fetching users:', err)
      setError(err.message)

      // Check if it's a database connection error
      const errorMessage = err.message || ''
      const isDbError = errorMessage.includes('Database connection') ||
                        errorMessage.includes('database') ||
                        errorMessage.includes('connection')

      toast({
        title: "Error",
        description: isDbError
          ? "Database connection error. Please check your database configuration."
          : "Failed to load users. Please try again.",
        variant: "destructive",
      })

      // Set empty users array to prevent rendering issues
      setUsers([])
    } finally {
      setIsLoading(false)
    }
  }

  // Load users on component mount
  useEffect(() => {
    fetchUsers()
  }, [])

  // Handle add user success
  const handleAddUserSuccess = () => {
    fetchUsers()
  }

  // Handle edit user
  const handleEditUser = (user: User) => {
    setSelectedUserId(user.id)
    setIsEditUserModalOpen(true)
  }

  // Handle toggle user activation
  const handleToggleActivation = (user: User) => {
    console.log('Toggle activation for user:', user)
    setSelectedUserId(user.id)
    setSelectedUsername(user.username)
    setSelectedUserActive(user.is_active)
    setIsToggleActivationModalOpen(true)
  }

  // Handle user action success (edit/activate/deactivate)
  const handleUserActionSuccess = () => {
    console.log('User action successful, refreshing user list...')
    // Add a small delay to ensure the database has updated
    setTimeout(() => {
      fetchUsers()
    }, 500)
  }

  // Handle edit role
  const handleEditRole = (roleType: 'Admin' | 'Manager' | 'Staff') => {
    console.log('Editing role:', roleType)
    setSelectedRoleType(roleType)
    setIsEditRoleModalOpen(true)
  }

  // Handle role update success
  const handleRoleUpdateSuccess = () => {
    console.log('Role permissions updated successfully')
    toast({
      title: "Success",
      description: `${selectedRoleType} role permissions have been updated successfully`,
      variant: "default",
    })
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">Manage your farm profile, preferences, and system settings.</p>
      </div>

      <Tabs defaultValue="farm" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="farm">Farm Profile</TabsTrigger>
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="data">Data Management</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
        </TabsList>

        {/* Farm Profile Settings */}
        <TabsContent value="farm" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Farm Information</CardTitle>
              <CardDescription>Update your farm details and contact information.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="farm-name">Farm Name</Label>
                  <Input
                    id="farm-name"
                    placeholder="Green Pastures Goat Farm"
                    defaultValue="Green Pastures Goat Farm"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="owner-name">Owner Name</Label>
                  <Input id="owner-name" placeholder="John Doe" defaultValue="John Doe" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input id="phone" placeholder="+****************" defaultValue="+****************" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    defaultValue="<EMAIL>"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Farm Address</Label>
                <Textarea
                  id="address"
                  placeholder="123 Farm Road, Rural County"
                  defaultValue="123 Farm Road, Rural County"
                />
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label htmlFor="farm-size">Farm Size (acres)</Label>
                  <Input id="farm-size" type="number" placeholder="10" defaultValue="10" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="farm-type">Farm Type</Label>
                  <Select defaultValue="dairy">
                    <SelectTrigger id="farm-type">
                      <SelectValue placeholder="Select farm type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="dairy">Dairy</SelectItem>
                      <SelectItem value="meat">Meat</SelectItem>
                      <SelectItem value="fiber">Fiber</SelectItem>
                      <SelectItem value="mixed">Mixed</SelectItem>
                      <SelectItem value="breeding">Breeding</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="established">Established Year</Label>
                  <Input id="established" type="number" placeholder="2010" defaultValue="2010" />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Cancel</Button>
              <LoadingButton>Save Changes</LoadingButton>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Registration & Compliance</CardTitle>
              <CardDescription>Manage your farm registration and compliance information.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="registration-number">Farm Registration Number</Label>
                  <Input id="registration-number" placeholder="FR-12345-AB" defaultValue="FR-12345-AB" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tax-id">Tax ID / EIN</Label>
                  <Input id="tax-id" placeholder="12-3456789" defaultValue="12-3456789" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="license-type">License Type</Label>
                  <Select defaultValue="commercial">
                    <SelectTrigger id="license-type">
                      <SelectValue placeholder="Select license type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="commercial">Commercial</SelectItem>
                      <SelectItem value="hobby">Hobby</SelectItem>
                      <SelectItem value="educational">Educational</SelectItem>
                      <SelectItem value="nonprofit">Non-profit</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="license-expiry">License Expiry Date</Label>
                  <Input id="license-expiry" type="date" defaultValue="2025-12-31" />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Cancel</Button>
              <LoadingButton>Save Changes</LoadingButton>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Account Settings */}
        <TabsContent value="account" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Your Profile</CardTitle>
              <CardDescription>Update your personal information and preferences.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="full-name">Full Name</Label>
                  <Input id="full-name" placeholder="John Doe" defaultValue="John Doe" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input id="username" placeholder="johndoe" defaultValue="johndoe" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="user-email">Email</Label>
                  <Input id="user-email" type="email" placeholder="<EMAIL>" defaultValue="<EMAIL>" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone-number">Phone Number</Label>
                  <Input id="phone-number" placeholder="+****************" defaultValue="+****************" />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Cancel</Button>
              <LoadingButton>Update Profile</LoadingButton>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>Update your account password.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="current-password">Current Password</Label>
                <Input id="current-password" type="password" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-password">New Password</Label>
                <Input id="new-password" type="password" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirm-password">Confirm New Password</Label>
                <Input id="confirm-password" type="password" />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Cancel</Button>
              <LoadingButton>Change Password</LoadingButton>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Two-Factor Authentication</CardTitle>
              <CardDescription>Add an extra layer of security to your account.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <div className="font-medium">Text Message Authentication</div>
                  <div className="text-sm text-muted-foreground">Receive a code via SMS to verify your identity.</div>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <div className="font-medium">Authenticator App</div>
                  <div className="text-sm text-muted-foreground">
                    Use an authenticator app to generate verification codes.
                  </div>
                </div>
                <Switch />
              </div>
              <div className="flex items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <div className="font-medium">Backup Codes</div>
                  <div className="text-sm text-muted-foreground">Generate backup codes for emergency access.</div>
                </div>
                <Button variant="outline" size="sm">
                  Generate Codes
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Settings */}
        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Preferences</CardTitle>
              <CardDescription>Configure application behavior and defaults.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Dark Mode</Label>
                    <p className="text-sm text-muted-foreground">Switch between light and dark themes.</p>
                  </div>
                  <Switch />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Automatic Backups</Label>
                    <p className="text-sm text-muted-foreground">Automatically backup your data weekly.</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label htmlFor="date-format">Date Format</Label>
                  <Select defaultValue="mm-dd-yyyy">
                    <SelectTrigger id="date-format">
                      <SelectValue placeholder="Select date format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mm-dd-yyyy">MM-DD-YYYY</SelectItem>
                      <SelectItem value="dd-mm-yyyy">DD-MM-YYYY</SelectItem>
                      <SelectItem value="yyyy-mm-dd">YYYY-MM-DD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="weight-unit">Weight Unit</Label>
                  <Select defaultValue="kg">
                    <SelectTrigger id="weight-unit">
                      <SelectValue placeholder="Select weight unit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="kg">Kilograms (kg)</SelectItem>
                      <SelectItem value="lb">Pounds (lb)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Select defaultValue="mwk">
                    <SelectTrigger id="currency">
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mwk">Malawi Kwacha (MWK)</SelectItem>
                      <SelectItem value="usd">US Dollar ($)</SelectItem>
                      <SelectItem value="eur">Euro (€)</SelectItem>
                      <SelectItem value="gbp">British Pound (£)</SelectItem>
                      <SelectItem value="cad">Canadian Dollar (C$)</SelectItem>
                      <SelectItem value="aud">Australian Dollar (A$)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <LoadingButton className="ml-auto">Save Preferences</LoadingButton>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Notifications</CardTitle>
              <CardDescription>Configure how and when you receive notifications.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">Receive important alerts via email.</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">SMS Notifications</Label>
                    <p className="text-sm text-muted-foreground">Receive urgent alerts via text message.</p>
                  </div>
                  <Switch />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label className="text-base">Notification Types</Label>
                  <div className="grid gap-2">
                    <div className="flex items-center space-x-2">
                      <Switch id="health-alerts" defaultChecked />
                      <Label htmlFor="health-alerts">Health Alerts</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="breeding-reminders" defaultChecked />
                      <Label htmlFor="breeding-reminders">Breeding Reminders</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="inventory-alerts" defaultChecked />
                      <Label htmlFor="inventory-alerts">Low Inventory Alerts</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="financial-alerts" />
                      <Label htmlFor="financial-alerts">Financial Reports</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="system-updates" />
                      <Label htmlFor="system-updates">System Updates</Label>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <LoadingButton className="ml-auto">Save Notification Settings</LoadingButton>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Data Management */}
        <TabsContent value="data" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Backup & Restore</CardTitle>
              <CardDescription>Manage your farm data backups and restoration.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="rounded-lg border p-4">
                <div className="space-y-2">
                  <div className="font-medium">Create Backup</div>
                  <p className="text-sm text-muted-foreground">Create a complete backup of all your farm data.</p>
                  <Button className="mt-2 flex items-center gap-2">
                    <LucideDownload className="h-4 w-4" />
                    Create Backup
                  </Button>
                </div>
              </div>

              <div className="rounded-lg border p-4">
                <div className="space-y-2">
                  <div className="font-medium">Restore from Backup</div>
                  <p className="text-sm text-muted-foreground">Restore your farm data from a previous backup.</p>
                  <div className="mt-2 flex flex-col gap-2 sm:flex-row">
                    <Input type="file" accept=".zip,.json" />
                    <Button variant="outline" className="flex items-center gap-2">
                      <LucideUpload className="h-4 w-4" />
                      Restore
                    </Button>
                  </div>
                </div>
              </div>

              <div className="rounded-lg border p-4">
                <div className="space-y-2">
                  <div className="font-medium">Scheduled Backups</div>
                  <p className="text-sm text-muted-foreground">Configure automatic backup schedule.</p>
                  <div className="mt-2 space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch id="auto-backup" defaultChecked />
                      <Label htmlFor="auto-backup">Enable Automatic Backups</Label>
                    </div>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="backup-frequency">Backup Frequency</Label>
                        <Select defaultValue="weekly">
                          <SelectTrigger id="backup-frequency">
                            <SelectValue placeholder="Select frequency" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="retention-period">Retention Period</Label>
                        <Select defaultValue="3months">
                          <SelectTrigger id="retention-period">
                            <SelectValue placeholder="Select period" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1month">1 Month</SelectItem>
                            <SelectItem value="3months">3 Months</SelectItem>
                            <SelectItem value="6months">6 Months</SelectItem>
                            <SelectItem value="1year">1 Year</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <LoadingButton className="ml-auto">Save Backup Settings</LoadingButton>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Export & Import</CardTitle>
              <CardDescription>Export or import specific data from your farm.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-base">Export Data</Label>
                  <p className="text-sm text-muted-foreground">
                    Export specific data categories to CSV or Excel format.
                  </p>
                  <div className="mt-2 grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3">
                    <Button variant="outline" size="sm" className="justify-start">
                      Goat Registry
                    </Button>
                    <Button variant="outline" size="sm" className="justify-start">
                      Health Records
                    </Button>
                    <Button variant="outline" size="sm" className="justify-start">
                      Breeding Data
                    </Button>
                    <Button variant="outline" size="sm" className="justify-start">
                      Feeding Records
                    </Button>
                    <Button variant="outline" size="sm" className="justify-start">
                      Financial Data
                    </Button>
                    <Button variant="outline" size="sm" className="justify-start">
                      Inventory
                    </Button>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label className="text-base">Import Data</Label>
                  <p className="text-sm text-muted-foreground">Import data from CSV or Excel files.</p>
                  <div className="mt-2 space-y-2">
                    <Select defaultValue="goats">
                      <SelectTrigger id="import-type">
                        <SelectValue placeholder="Select data type to import" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="goats">Goat Registry</SelectItem>
                        <SelectItem value="health">Health Records</SelectItem>
                        <SelectItem value="breeding">Breeding Data</SelectItem>
                        <SelectItem value="feeding">Feeding Records</SelectItem>
                        <SelectItem value="financial">Financial Data</SelectItem>
                        <SelectItem value="inventory">Inventory</SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="flex flex-col gap-2 sm:flex-row">
                      <Input type="file" accept=".csv,.xlsx,.xls" />
                      <Button variant="outline" className="flex items-center gap-2">
                        <LucideUpload className="h-4 w-4" />
                        Import
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Management */}
        <TabsContent value="users" className="space-y-4">
          <PermissionGuard permission="manage_users">
            <Card>
              <CardHeader>
                <CardTitle>User Management</CardTitle>
                <CardDescription>Manage user accounts and permissions.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Farm Users</h3>
                <Button
                  size="sm"
                  className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white"
                  onClick={() => setIsAddUserModalOpen(true)}
                >
                  <LucideUserPlus className="h-4 w-4" />
                  Add User
                </Button>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-red-800 mb-4">
                  <div className="flex items-center gap-2">
                    <LucideAlertCircle className="h-4 w-4" />
                    <span className="font-medium">Error</span>
                  </div>
                  <p className="text-sm mt-1">{error}</p>

                  {/* Database connection error help */}
                  {error.includes('Database connection') || error.includes('database') || error.includes('connection') ? (
                    <div className="mt-2 p-2 bg-red-100 rounded text-sm">
                      <p className="font-medium">Troubleshooting steps:</p>
                      <ol className="list-decimal ml-4 mt-1 space-y-1">
                        <li>Check that your database server is running</li>
                        <li>Verify your database credentials in the environment variables</li>
                        <li>Ensure the database 'goat_management' exists</li>
                        <li>Check that the 'users' table has been created with the correct schema</li>
                      </ol>
                    </div>
                  ) : null}

                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2 border-red-300 text-red-700 hover:bg-red-100"
                    onClick={fetchUsers}
                  >
                    <LucideRefreshCw className="h-3 w-3 mr-2" />
                    Retry
                  </Button>
                </div>
              )}

              <div className="rounded-md border">
                <div className="relative w-full overflow-auto">
                  <table className="w-full caption-bottom text-sm">
                    <thead>
                      <tr className="border-b bg-muted/50 transition-colors">
                        <th className="h-12 px-4 text-left align-middle font-medium">Username</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Full Name</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Email</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Role</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Last Login</th>
                        <th className="h-12 px-4 text-left align-middle font-medium">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {isLoading ? (
                        // Loading skeleton
                        Array(3).fill(0).map((_, i) => (
                          <tr key={i} className="border-b">
                            <td className="p-4 align-middle"><div className="h-4 w-24 bg-gray-200 animate-pulse rounded"></div></td>
                            <td className="p-4 align-middle"><div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div></td>
                            <td className="p-4 align-middle"><div className="h-4 w-40 bg-gray-200 animate-pulse rounded"></div></td>
                            <td className="p-4 align-middle"><div className="h-4 w-16 bg-gray-200 animate-pulse rounded"></div></td>
                            <td className="p-4 align-middle"><div className="h-4 w-16 bg-gray-200 animate-pulse rounded"></div></td>
                            <td className="p-4 align-middle"><div className="h-4 w-24 bg-gray-200 animate-pulse rounded"></div></td>
                            <td className="p-4 align-middle">
                              <div className="flex space-x-2">
                                <div className="h-8 w-16 bg-gray-200 animate-pulse rounded"></div>
                                <div className="h-8 w-20 bg-gray-200 animate-pulse rounded"></div>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : users.length > 0 ? (
                        users.map((user) => (
                          <tr key={user.id} className="border-b transition-colors hover:bg-muted/50">
                            <td className="p-4 align-middle font-medium">{user.username}</td>
                            <td className="p-4 align-middle">{user.full_name || '-'}</td>
                            <td className="p-4 align-middle">{user.email || '-'}</td>
                            <td className="p-4 align-middle">
                              <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
                                ${user.role === 'Admin' ? 'bg-purple-100 text-purple-800' :
                                  user.role === 'Manager' ? 'bg-blue-100 text-blue-800' :
                                  user.role === 'Staff' ? 'bg-green-100 text-green-800' :
                                  'bg-gray-100 text-gray-800'}`}
                              >
                                {user.role}
                              </span>
                            </td>
                            <td className="p-4 align-middle">
                              <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
                                ${user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                              >
                                {user.is_active ? (
                                  <LucideUserCheck className="mr-1 h-3 w-3" />
                                ) : (
                                  <LucideUserX className="mr-1 h-3 w-3" />
                                )}
                                {user.is_active ? 'Active' : 'Inactive'}
                              </span>
                            </td>
                            <td className="p-4 align-middle">
                              {user.last_login ? new Date(user.last_login).toLocaleString() : 'Never'}
                            </td>
                            <td className="p-4 align-middle">
                              <div className="flex space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditUser(user)}
                                  className="flex items-center gap-1"
                                >
                                  <LucideEdit className="h-3.5 w-3.5" />
                                  Edit
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className={`${user.is_active ? 'text-amber-500 hover:text-amber-700' : 'text-green-500 hover:text-green-700'} flex items-center gap-1`}
                                  onClick={() => handleToggleActivation(user)}
                                >
                                  {user.is_active ? (
                                    <LucideToggleRight className="h-3.5 w-3.5" />
                                  ) : (
                                    <LucideToggleLeft className="h-3.5 w-3.5" />
                                  )}
                                  {user.is_active ? 'Deactivate' : 'Activate'}
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={7} className="p-8 text-center">
                            <div className="flex flex-col items-center justify-center">
                              <LucideUsers className="h-10 w-10 text-muted-foreground mb-3" />
                              <h3 className="text-lg font-medium mb-1">No users found</h3>
                              <p className="text-muted-foreground mb-4">Add your first user to get started</p>
                              <Button
                                onClick={() => setIsAddUserModalOpen(true)}
                                className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white"
                              >
                                <LucideUserPlus className="mr-2 h-4 w-4" />
                                Add User
                              </Button>
                            </div>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Add User Modal */}
              <AddUserModal
                isOpen={isAddUserModalOpen}
                onClose={() => setIsAddUserModalOpen(false)}
                onSuccess={handleAddUserSuccess}
              />

              {/* Edit User Modal */}
              <EditUserModal
                isOpen={isEditUserModalOpen}
                onClose={() => setIsEditUserModalOpen(false)}
                onSuccess={handleUserActionSuccess}
                userId={selectedUserId}
              />

              {/* Toggle User Activation Modal */}
              <ToggleUserActivationModal
                isOpen={isToggleActivationModalOpen}
                onClose={() => setIsToggleActivationModalOpen(false)}
                onSuccess={handleUserActionSuccess}
                userId={selectedUserId}
                username={selectedUsername}
                isCurrentlyActive={selectedUserActive}
              />
            </CardContent>
          </Card>
          </PermissionGuard>

          <PermissionGuard permission="manage_users">
          <Card>
            <CardHeader>
              <CardTitle>Role Management</CardTitle>
              <CardDescription>Configure user roles and permissions.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                    <LucideShield className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="font-medium">Manage User Roles</h4>
                    <p className="text-sm text-muted-foreground">Configure permissions for Admin, Manager, and Staff roles</p>
                  </div>
                </div>
                <Link href="/settings/roles">
                  <Button className="flex items-center gap-2">
                    <LucideUserCog className="h-4 w-4" />
                    Manage Roles
                  </Button>
                </Link>
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div className="flex items-center gap-2 p-3 border rounded-lg">
                  <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                    <LucideShield className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">Admin</p>
                    <p className="text-xs text-muted-foreground">Full access</p>
                  </div>
                </div>

                <div className="flex items-center gap-2 p-3 border rounded-lg">
                  <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <LucideShield className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">Manager</p>
                    <p className="text-xs text-muted-foreground">Most features</p>
                  </div>
                </div>

                <div className="flex items-center gap-2 p-3 border rounded-lg">
                  <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                    <LucideShield className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">Staff</p>
                    <p className="text-xs text-muted-foreground">Daily operations</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          </PermissionGuard>
        </TabsContent>
      </Tabs>
    </div>
  )
}

