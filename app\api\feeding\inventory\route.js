import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

export async function POST(request) {
  try {
    const data = await request.json();
    console.log('Received feed inventory data:', data);
    
    // Validate required fields
    if (!data.name || !data.category || !data.quantity || !data.unit) {
      console.log('Validation failed: Missing required fields');
      return NextResponse.json(
        { error: 'Missing required fields: name, category, quantity, and unit are required' },
        { status: 400 }
      );
    }
    
    // Generate a unique ID for the feed inventory item
    const timestamp = new Date().getTime();
    const feedId = `FEED-${timestamp}`;
    console.log('Generated feed ID:', feedId);
    
    // Log the SQL parameters for debugging
    const sqlParams = [
      feedId,
      data.name,
      data.category,
      data.quantity,
      data.unit,
      data.location || null,
      data.minLevel || null,
      data.maxLevel || null,
      data.protein || null,
      data.fiber || null,
      data.fat || null,
      data.moisture || null,
      data.price || null,
      data.supplier || null,
      data.purchaseDate || null,
      data.expiryDate || null,
      data.notes || null
    ];
    console.log('SQL parameters:', sqlParams);
    
    // Insert data into the feed_inventory table
    const [result] = await pool.execute(
      `INSERT INTO feed_inventory (
        id, name, category, quantity, unit, 
        location, min_level, max_level, 
        protein, fiber, fat, moisture,
        price, supplier, purchase_date, expiry_date, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      sqlParams
    );
    
    console.log('Database insert result:', result);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Feed inventory item added successfully',
      id: feedId
    });
    
  } catch (error) {
    console.error('Error creating feed inventory item:', error);
    return NextResponse.json(
      { error: `Failed to create feed inventory item: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Query to get all feed inventory items
    const [rows] = await pool.execute(`
      SELECT * FROM feed_inventory
      ORDER BY created_at DESC
    `);
    
    return NextResponse.json(rows);
  } catch (error) {
    console.error('Error fetching feed inventory items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch feed inventory items' },
      { status: 500 }
    );
  }
}
