import { NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

export async function GET() {
  let connection

  try {
    // Create direct connection (no pool)
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management',
    })

    console.log('Database connection established')

    // Test if table exists
    const [tables] = await connection.execute("SHOW TABLES LIKE 'report_generations'")
    console.log('Table exists:', tables.length > 0)

    if (tables.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Table report_generations does not exist',
        tables_found: tables
      })
    }

    // Get table structure
    const [structure] = await connection.execute("DESCRIBE report_generations")
    console.log('Table structure:', structure)

    // Count total records
    const [countResult] = await connection.execute('SELECT COUNT(*) as count FROM report_generations')
    const totalCount = countResult[0].count
    console.log('Total records:', totalCount)

    // Get all records (raw)
    const [allRecords] = await connection.execute('SELECT * FROM report_generations ORDER BY generated_at DESC')
    console.log('All records:', allRecords)

    // Get recent 5 records with formatting
    const [recentRecords] = await connection.execute(`
      SELECT 
        id,
        report_type,
        date_range,
        generated_by,
        filters,
        file_name,
        file_format,
        generated_at,
        DATE_FORMAT(generated_at, '%Y-%m-%d %H:%i:%s') as formatted_date
      FROM report_generations 
      ORDER BY generated_at DESC 
      LIMIT 5
    `)

    // Format for display
    const formattedReports = recentRecords.map(report => {
      const reportTypeFormatted = report.report_type.charAt(0).toUpperCase() + report.report_type.slice(1)
      
      return {
        id: report.id,
        report_type: reportTypeFormatted,
        title: `${reportTypeFormatted} Report (${report.date_range} days)`,
        summary: `Generated as ${report.file_format?.toUpperCase() || 'PDF'} by ${report.generated_by}`,
        date: report.formatted_date,
        file_name: report.file_name,
        file_format: report.file_format,
        tab_key: report.report_type.toLowerCase(),
        downloadable: true,
        raw_data: report
      }
    })

    return NextResponse.json({
      success: true,
      database_connected: true,
      table_exists: true,
      table_structure: structure,
      total_records: totalCount,
      recent_records_raw: recentRecords,
      formatted_reports: formattedReports,
      all_records: allRecords
    })

  } catch (error) {
    console.error('Debug API error:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 })
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}
