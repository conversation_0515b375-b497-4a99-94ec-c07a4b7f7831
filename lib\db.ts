import mysql from 'mysql2/promise';

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Database utility object with error handling
export const db = {
  /**
   * Execute a SQL query with parameters
   * @param sql The SQL query to execute
   * @param params Optional parameters for the query
   * @returns Promise resolving to the query results
   */
  query: async (sql: string, params?: any[]) => {
    try {
      const [results] = await pool.query(sql, params);
      return results;
    } catch (error: any) {
      console.error('Database query error:', error.message);
      // Add query info to the error for better debugging
      error.query = sql;
      error.params = params;
      throw error;
    }
  },

  /**
   * Get a dedicated connection from the pool
   * @returns A database connection
   */
  getConnection: async () => {
    try {
      return await pool.getConnection();
    } catch (error: any) {
      console.error('Database connection error:', error.message);
      throw error;
    }
  },

  /**
   * Check if the database connection is working
   * @returns True if connected, throws an error otherwise
   */
  testConnection: async () => {
    try {
      await pool.query('SELECT 1');
      return true;
    } catch (error: any) {
      console.error('Database connection test failed:', error.message);
      throw error;
    }
  }
};

// Export the pool for direct access if needed
export default pool;
