import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import mysql from 'mysql2/promise'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

// Database connection
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management'
}

interface CalendarEvent {
  id: string
  title: string
  date: string
  time?: string
  type: 'health' | 'breeding' | 'feeding' | 'financial' | 'general'
  status: 'upcoming' | 'today' | 'overdue' | 'completed'
  description?: string
  goatId?: number
  goatName?: string
  priority: 'low' | 'medium' | 'high'
}

// Helper function to determine event status
function getEventStatus(date: string): 'upcoming' | 'today' | 'overdue' {
  const eventDate = new Date(date)
  const today = new Date()

  // Reset time to compare only dates
  eventDate.setHours(0, 0, 0, 0)
  today.setHours(0, 0, 0, 0)

  if (eventDate.getTime() === today.getTime()) {
    return 'today'
  } else if (eventDate < today) {
    return 'overdue'
  } else {
    return 'upcoming'
  }
}

// Helper function to determine priority based on event type and urgency
function getEventPriority(type: string, status: string): 'low' | 'medium' | 'high' {
  if (status === 'overdue') return 'high'
  if (status === 'today') return 'high'
  if (type === 'health') return 'high'
  if (type === 'breeding') return 'medium'
  return 'low'
}

export async function GET(request: NextRequest) {
  try {
    console.log('Calendar API: Fetching calendar events')

    // Verify JWT token
    const authHeader = request.headers.get('authorization')
    const token = authHeader?.replace('Bearer ', '') || request.cookies.get('token')?.value

    if (!token) {
      console.log('Calendar API: No token provided')
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    let decoded: any
    try {
      decoded = jwt.verify(token, JWT_SECRET)
      console.log('Calendar API: Token verified for user:', decoded.username)
    } catch (jwtError) {
      console.log('Calendar API: Invalid token')
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Create database connection
    const connection = await mysql.createConnection(dbConfig)

    try {
      const events: CalendarEvent[] = []

      // Fetch health-related events
      console.log('Calendar API: Fetching health events')

      // Health follow-up appointments
      const [healthFollowUpRows] = await connection.execute(`
        SELECT
          hr.id,
          hr.record_date as date,
          hr.record_type,
          hr.diagnosis,
          hr.treatment,
          hr.follow_up_date,
          g.name as goat_name,
          g.id as goat_id
        FROM health_records hr
        JOIN goats g ON hr.goat_id = g.id
        WHERE hr.follow_up_date IS NOT NULL
          AND hr.follow_up_date >= CURDATE() - INTERVAL 7 DAY
          AND hr.follow_up_date <= CURDATE() + INTERVAL 60 DAY
        ORDER BY hr.follow_up_date ASC
      `) as any[]

      healthFollowUpRows.forEach((row: any) => {
        const eventDate = new Date(row.follow_up_date).toISOString().split('T')[0]
        const status = getEventStatus(eventDate)

        events.push({
          id: `health_followup_${row.id}`,
          title: `${row.record_type} Follow-up - ${row.goat_name}`,
          date: eventDate,
          type: 'health',
          status,
          description: `Follow-up for ${row.record_type.toLowerCase()}: ${row.diagnosis || row.treatment}`,
          goatId: row.goat_id,
          goatName: row.goat_name,
          priority: getEventPriority('health', status)
        })
      })

      // Scheduled health events (vaccinations, deworming, etc.)
      const [scheduledHealthRows] = await connection.execute(`
        SELECT
          g.id as goat_id,
          g.name as goat_name,
          g.birth_date,
          CASE
            WHEN DATEDIFF(CURDATE(), g.birth_date) >= 365 THEN
              DATE_ADD(CURDATE(), INTERVAL (365 - (DATEDIFF(CURDATE(), g.birth_date) % 365)) DAY)
            ELSE
              DATE_ADD(g.birth_date, INTERVAL 365 DAY)
          END as next_vaccination_date,
          CASE
            WHEN DATEDIFF(CURDATE(), g.birth_date) >= 90 THEN
              DATE_ADD(CURDATE(), INTERVAL (90 - (DATEDIFF(CURDATE(), g.birth_date) % 90)) DAY)
            ELSE
              DATE_ADD(g.birth_date, INTERVAL 90 DAY)
          END as next_deworming_date
        FROM goats g
        WHERE g.status IN ('Healthy', 'Pregnant', 'Lactating')
          AND g.birth_date IS NOT NULL
      `) as any[]

      scheduledHealthRows.forEach((row: any) => {
        // Add vaccination events
        if (row.next_vaccination_date) {
          const vaccDate = new Date(row.next_vaccination_date).toISOString().split('T')[0]
          const vaccStatus = getEventStatus(vaccDate)

          events.push({
            id: `vaccination_${row.goat_id}_${vaccDate}`,
            title: `Annual Vaccination - ${row.goat_name}`,
            date: vaccDate,
            type: 'health',
            status: vaccStatus,
            description: `Annual vaccination due for ${row.goat_name}`,
            goatId: row.goat_id,
            goatName: row.goat_name,
            priority: getEventPriority('health', vaccStatus)
          })
        }

        // Add deworming events
        if (row.next_deworming_date) {
          const dewormDate = new Date(row.next_deworming_date).toISOString().split('T')[0]
          const dewormStatus = getEventStatus(dewormDate)

          events.push({
            id: `deworming_${row.goat_id}_${dewormDate}`,
            title: `Deworming - ${row.goat_name}`,
            date: dewormDate,
            type: 'health',
            status: dewormStatus,
            description: `Quarterly deworming due for ${row.goat_name}`,
            goatId: row.goat_id,
            goatName: row.goat_name,
            priority: getEventPriority('health', dewormStatus)
          })
        }
      })

      // Fetch breeding-related events
      console.log('Calendar API: Fetching breeding events')

      // Expected kidding dates from breeding records
      const [breedingRows] = await connection.execute(`
        SELECT
          br.id,
          br.expected_kidding_date,
          br.breeding_date,
          br.status,
          br.notes,
          doe.name as doe_name,
          doe.id as doe_id,
          buck.name as buck_name
        FROM breeding_records br
        JOIN goats doe ON br.doe_id = doe.id
        LEFT JOIN goats buck ON br.buck_id = buck.id
        WHERE br.expected_kidding_date IS NOT NULL
          AND br.expected_kidding_date >= CURDATE() - INTERVAL 7 DAY
          AND br.expected_kidding_date <= CURDATE() + INTERVAL 90 DAY
          AND br.status IN ('Pending', 'Confirmed')
        ORDER BY br.expected_kidding_date ASC
      `) as any[]

      breedingRows.forEach((row: any) => {
        const eventDate = new Date(row.expected_kidding_date).toISOString().split('T')[0]
        const status = getEventStatus(eventDate)

        events.push({
          id: `kidding_${row.id}`,
          title: `Expected Kidding - ${row.doe_name}`,
          date: eventDate,
          type: 'breeding',
          status,
          description: `Expected kidding date for ${row.doe_name}${row.buck_name ? ` (bred with ${row.buck_name})` : ''}${row.notes ? `. Notes: ${row.notes}` : ''}`,
          goatId: row.doe_id,
          goatName: row.doe_name,
          priority: getEventPriority('breeding', status)
        })
      })

      // Breeding check-ups (30 days after breeding)
      const [breedingCheckRows] = await connection.execute(`
        SELECT
          br.id,
          br.breeding_date,
          DATE_ADD(br.breeding_date, INTERVAL 30 DAY) as check_date,
          br.status,
          doe.name as doe_name,
          doe.id as doe_id,
          buck.name as buck_name
        FROM breeding_records br
        JOIN goats doe ON br.doe_id = doe.id
        LEFT JOIN goats buck ON br.buck_id = buck.id
        WHERE br.breeding_date IS NOT NULL
          AND DATE_ADD(br.breeding_date, INTERVAL 30 DAY) >= CURDATE() - INTERVAL 7 DAY
          AND DATE_ADD(br.breeding_date, INTERVAL 30 DAY) <= CURDATE() + INTERVAL 30 DAY
          AND br.status = 'Pending'
        ORDER BY check_date ASC
      `) as any[]

      breedingCheckRows.forEach((row: any) => {
        const eventDate = new Date(row.check_date).toISOString().split('T')[0]
        const status = getEventStatus(eventDate)

        events.push({
          id: `breeding_check_${row.id}`,
          title: `Pregnancy Check - ${row.doe_name}`,
          date: eventDate,
          type: 'breeding',
          status,
          description: `30-day pregnancy check for ${row.doe_name}${row.buck_name ? ` (bred with ${row.buck_name})` : ''}`,
          goatId: row.doe_id,
          goatName: row.doe_name,
          priority: getEventPriority('breeding', status)
        })
      })

      // Fetch heat cycle events (planned breeding opportunities)
      const [heatCycleRows] = await connection.execute(`
        SELECT
          hc.id,
          hc.heat_date,
          hc.planned_breeding_date,
          hc.intensity,
          hc.notes,
          g.name as goat_name,
          g.id as goat_id
        FROM heat_cycles hc
        JOIN goats g ON hc.goat_id = g.id
        WHERE hc.planned_breeding_date IS NOT NULL
          AND hc.planned_breeding_date >= CURDATE() - INTERVAL 3 DAY
          AND hc.planned_breeding_date <= CURDATE() + INTERVAL 45 DAY
          AND g.status IN ('Healthy', 'Lactating')
        ORDER BY hc.planned_breeding_date ASC
      `) as any[]

      heatCycleRows.forEach((row: any) => {
        const eventDate = new Date(row.planned_breeding_date).toISOString().split('T')[0]
        const status = getEventStatus(eventDate)

        events.push({
          id: `heat_cycle_${row.id}`,
          title: `Planned Breeding - ${row.goat_name}`,
          date: eventDate,
          type: 'breeding',
          status,
          description: `Planned breeding for ${row.goat_name} (${row.intensity} heat intensity).${row.notes ? ` Notes: ${row.notes}` : ''}`,
          goatId: row.goat_id,
          goatName: row.goat_name,
          priority: getEventPriority('breeding', status)
        })
      })

      // Fetch feeding-related events
      console.log('Calendar API: Fetching feeding events')

      // Recent feeding records
      const [feedingRecordRows] = await connection.execute(`
        SELECT
          fr.id,
          fr.date,
          fr.time,
          fr.record_type,
          fr.consumption_level,
          fr.notes,
          fr.recorded_by
        FROM feeding_records fr
        WHERE fr.date >= CURDATE() - INTERVAL 3 DAY
          AND fr.date <= CURDATE() + INTERVAL 30 DAY
        ORDER BY fr.date ASC, fr.time ASC
      `) as any[]

      feedingRecordRows.forEach((row: any) => {
        const eventDate = new Date(row.date).toISOString().split('T')[0]
        const status = getEventStatus(eventDate)

        events.push({
          id: `feeding_record_${row.id}`,
          title: `Feeding Record - ${row.record_type}`,
          date: eventDate,
          type: 'feeding',
          status,
          description: `${row.record_type} feeding record${row.consumption_level ? ` (${row.consumption_level} consumption)` : ''}${row.notes ? `. ${row.notes}` : ''}. Recorded by: ${row.recorded_by}`,
          priority: getEventPriority('feeding', status)
        })
      })

      // Feed inventory alerts (low stock warnings)
      const [feedInventoryRows] = await connection.execute(`
        SELECT
          fi.id,
          fi.name as feed_name,
          fi.quantity as current_stock,
          fi.min_level as minimum_stock,
          fi.unit,
          fi.expiry_date,
          fi.category
        FROM feed_items fi
        WHERE (fi.quantity <= fi.min_level OR fi.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 14 DAY))
          AND fi.quantity > 0
        ORDER BY fi.expiry_date ASC, fi.quantity ASC
      `) as any[]

      feedInventoryRows.forEach((row: any) => {
        if (row.expiry_date) {
          const eventDate = new Date(row.expiry_date).toISOString().split('T')[0]
          const status = getEventStatus(eventDate)

          events.push({
            id: `feed_expiry_${row.id}`,
            title: `Feed Expiring - ${row.feed_name}`,
            date: eventDate,
            type: 'feeding',
            status,
            description: `${row.feed_name} (${row.category}) expires on this date. Current stock: ${row.current_stock} ${row.unit}`,
            priority: getEventPriority('feeding', status)
          })
        }

        if (row.current_stock <= row.minimum_stock) {
          // Create low stock alert for tomorrow
          const tomorrow = new Date()
          tomorrow.setDate(tomorrow.getDate() + 1)
          const eventDate = tomorrow.toISOString().split('T')[0]

          events.push({
            id: `feed_low_stock_${row.id}`,
            title: `Low Stock Alert - ${row.feed_name}`,
            date: eventDate,
            type: 'feeding',
            status: 'upcoming',
            description: `${row.feed_name} (${row.category}) is running low. Current: ${row.current_stock} ${row.unit}, Minimum: ${row.minimum_stock} ${row.unit}`,
            priority: 'high'
          })
        }
      })

      // Fetch financial events (recurring transactions)
      console.log('Calendar API: Fetching financial events')
      const [financialRows] = await connection.execute(`
        SELECT
          ft.id,
          ft.transaction_date,
          ft.description,
          ft.amount,
          ft.transaction_type,
          ft.is_recurring
        FROM financial_transactions ft
        WHERE ft.is_recurring = 1
          AND ft.transaction_date >= CURDATE() - INTERVAL 30 DAY
          AND ft.transaction_date <= CURDATE() + INTERVAL 60 DAY
        ORDER BY ft.transaction_date ASC
      `) as any[]

      financialRows.forEach((row: any) => {
        const eventDate = new Date(row.transaction_date).toISOString().split('T')[0]
        const status = getEventStatus(eventDate)

        events.push({
          id: `financial_${row.id}`,
          title: `${row.transaction_type} - ${row.description}`,
          date: eventDate,
          type: 'financial',
          status,
          description: `Recurring ${row.transaction_type.toLowerCase()}: MWK ${row.amount}`,
          priority: getEventPriority('financial', status)
        })
      })

      // Fetch inventory-related events
      console.log('Calendar API: Fetching inventory events')
      const [inventoryRows] = await connection.execute(`
        SELECT
          ii.id,
          ii.name as item_name,
          ii.quantity as current_stock,
          ii.min_level as minimum_stock,
          ii.unit,
          ii.expiry_date,
          ii.category
        FROM inventory_items ii
        WHERE (ii.quantity <= ii.min_level OR ii.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 14 DAY))
          AND ii.quantity > 0
        ORDER BY ii.expiry_date ASC, ii.quantity ASC
      `) as any[]

      inventoryRows.forEach((row: any) => {
        if (row.expiry_date) {
          const eventDate = new Date(row.expiry_date).toISOString().split('T')[0]
          const status = getEventStatus(eventDate)

          events.push({
            id: `inventory_expiry_${row.id}`,
            title: `Inventory Expiring - ${row.item_name}`,
            date: eventDate,
            type: 'general',
            status,
            description: `${row.item_name} (${row.category}) expires on this date. Current stock: ${row.current_stock} ${row.unit}`,
            priority: getEventPriority('general', status)
          })
        }

        if (row.current_stock <= row.minimum_stock) {
          // Create low stock alert for tomorrow
          const tomorrow = new Date()
          tomorrow.setDate(tomorrow.getDate() + 1)
          const eventDate = tomorrow.toISOString().split('T')[0]

          events.push({
            id: `inventory_low_stock_${row.id}`,
            title: `Low Stock Alert - ${row.item_name}`,
            date: eventDate,
            type: 'general',
            status: 'upcoming',
            description: `${row.item_name} (${row.category}) is running low. Current: ${row.current_stock} ${row.unit}, Minimum: ${row.minimum_stock} ${row.unit}`,
            priority: 'high'
          })
        }
      })

      // Add scheduled farm maintenance events
      const today = new Date()
      const currentMonth = today.getMonth()
      const currentYear = today.getFullYear()

      // Monthly farm inspection (15th of each month)
      const inspectionDate = new Date(currentYear, currentMonth, 15)
      if (inspectionDate >= new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)) {
        const eventDate = inspectionDate.toISOString().split('T')[0]
        const status = getEventStatus(eventDate)

        events.push({
          id: `inspection_${currentYear}_${currentMonth}`,
          title: 'Monthly Farm Inspection',
          date: eventDate,
          type: 'general',
          status,
          description: 'Monthly comprehensive farm inspection and maintenance check',
          priority: getEventPriority('general', status)
        })
      }

      // Weekly feed quality check (every Sunday)
      const nextSunday = new Date(today)
      nextSunday.setDate(today.getDate() + (7 - today.getDay()) % 7)
      if (nextSunday.getTime() - today.getTime() <= 7 * 24 * 60 * 60 * 1000) {
        const eventDate = nextSunday.toISOString().split('T')[0]
        const status = getEventStatus(eventDate)

        events.push({
          id: `feed_check_${nextSunday.getTime()}`,
          title: 'Weekly Feed Quality Check',
          date: eventDate,
          type: 'feeding',
          status,
          description: 'Weekly feed inventory and quality assessment',
          priority: getEventPriority('feeding', status)
        })
      }

      console.log(`Calendar API: Found ${events.length} events`)

      return NextResponse.json({
        events: events.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()),
        summary: {
          total: events.length,
          today: events.filter(e => e.status === 'today').length,
          overdue: events.filter(e => e.status === 'overdue').length,
          upcoming: events.filter(e => e.status === 'upcoming').length,
          byType: {
            health: events.filter(e => e.type === 'health').length,
            breeding: events.filter(e => e.type === 'breeding').length,
            feeding: events.filter(e => e.type === 'feeding').length,
            financial: events.filter(e => e.type === 'financial').length,
            general: events.filter(e => e.type === 'general').length
          }
        }
      })

    } finally {
      await connection.end()
    }

  } catch (error) {
    console.error('Calendar API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle other HTTP methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to fetch calendar events.' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to fetch calendar events.' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use GET to fetch calendar events.' },
    { status: 405 }
  )
}
