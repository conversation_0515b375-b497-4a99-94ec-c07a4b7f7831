'use client'

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON>alogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LoadingButton } from "@/components/ui/loading-button"
import { useToast } from "@/components/ui/use-toast"
import { LucideAlertCircle, LucideLoader2 } from "lucide-react"

interface User {
  id: number;
  username: string;
  email: string | null;
  full_name: string | null;
  role: string;
}

interface EditUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  userId: number | null;
}

export function EditUserModal({ isOpen, onClose, onSuccess, userId }: EditUserModalProps) {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [isFetching, setIsFetching] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Form state
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: '',
    email: '',
    full_name: '',
    role: ''
  })
  
  // Form validation state
  const [validationErrors, setValidationErrors] = useState({
    username: '',
    password: '',
    confirmPassword: '',
    email: ''
  })
  
  // Fetch user data when modal opens
  useEffect(() => {
    if (isOpen && userId) {
      fetchUserData()
    }
  }, [isOpen, userId])
  
  // Fetch user data
  const fetchUserData = async () => {
    if (!userId) return
    
    setIsFetching(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/users/${userId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch user: ${response.status}`)
      }
      
      const userData: User = await response.json()
      
      setFormData({
        username: userData.username,
        password: '',
        confirmPassword: '',
        email: userData.email || '',
        full_name: userData.full_name || '',
        role: userData.role
      })
    } catch (err: any) {
      console.error('Error fetching user:', err)
      setError(err.message)
      toast({
        title: "Error",
        description: "Failed to load user data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsFetching(false)
    }
  }
  
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    
    // Clear validation errors when user types
    if (validationErrors[name as keyof typeof validationErrors]) {
      setValidationErrors(prev => ({ ...prev, [name]: '' }))
    }
  }
  
  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }
  
  // Validate form
  const validateForm = () => {
    let isValid = true
    const errors = {
      username: '',
      password: '',
      confirmPassword: '',
      email: ''
    }
    
    // Username validation
    if (!formData.username.trim()) {
      errors.username = 'Username is required'
      isValid = false
    } else if (formData.username.length < 3) {
      errors.username = 'Username must be at least 3 characters'
      isValid = false
    }
    
    // Password validation (only if password is being changed)
    if (formData.password) {
      if (formData.password.length < 6) {
        errors.password = 'Password must be at least 6 characters'
        isValid = false
      }
      
      // Confirm password validation
      if (formData.password !== formData.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match'
        isValid = false
      }
    }
    
    // Email validation (optional field)
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address'
      isValid = false
    }
    
    setValidationErrors(errors)
    return isValid
  }
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    if (!validateForm() || !userId) {
      return
    }
    
    setIsLoading(true)
    setError(null)
    
    try {
      // Prepare data for API
      const userData: any = {
        username: formData.username,
        email: formData.email || null,
        full_name: formData.full_name || null,
        role: formData.role
      }
      
      // Only include password if it was changed
      if (formData.password) {
        userData.password = formData.password
      }
      
      // Call API to update user
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update user')
      }
      
      // Show success message
      toast({
        title: "Success",
        description: "User has been updated successfully",
        variant: "default",
      })
      
      // Reset form and close modal
      onSuccess()
      onClose()
      
    } catch (err: any) {
      console.error('Error updating user:', err)
      setError(err.message)
      
      toast({
        title: "Error",
        description: err.message,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  // Handle modal close
  const handleClose = () => {
    setFormData({
      username: '',
      password: '',
      confirmPassword: '',
      email: '',
      full_name: '',
      role: ''
    })
    setValidationErrors({
      username: '',
      password: '',
      confirmPassword: '',
      email: ''
    })
    setError(null)
    onClose()
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
          <DialogDescription>
            Update user account information and permissions.
          </DialogDescription>
        </DialogHeader>
        
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-red-800 mb-4">
            <div className="flex items-center gap-2">
              <LucideAlertCircle className="h-4 w-4" />
              <span className="font-medium">Error</span>
            </div>
            <p className="text-sm mt-1">{error}</p>
          </div>
        )}
        
        {isFetching ? (
          <div className="py-8 flex justify-center items-center">
            <div className="flex flex-col items-center gap-2">
              <LucideLoader2 className="h-8 w-8 text-purple-500 animate-spin" />
              <p className="text-sm text-muted-foreground">Loading user data...</p>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="username">
                  Username <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="username"
                  name="username"
                  value={formData.username}
                  onChange={handleChange}
                  placeholder="johndoe"
                  className={validationErrors.username ? "border-red-500" : ""}
                />
                {validationErrors.username && (
                  <p className="text-sm text-red-500">{validationErrors.username}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="full_name">Full Name</Label>
                <Input
                  id="full_name"
                  name="full_name"
                  value={formData.full_name}
                  onChange={handleChange}
                  placeholder="John Doe"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                className={validationErrors.email ? "border-red-500" : ""}
              />
              {validationErrors.email && (
                <p className="text-sm text-red-500">{validationErrors.email}</p>
              )}
            </div>
            
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="password">
                  New Password <span className="text-sm text-muted-foreground">(optional)</span>
                </Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleChange}
                  className={validationErrors.password ? "border-red-500" : ""}
                  placeholder="Leave blank to keep current"
                />
                {validationErrors.password && (
                  <p className="text-sm text-red-500">{validationErrors.password}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">
                  Confirm New Password
                </Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className={validationErrors.confirmPassword ? "border-red-500" : ""}
                  disabled={!formData.password}
                />
                {validationErrors.confirmPassword && (
                  <p className="text-sm text-red-500">{validationErrors.confirmPassword}</p>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="role">
                Role <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.role}
                onValueChange={(value) => handleSelectChange('role', value)}
              >
                <SelectTrigger id="role">
                  <SelectValue placeholder="Select user role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Admin">Admin</SelectItem>
                  <SelectItem value="Manager">Manager</SelectItem>
                  <SelectItem value="Staff">Staff</SelectItem>
                  <SelectItem value="Viewer">Viewer</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <DialogFooter className="mt-6">
              <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                Cancel
              </Button>
              <LoadingButton type="submit" isLoading={isLoading}>
                Update User
              </LoadingButton>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}
