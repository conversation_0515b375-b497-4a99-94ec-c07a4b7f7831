'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  LucideCalendarDays,
  LucideChevronLeft,
  LucideChevronRight,
  LucideHeart,
  LucideCalendarClock,
  LucidePill,
  LucideWheat,
  LucideCoins,
  LucideAlertTriangle,
  LucideCheck,
  LucideClock,
  LucideArrowLeft,
  LucideFilter,
  LucidePlus
} from 'lucide-react'
import Link from 'next/link'
import DashboardLayout from '@/components/dashboard-layout'

interface CalendarEvent {
  id: string
  title: string
  date: string
  time?: string
  type: 'health' | 'breeding' | 'feeding' | 'financial' | 'general'
  status: 'upcoming' | 'today' | 'overdue' | 'completed'
  description?: string
  goatId?: number
  goatName?: string
  priority: 'low' | 'medium' | 'high'
}

const eventTypeConfig = {
  health: {
    icon: LucideHeart,
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    borderColor: 'border-red-200',
    label: 'Health'
  },
  breeding: {
    icon: LucideCalendarClock,
    color: 'text-pink-600',
    bgColor: 'bg-pink-100',
    borderColor: 'border-pink-200',
    label: 'Breeding'
  },
  feeding: {
    icon: LucideWheat,
    color: 'text-amber-600',
    bgColor: 'bg-amber-100',
    borderColor: 'border-amber-200',
    label: 'Feeding'
  },
  financial: {
    icon: LucideCoins,
    color: 'text-cyan-600',
    bgColor: 'bg-cyan-100',
    borderColor: 'border-cyan-200',
    label: 'Financial'
  },
  general: {
    icon: LucideCalendarDays,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    borderColor: 'border-gray-200',
    label: 'General'
  }
}

const statusConfig = {
  upcoming: {
    icon: LucideClock,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    label: 'Upcoming'
  },
  today: {
    icon: LucideAlertTriangle,
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    label: 'Today'
  },
  overdue: {
    icon: LucideAlertTriangle,
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    label: 'Overdue'
  },
  completed: {
    icon: LucideCheck,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    label: 'Completed'
  }
}

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [filteredEvents, setFilteredEvents] = useState<CalendarEvent[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('calendar')
  const [eventTypeFilter, setEventTypeFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()

    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())

    const days = []
    const currentDateObj = new Date(startDate)

    for (let i = 0; i < 42; i++) {
      days.push(new Date(currentDateObj))
      currentDateObj.setDate(currentDateObj.getDate() + 1)
    }

    return days
  }

  // Get events for a specific date
  const getEventsForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0]
    return filteredEvents.filter(event => event.date === dateStr)
  }

  // Check if date is today
  const isToday = (date: Date) => {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  // Check if date is in current month
  const isCurrentMonth = (date: Date) => {
    return date.getMonth() === currentDate.getMonth()
  }

  // Navigate months
  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(newDate.getMonth() - 1)
      } else {
        newDate.setMonth(newDate.getMonth() + 1)
      }
      return newDate
    })
  }

  // Fetch calendar events
  useEffect(() => {
    const fetchEvents = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // Get token from localStorage for API request
        const token = localStorage.getItem('token')

        // If no token, show authentication required message
        if (!token) {
          console.log('Calendar: No authentication token found')
          setError('Authentication required - please log in to view calendar events')
          setEvents([])
          return
        }

        const headers: HeadersInit = {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }

        const response = await fetch('/api/calendar', {
          method: 'GET',
          headers,
          credentials: 'include' // Include cookies for authentication
        })

        if (!response.ok) {
          if (response.status === 401) {
            console.log('Calendar: Authentication expired')
            setError('Authentication expired - please log in again')
          } else {
            console.log(`Calendar: API error ${response.status}`)
            setError(`Failed to load calendar events (Error ${response.status})`)
          }
          setEvents([])
          return
        }

        const data = await response.json()
        setEvents(data.events || [])
        console.log('Calendar: Successfully loaded events from API:', data.events?.length || 0)

        // Clear any previous errors
        setError(null)
      } catch (err: any) {
        console.error('Error fetching calendar events:', err)
        setError('Failed to load calendar events - please try again')
        setEvents([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchEvents()
  }, [])

  // Filter events based on selected filters
  useEffect(() => {
    let filtered = events

    if (eventTypeFilter !== 'all') {
      filtered = filtered.filter(event => event.type === eventTypeFilter)
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(event => event.status === statusFilter)
    }

    setFilteredEvents(filtered)
  }, [events, eventTypeFilter, statusFilter])

  const calendarDays = generateCalendarDays()
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm" className="flex items-center gap-2">
                <LucideArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Farm Calendar</h1>
              <p className="text-muted-foreground">
                Manage schedules, events, and important farm dates
              </p>
            </div>
          </div>
          <Button className="bg-green-600 hover:bg-green-700">
            <LucidePlus className="mr-2 h-4 w-4" />
            Add Event
          </Button>
        </div>

        {/* Error state */}
        {error && !isLoading && (
          <div className={`border rounded-lg p-4 ${
            error.includes('Authentication')
              ? 'bg-red-50 border-red-200'
              : 'bg-yellow-50 border-yellow-200'
          }`}>
            <div className={`flex items-center gap-2 ${
              error.includes('Authentication')
                ? 'text-red-800'
                : 'text-yellow-800'
            }`}>
              <LucideAlertTriangle className="h-5 w-5" />
              <p className="font-medium">
                {error.includes('Authentication') ? 'Authentication Required' : 'Calendar Notice'}
              </p>
            </div>
            <p className={`text-sm mt-1 ${
              error.includes('Authentication')
                ? 'text-red-700'
                : 'text-yellow-700'
            }`}>
              {error}
            </p>
          </div>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="calendar">Calendar View</TabsTrigger>
            <TabsTrigger value="list">List View</TabsTrigger>
            <TabsTrigger value="upcoming">Upcoming Events</TabsTrigger>
          </TabsList>

          {/* Filters */}
          <div className="flex gap-4">
            <Select value={eventTypeFilter} onValueChange={setEventTypeFilter}>
              <SelectTrigger className="w-48">
                <LucideFilter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="health">Health</SelectItem>
                <SelectItem value="breeding">Breeding</SelectItem>
                <SelectItem value="feeding">Feeding</SelectItem>
                <SelectItem value="financial">Financial</SelectItem>
                <SelectItem value="general">General</SelectItem>
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <LucideFilter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="upcoming">Upcoming</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="overdue">Overdue</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Calendar View */}
          <TabsContent value="calendar" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <LucideCalendarDays className="h-5 w-5" />
                    {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
                  </CardTitle>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigateMonth('prev')}
                    >
                      <LucideChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentDate(new Date())}
                    >
                      Today
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigateMonth('next')}
                    >
                      <LucideChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Calendar Grid */}
                <div className="grid grid-cols-7 gap-1">
                  {/* Day headers */}
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                    <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                      {day}
                    </div>
                  ))}

                  {/* Calendar days */}
                  {calendarDays.map((day, index) => {
                    const dayEvents = getEventsForDate(day)
                    const isCurrentMonthDay = isCurrentMonth(day)
                    const isTodayDay = isToday(day)

                    return (
                      <div
                        key={index}
                        className={`
                          min-h-24 p-1 border border-gray-100 cursor-pointer transition-colors
                          ${isCurrentMonthDay ? 'bg-white hover:bg-gray-50' : 'bg-gray-50 text-gray-400'}
                          ${isTodayDay ? 'bg-blue-50 border-blue-200' : ''}
                          ${selectedDate?.toDateString() === day.toDateString() ? 'bg-green-50 border-green-200' : ''}
                        `}
                        onClick={() => setSelectedDate(day)}
                      >
                        <div className={`text-sm ${isTodayDay ? 'font-bold text-blue-600' : ''}`}>
                          {day.getDate()}
                        </div>
                        <div className="space-y-1 mt-1">
                          {dayEvents.slice(0, 2).map(event => {
                            const config = eventTypeConfig[event.type]
                            return (
                              <div
                                key={event.id}
                                className={`text-xs p-1 rounded ${config.bgColor} ${config.color} truncate`}
                                title={event.title}
                              >
                                {event.title}
                              </div>
                            )
                          })}
                          {dayEvents.length > 2 && (
                            <div className="text-xs text-gray-500">
                              +{dayEvents.length - 2} more
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Selected Date Events */}
            {selectedDate && (
              <Card>
                <CardHeader>
                  <CardTitle>
                    Events for {selectedDate.toLocaleDateString()}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {(() => {
                    const dayEvents = getEventsForDate(selectedDate)
                    if (dayEvents.length === 0) {
                      return (
                        <p className="text-gray-500 text-center py-4">
                          No events scheduled for this date
                        </p>
                      )
                    }

                    return (
                      <div className="space-y-3">
                        {dayEvents.map(event => {
                          const typeConfig = eventTypeConfig[event.type]
                          const statusConfigItem = statusConfig[event.status]

                          return (
                            <div
                              key={event.id}
                              className={`p-4 rounded-lg border ${typeConfig.borderColor} ${typeConfig.bgColor}`}
                            >
                              <div className="flex items-start justify-between">
                                <div className="flex items-start gap-3">
                                  <typeConfig.icon className={`h-5 w-5 ${typeConfig.color} mt-0.5`} />
                                  <div>
                                    <h4 className="font-medium">{event.title}</h4>
                                    {event.time && (
                                      <p className="text-sm text-gray-600">{event.time}</p>
                                    )}
                                    {event.description && (
                                      <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                                    )}
                                    {event.goatName && (
                                      <p className="text-sm text-gray-600">Goat: {event.goatName}</p>
                                    )}
                                  </div>
                                </div>
                                <div className="flex gap-2">
                                  <Badge variant="outline" className={statusConfigItem.bgColor}>
                                    {statusConfigItem.label}
                                  </Badge>
                                  <Badge variant="outline">
                                    {typeConfig.label}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    )
                  })()}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* List View */}
          <TabsContent value="list" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>All Events</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
                    <p className="text-gray-500 mt-2">Loading events...</p>
                  </div>
                ) : filteredEvents.length === 0 ? (
                  <div className="text-center py-8">
                    <LucideCalendarDays className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No events found</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredEvents
                      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
                      .map(event => {
                        const typeConfig = eventTypeConfig[event.type]
                        const statusConfigItem = statusConfig[event.status]

                        return (
                          <div
                            key={event.id}
                            className={`p-4 rounded-lg border ${typeConfig.borderColor} ${typeConfig.bgColor}`}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex items-start gap-3">
                                <typeConfig.icon className={`h-5 w-5 ${typeConfig.color} mt-0.5`} />
                                <div>
                                  <h4 className="font-medium">{event.title}</h4>
                                  <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                                    <span>{new Date(event.date).toLocaleDateString()}</span>
                                    {event.time && <span>{event.time}</span>}
                                  </div>
                                  {event.description && (
                                    <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                                  )}
                                  {event.goatName && (
                                    <p className="text-sm text-gray-600">Goat: {event.goatName}</p>
                                  )}
                                </div>
                              </div>
                              <div className="flex gap-2">
                                <Badge variant="outline" className={statusConfigItem.bgColor}>
                                  {statusConfigItem.label}
                                </Badge>
                                <Badge variant="outline">
                                  {typeConfig.label}
                                </Badge>
                                {event.priority === 'high' && (
                                  <Badge variant="destructive">High Priority</Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        )
                      })}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Upcoming Events */}
          <TabsContent value="upcoming" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {/* Today's Events */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-orange-600">
                    <LucideAlertTriangle className="h-5 w-5" />
                    Today's Events
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {(() => {
                    const todayEvents = filteredEvents.filter(event => event.status === 'today')
                    if (todayEvents.length === 0) {
                      return <p className="text-gray-500 text-sm">No events today</p>
                    }
                    return (
                      <div className="space-y-2">
                        {todayEvents.map(event => {
                          const config = eventTypeConfig[event.type]
                          return (
                            <div key={event.id} className="flex items-center gap-2 p-2 rounded bg-orange-50">
                              <config.icon className={`h-4 w-4 ${config.color}`} />
                              <div className="flex-1">
                                <p className="text-sm font-medium">{event.title}</p>
                                {event.time && <p className="text-xs text-gray-600">{event.time}</p>}
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    )
                  })()}
                </CardContent>
              </Card>

              {/* Overdue Events */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-600">
                    <LucideAlertTriangle className="h-5 w-5" />
                    Overdue Events
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {(() => {
                    const overdueEvents = filteredEvents.filter(event => event.status === 'overdue')
                    if (overdueEvents.length === 0) {
                      return <p className="text-gray-500 text-sm">No overdue events</p>
                    }
                    return (
                      <div className="space-y-2">
                        {overdueEvents.map(event => {
                          const config = eventTypeConfig[event.type]
                          return (
                            <div key={event.id} className="flex items-center gap-2 p-2 rounded bg-red-50">
                              <config.icon className={`h-4 w-4 ${config.color}`} />
                              <div className="flex-1">
                                <p className="text-sm font-medium">{event.title}</p>
                                <p className="text-xs text-gray-600">{new Date(event.date).toLocaleDateString()}</p>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    )
                  })()}
                </CardContent>
              </Card>

              {/* Upcoming This Week */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-blue-600">
                    <LucideClock className="h-5 w-5" />
                    This Week
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {(() => {
                    const today = new Date()
                    const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
                    const thisWeekEvents = filteredEvents.filter(event => {
                      const eventDate = new Date(event.date)
                      return eventDate >= today && eventDate <= weekFromNow && event.status === 'upcoming'
                    })

                    if (thisWeekEvents.length === 0) {
                      return <p className="text-gray-500 text-sm">No upcoming events this week</p>
                    }
                    return (
                      <div className="space-y-2">
                        {thisWeekEvents.slice(0, 5).map(event => {
                          const config = eventTypeConfig[event.type]
                          return (
                            <div key={event.id} className="flex items-center gap-2 p-2 rounded bg-blue-50">
                              <config.icon className={`h-4 w-4 ${config.color}`} />
                              <div className="flex-1">
                                <p className="text-sm font-medium">{event.title}</p>
                                <p className="text-xs text-gray-600">{new Date(event.date).toLocaleDateString()}</p>
                              </div>
                            </div>
                          )
                        })}
                        {thisWeekEvents.length > 5 && (
                          <p className="text-xs text-gray-500 text-center">
                            +{thisWeekEvents.length - 5} more events
                          </p>
                        )}
                      </div>
                    )
                  })()}
                </CardContent>
              </Card>
            </div>

            {/* Quick Stats */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                      <LucideHeart className="h-5 w-5 text-red-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Health Events</p>
                      <p className="text-2xl font-bold">
                        {filteredEvents.filter(e => e.type === 'health').length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-pink-100 flex items-center justify-center">
                      <LucideCalendarClock className="h-5 w-5 text-pink-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Breeding Events</p>
                      <p className="text-2xl font-bold">
                        {filteredEvents.filter(e => e.type === 'breeding').length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                      <LucideWheat className="h-5 w-5 text-amber-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Feeding Events</p>
                      <p className="text-2xl font-bold">
                        {filteredEvents.filter(e => e.type === 'feeding').length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                      <LucideAlertTriangle className="h-5 w-5 text-red-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Overdue</p>
                      <p className="text-2xl font-bold">
                        {filteredEvents.filter(e => e.status === 'overdue').length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
