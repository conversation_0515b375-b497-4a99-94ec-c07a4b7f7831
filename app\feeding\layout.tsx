"use client"

import { ReactNode } from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import DashboardLayout from "@/components/dashboard-layout"
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"

export default function FeedingLayout({
  children,
}: {
  children: ReactNode
}) {
  const pathname = usePathname()

  // Determine if we're on a page that should show tabs
  const shouldShowTabs = () => {
    // Don't show tabs on add/edit pages
    return !pathname.includes("/add") &&
           !pathname.includes("/edit") &&
           !pathname.includes("/record") &&
           !pathname.includes("/inventory/add");
  }

  return (
    <DashboardLayout>
      {children}
    </DashboardLayout>
  )
}
