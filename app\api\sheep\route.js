import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const gender = searchParams.get('gender');

    // Create a connection for this request
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    });

    // Build query with optional gender filter
    let query = `
      SELECT id, name, tag_number as tag, breed, gender, 
             TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) as age, status
      FROM animals
      WHERE animal_type = 'Sheep'
    `;
    const queryParams = [];

    if (gender && gender !== 'all') {
      query += ' AND gender = ?';
      queryParams.push(gender);
    }

    query += ' ORDER BY name';

    const [rows] = await connection.execute(query, queryParams);

    // Calculate stats
    const stats = {
      total: rows.length,
      healthy: rows.filter(sheep => sheep.status === 'Healthy').length,
      sick: rows.filter(sheep => sheep.status === 'Sick').length,
      injured: rows.filter(sheep => sheep.status === 'Injured').length,
      quarantined: rows.filter(sheep => sheep.status === 'Quarantined').length,
      males: rows.filter(sheep => sheep.gender === 'Male').length,
      females: rows.filter(sheep => sheep.gender === 'Female').length
    };

    await connection.end();

    return NextResponse.json({
      sheep: rows,
      stats: stats
    });
  } catch (error) {
    console.error('Error fetching sheep:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sheep' },
      { status: 500 }
    );
  }
}
