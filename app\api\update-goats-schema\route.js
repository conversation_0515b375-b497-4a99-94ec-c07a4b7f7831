import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

export async function GET() {
  try {
    // Create a connection for this request
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    });
    
    // Alter the goats table to update the status ENUM
    await connection.execute(`
      ALTER TABLE goats 
      MODIFY COLUMN status ENUM('Healthy', 'Sick', 'Pregnant', 'Lactating', 'Deceased', 'Injured', 'Quarantined') 
      NOT NULL DEFAULT 'Healthy'
    `);
    
    await connection.end();
    
    return NextResponse.json({ 
      success: true, 
      message: "Goats table schema updated successfully" 
    });
  } catch (error) {
    console.error('Error updating goats schema:', error);
    return NextResponse.json(
      { error: 'Failed to update goats schema: ' + error.message },
      { status: 500 }
    );
  }
}
