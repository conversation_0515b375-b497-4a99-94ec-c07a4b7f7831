import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool with improved configuration
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0,
  // Add timeout settings
  connectTimeout: 10000, // 10 seconds
  // Handle binary data properly
  supportBigNumbers: true,
  bigNumberStrings: true,
  // Ensure proper character encoding
  charset: 'utf8mb4',
});

export async function GET(request) {
  let connection;

  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30days'; // Default to 30 days
    const category = searchParams.get('category') || 'all';
    const type = searchParams.get('type') || 'all';
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Get a connection from the pool
    connection = await pool.getConnection();

    try {
      // Build the date filter based on the period - more explicit approach
      let dateFilter = '';

      // Initialize query parameters array
      let baseParams = [];

      if (startDate && endDate) {
        dateFilter = `AND transaction_date BETWEEN ? AND ?`;
        baseParams.push(startDate, endDate);
      } else {
        // Use fixed date filters without parameters to avoid issues
        switch (period) {
          case '7days':
            dateFilter = 'AND transaction_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)';
            break;
          case '30days':
            dateFilter = 'AND transaction_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
            break;
          case '90days':
            dateFilter = 'AND transaction_date >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)';
            break;
          case 'year':
            dateFilter = 'AND YEAR(transaction_date) = YEAR(CURDATE())';
            break;
          case 'all':
            dateFilter = '';
            break;
          default:
            dateFilter = 'AND transaction_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
        }
      }

      // Build the category filter
      const categoryFilter = category !== 'all' ? 'AND category = ?' : '';

      // Build the transaction type filter
      const typeFilter = type !== 'all' ? 'AND transaction_type = ?' : '';

      // Log the filter settings for debugging
      console.log('Filters:', { period, category, type, dateFilter, categoryFilter, typeFilter });

      // Safely construct queries with proper parameter handling
      try {
        // Get all transactions with filters - handle each filter case separately
        let transactionsQuery;
        let transactionParams;

        if (category !== 'all' && type !== 'all') {
          // Both category and type filters
          transactionsQuery = `
            SELECT
              id,
              transaction_type,
              amount,
              transaction_date,
              description,
              category,
              payment_method,
              reference_number,
              notes,
              is_recurring,
              receipt_path,
              DATE_FORMAT(transaction_date, '%Y-%m-%d') as formatted_date
            FROM financial_transactions
            WHERE 1=1 ${dateFilter} AND category = ? AND transaction_type = ?
            ORDER BY transaction_date DESC, id DESC
            LIMIT 500
          `;

          transactionParams = startDate && endDate
            ? [startDate, endDate, category, type]
            : [category, type];
        } else if (category !== 'all') {
          // Only category filter
          transactionsQuery = `
            SELECT
              id,
              transaction_type,
              amount,
              transaction_date,
              description,
              category,
              payment_method,
              reference_number,
              notes,
              is_recurring,
              receipt_path,
              DATE_FORMAT(transaction_date, '%Y-%m-%d') as formatted_date
            FROM financial_transactions
            WHERE 1=1 ${dateFilter} AND category = ?
            ORDER BY transaction_date DESC, id DESC
            LIMIT 500
          `;

          transactionParams = startDate && endDate
            ? [startDate, endDate, category]
            : [category];
        } else if (type !== 'all') {
          // Only type filter
          transactionsQuery = `
            SELECT
              id,
              transaction_type,
              amount,
              transaction_date,
              description,
              category,
              payment_method,
              reference_number,
              notes,
              is_recurring,
              receipt_path,
              DATE_FORMAT(transaction_date, '%Y-%m-%d') as formatted_date
            FROM financial_transactions
            WHERE 1=1 ${dateFilter} AND transaction_type = ?
            ORDER BY transaction_date DESC, id DESC
            LIMIT 500
          `;

          transactionParams = startDate && endDate
            ? [startDate, endDate, type]
            : [type];
        } else {
          // No specific filters
          transactionsQuery = `
            SELECT
              id,
              transaction_type,
              amount,
              transaction_date,
              description,
              category,
              payment_method,
              reference_number,
              notes,
              is_recurring,
              receipt_path,
              DATE_FORMAT(transaction_date, '%Y-%m-%d') as formatted_date
            FROM financial_transactions
            WHERE 1=1 ${dateFilter}
            ORDER BY transaction_date DESC, id DESC
            LIMIT 500
          `;

          transactionParams = startDate && endDate
            ? [startDate, endDate]
            : [];
        }

        const [transactions] = await connection.execute(transactionsQuery, transactionParams);

        // Get summary statistics with a simpler approach to avoid malformed packet issues
        let summaryQuery;
        let summaryParams;

        if (category !== 'all') {
          // For category filter, use a simpler query structure
          summaryQuery = `
            SELECT
              COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END), 0) as total_income,
              COALESCE(SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END), 0) as total_expenses,
              COUNT(CASE WHEN transaction_type = 'income' THEN 1 ELSE NULL END) as income_count,
              COUNT(CASE WHEN transaction_type = 'expense' THEN 1 ELSE NULL END) as expense_count
            FROM financial_transactions
            WHERE 1=1 ${dateFilter} AND category = ?
          `;

          // Only use the category parameter
          summaryParams = startDate && endDate
            ? [startDate, endDate, category]
            : [category];
        } else {
          // For non-category filters, use the standard approach
          summaryQuery = `
            SELECT
              COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END), 0) as total_income,
              COALESCE(SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END), 0) as total_expenses,
              COUNT(CASE WHEN transaction_type = 'income' THEN 1 ELSE NULL END) as income_count,
              COUNT(CASE WHEN transaction_type = 'expense' THEN 1 ELSE NULL END) as expense_count
            FROM financial_transactions
            WHERE 1=1 ${dateFilter} ${typeFilter}
          `;

          // Use appropriate parameters based on filters
          summaryParams = [...baseParams];
          if (type !== 'all') {
            summaryParams.push(type);
          }
        }

        // Execute the summary query
        const [summary] = await connection.execute(summaryQuery, summaryParams);

        // Get category breakdown with a simpler approach
        let categoriesQuery;
        let categoryParams;

        if (category !== 'all') {
          // For specific category, just get that category's data
          categoriesQuery = `
            SELECT
              category,
              transaction_type,
              SUM(amount) as total,
              COUNT(*) as count
            FROM financial_transactions
            WHERE 1=1 ${dateFilter} AND category = ?
            GROUP BY category, transaction_type
            ORDER BY total DESC
          `;

          // Only use the category parameter
          categoryParams = startDate && endDate
            ? [startDate, endDate, category]
            : [category];
        } else {
          // For all categories
          categoriesQuery = `
            SELECT
              category,
              transaction_type,
              SUM(amount) as total,
              COUNT(*) as count
            FROM financial_transactions
            WHERE 1=1 ${dateFilter} ${typeFilter}
            GROUP BY category, transaction_type
            ORDER BY total DESC
          `;

          // Use appropriate parameters
          categoryParams = [...baseParams];
          if (type !== 'all') {
            categoryParams.push(type);
          }
        }

        const [categories] = await connection.execute(categoriesQuery, categoryParams);

        // Get monthly trends - no parameters needed
        const monthlyTrendsQuery = `
          SELECT
            DATE_FORMAT(transaction_date, '%Y-%m') as month,
            transaction_type,
            SUM(amount) as total
          FROM financial_transactions
          WHERE transaction_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
          GROUP BY month, transaction_type
          ORDER BY month
        `;

        const [monthlyTrends] = await connection.execute(monthlyTrendsQuery);

        // Get unique categories - no parameters needed
        const uniqueCategoriesQuery = `
          SELECT DISTINCT category FROM financial_transactions ORDER BY category
        `;

        const [uniqueCategories] = await connection.execute(uniqueCategoriesQuery);

        // Process the results to ensure proper data types
        const processedTransactions = transactions.map(tx => ({
          ...tx,
          amount: Number(tx.amount),
          is_recurring: Boolean(tx.is_recurring)
        }));

        const processedSummary = {
          total_income: Number(summary[0]?.total_income || 0),
          total_expenses: Number(summary[0]?.total_expenses || 0),
          income_count: Number(summary[0]?.income_count || 0),
          expense_count: Number(summary[0]?.expense_count || 0)
        };

        const processedCategories = categories.map(cat => ({
          ...cat,
          total: Number(cat.total),
          count: Number(cat.count)
        }));

        const processedMonthlyTrends = monthlyTrends.map(trend => ({
          ...trend,
          total: Number(trend.total)
        }));

        // Calculate net profit
        const netProfit = processedSummary.total_income - processedSummary.total_expenses;

        // Return the processed data
        return NextResponse.json({
          transactions: processedTransactions,
          summary: {
            ...processedSummary,
            net_profit: netProfit,
            transaction_count: processedSummary.income_count + processedSummary.expense_count
          },
          categories: processedCategories,
          monthlyTrends: processedMonthlyTrends,
          uniqueCategories: uniqueCategories.map(c => c.category)
        });
      } catch (queryError) {
        console.error('Error executing specific query:', queryError);
        return NextResponse.json(
          {
            error: 'Error executing database query: ' +
              (queryError.message || 'Unknown query error') +
              (queryError.code ? ` (Code: ${queryError.code})` : '')
          },
          { status: 500 }
        );
      }
    } catch (error) {
      console.error('Error executing database queries:', error);
      return NextResponse.json(
        { error: 'Database query error: ' + (error.message || 'Unknown database error') },
        { status: 500 }
      );
    } finally {
      // Release the connection if it was obtained
      if (connection) {
        try {
          connection.release();
        } catch (releaseError) {
          console.error('Error releasing database connection:', releaseError);
        }
      }
    }
  } catch (error) {
    console.error('Error fetching financial transactions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch financial transactions: ' + (error.message || 'Unknown error') },
      { status: 500 }
    );
  }
}
