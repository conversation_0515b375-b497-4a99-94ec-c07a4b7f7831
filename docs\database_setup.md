# Database Setup Guide

This guide will help you set up the database for the Goat Farm Management System.

## Prerequisites

- MySQL Server 5.7+ or MariaDB 10.2+
- MySQL client or command-line tools

## Step 1: Create the Database

Connect to your MySQL server and create the database:

```sql
CREATE DATABASE goat_management;
USE goat_management;
```

## Step 2: Create the Users Table

Create the users table with the required schema:

```sql
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(191) DEFAULT NULL,
  `full_name` varchar(255) DEFAULT NULL,
  `role` enum('Admin','Manager','Staff','Viewer') NOT NULL DEFAULT 'Viewer',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  <PERSON><PERSON><PERSON><PERSON> KEY (`id`),
  <PERSON>IQ<PERSON> KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

## Step 3: Create an Admin User

Create an initial admin user:

```sql
INSERT INTO users (
  username, 
  password_hash, 
  email, 
  full_name, 
  role, 
  is_active
) VALUES (
  'admin',
  -- This is a bcrypt hash for 'admin123' - you should change this in production
  '$2b$10$3euPcmQFCiblsZeEu5s7p.9MXXCg/gZJgwJLYaEj.kw9L9KOpoXO.',
  '<EMAIL>',
  'System Administrator',
  'Admin',
  1
);
```

## Step 4: Configure Environment Variables

Create a `.env` file in the root of your project with the following variables:

```
DB_HOST=localhost
DB_USER=your_mysql_username
DB_PASSWORD=your_mysql_password
DB_NAME=goat_management
```

## Troubleshooting Database Connection Issues

If you encounter database connection errors:

1. **Check MySQL Server**: Ensure your MySQL server is running:
   ```bash
   # For Linux/Mac
   sudo systemctl status mysql
   
   # For Windows
   net start mysql
   ```

2. **Verify Credentials**: Make sure your database credentials in the `.env` file are correct.

3. **Test Connection**: Try connecting to your database using the MySQL client:
   ```bash
   mysql -u your_username -p -h localhost goat_management
   ```

4. **Check Table Structure**: Verify the users table exists and has the correct structure:
   ```sql
   DESCRIBE users;
   ```

5. **Check Permissions**: Ensure your database user has the necessary permissions:
   ```sql
   GRANT ALL PRIVILEGES ON goat_management.* TO 'your_username'@'localhost';
   FLUSH PRIVILEGES;
   ```

## Running Database Migrations

If you need to update your database schema, use the migration script:

```bash
node scripts/run_migration.js
```

This will apply any pending migrations to your database.

## Database Backup

It's recommended to regularly backup your database:

```bash
# For Linux/Mac
mysqldump -u your_username -p goat_management > backup_$(date +%Y%m%d).sql

# For Windows
mysqldump -u your_username -p goat_management > backup_%date:~-4,4%%date:~-7,2%%date:~-10,2%.sql
```
