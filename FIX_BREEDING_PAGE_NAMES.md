# Fix: Animal Names Showing as "Unknown" in Breeding Page

## 🔍 Problem

In the Breeding page (`app/breeding/page.tsx`), animal names were showing as "Unknown" or "N/A" in:
- **Breeding Records tab**: <PERSON><PERSON> and <PERSON> columns
- **Heat Cycles tab**: Animal name column
- **Overview cards**: Breeding pairs display

## 🐛 Root Cause

The issue was a **field name mismatch** between API and frontend:

### API Returns:
1. **Breeding Records** (`/api/breeding-records`):
   - `female_name`, `male_name`
   - `female_tag`, `male_tag`
   - `female_type`, `male_type`

2. **Heat Cycles** (`/api/breeding-records`):
   - `animal_name`
   - `animal_tag`
   - `animal_type`

### Frontend Expected:
1. **Breeding Records**:
   - ❌ `doe_name`, `buck_name` (incorrect)

2. **Heat Cycles**:
   - ❌ `goat_name` (incorrect)

Since these fields didn't exist in the API response, they always returned `undefined`, triggering the fallback to "Unknown" or "N/A".

## ✅ Solution Applied

### 1. Fixed Search Filter (Line 197-206)

**Before:**
```tsx
const filtered = breedingData.records.filter((record: any) =>
  (record.doe_name && record.doe_name.toLowerCase().includes(lowercasedSearch)) ||
  (record.buck_name && record.buck_name.toLowerCase().includes(lowercasedSearch)) ||
  ...
)
```

**After:**
```tsx
const filtered = breedingData.records.filter((record: any) =>
  (record.female_name && record.female_name.toLowerCase().includes(lowercasedSearch)) ||
  (record.male_name && record.male_name.toLowerCase().includes(lowercasedSearch)) ||
  ...
)
```

### 2. Fixed Overview Cards (Line 576-584)

**Before:**
```tsx
<div className="font-medium">
  {breeding.doe_name} {breeding.buck_name ? `× ${breeding.buck_name}` : ''}
</div>
```

**After:**
```tsx
<div className="font-medium">
  {breeding.female_name} {breeding.male_name ? `× ${breeding.male_name}` : ''}
</div>
```

### 3. Fixed Breeding Records Table (Line 714-735)

**Before:**
```tsx
<TableCell className="font-medium">{record.doe_name || "Unknown"}</TableCell>
<TableCell>{record.buck_name || "N/A"}</TableCell>
```

**After:**
```tsx
<TableCell className="font-medium">
  <div className="flex flex-col">
    <span>{record.female_name || "Unknown"}</span>
    <span className="text-xs text-muted-foreground">#{record.female_tag}</span>
  </div>
</TableCell>
<TableCell>
  <div className="flex flex-col">
    <span>{record.male_name || "N/A"}</span>
    {record.male_tag && (
      <span className="text-xs text-muted-foreground">#{record.male_tag}</span>
    )}
  </div>
</TableCell>
```

### 4. Fixed Heat Cycles Table (Line 909-931)

**Before:**
```tsx
<TableCell className="font-medium">{cycle.goat_name || "Unknown"}</TableCell>
```

**After:**
```tsx
<TableCell className="font-medium">
  <div className="flex flex-col">
    <span>{cycle.animal_name || "Unknown"}</span>
    <span className="text-xs text-muted-foreground">#{cycle.animal_tag}</span>
  </div>
</TableCell>
```

### 5. Updated Table Headers for Consistency

**Breeding Records Table (Line 703-713):**
- Changed "Doe" → "Female"
- Changed "Buck" → "Male"
- Changed "Expected Kidding" → "Expected Birth"

**Heat Cycles Table (Line 898-908):**
- Changed "Doe" → "Animal"

**Reason:** The system handles multiple animal types (goats, sheep, cattle, pigs), so generic terms are more appropriate.

## 🎁 Bonus Improvements

1. ✅ **Added tag numbers**: Now displays animal tag numbers below names for easy identification
2. ✅ **Better formatting**: Two-line display with name and tag number
3. ✅ **Consistent styling**: Matches the health records page design
4. ✅ **Generic terminology**: "Female/Male" instead of "Doe/Buck" for multi-species support

## 📊 Changes Summary

| Section | Field Before | Field After | Display Enhancement |
|---------|-------------|-------------|---------------------|
| **Search Filter** | `doe_name`, `buck_name` | `female_name`, `male_name` | ✅ Fixed |
| **Overview Cards** | `doe_name`, `buck_name` | `female_name`, `male_name` | ✅ Fixed |
| **Breeding Records** | `doe_name`, `buck_name` | `female_name`, `male_name` | ✅ + Tag numbers |
| **Heat Cycles** | `goat_name` | `animal_name` | ✅ + Tag numbers |
| **Table Headers** | Doe/Buck | Female/Male/Animal | ✅ Generic terms |

## 🎯 Files Modified

- **`app/breeding/page.tsx`**
  - Line 197-206: Fixed search filter
  - Line 576-584: Fixed overview cards display
  - Line 703-713: Updated breeding records table headers
  - Line 714-735: Fixed breeding records table data display
  - Line 898-908: Updated heat cycles table header
  - Line 909-931: Fixed heat cycles table data display

## ✅ Testing

To verify the fix works:

1. **Navigate to Breeding page**: `/breeding`
2. **Check Overview tab**:
   - Recent breedings should show actual animal names (not "Unknown")
3. **Click "Breeding Records" tab**:
   - Female and Male columns should show actual names
   - Tag numbers should appear below names
4. **Click "Heat Cycles" tab**:
   - Animal column should show actual names
   - Tag numbers should appear below names
5. **Test search**:
   - Search by animal name should work correctly

## 🔍 API Endpoints Reference

### Breeding Records API (`/api/breeding-records`)

**SQL Query:**
```sql
SELECT
  br.*,
  female.name AS female_name,
  female.breed AS female_breed,
  female.animal_type AS female_type,
  female.tag_number AS female_tag,
  male.name AS male_name,
  male.breed AS male_breed,
  male.animal_type AS male_type,
  male.tag_number AS male_tag
FROM breeding_records br
LEFT JOIN animals female ON br.female_id = female.id
LEFT JOIN animals male ON br.male_id = male.id
```

**Returns:** Array of breeding records with joined animal data

### Heat Cycles Data (from breeding-records API)

**SQL Query:**
```sql
SELECT
  hc.*,
  a.name AS animal_name,
  a.breed AS animal_breed,
  a.tag_number AS animal_tag,
  a.animal_type AS animal_type
FROM heat_cycles hc
JOIN animals a ON hc.animal_id = a.id
```

**Returns:** Array of heat cycle records with joined animal data

## 💡 Why This Happened

This is a common issue when:
1. **Legacy field names**: Code originally used goat-specific terms (`doe_name`, `buck_name`, `goat_name`)
2. **Database refactoring**: Database was updated to support multiple animal types with generic field names
3. **Incomplete migration**: Frontend wasn't fully updated to match new API field names
4. **Copy-paste errors**: Code copied from goat-specific sections without updating field references

## 🎯 Prevention Tips

To prevent similar issues:

1. **Use TypeScript interfaces** for API responses
2. **Create shared types** for common data structures
3. **Test all tabs/views** after API changes
4. **Use consistent naming** across API and frontend
5. **Document field mappings** when refactoring

## 📝 Related Files

- **API**: `app/api/breeding-records/route.js`
- **API**: `app/api/breeding/route.js`
- **API**: `app/api/breeding/heat-cycles/route.js`
- **Frontend**: `app/breeding/page.tsx`

## 🎉 Result

All breeding page sections now correctly display:
- ✅ Female animal names (from database)
- ✅ Male animal names (from database)
- ✅ Tag numbers (for easy identification)
- ✅ Consistent formatting across all tabs
- ✅ Generic terminology (Female/Male instead of Doe/Buck)
- ✅ Proper fallback to "Unknown" only when data is truly missing
- ✅ Working search functionality

---

**Fix completed successfully!** 🚀

## 🔗 Related Fixes

This fix is similar to the one applied to the Health Records page:
- See: `FIX_ANIMAL_NAME_DISPLAY.md`

Both fixes address the same root cause: field name mismatches between API and frontend after database refactoring to support multiple animal types.

