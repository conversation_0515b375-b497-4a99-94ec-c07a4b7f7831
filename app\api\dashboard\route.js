import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET() {
  try {
    // Get a connection from the pool
    const connection = await pool.getConnection();
    
    try {
      // 1. Get animal statistics by type
      const [animalStats] = await connection.execute(`
        SELECT
          animal_type,
          COUNT(*) as total,
          SUM(CASE WHEN gender = 'Male' THEN 1 ELSE 0 END) as males,
          SUM(CASE WHEN gender = 'Female' THEN 1 ELSE 0 END) as females,
          SUM(CASE WHEN status = 'Healthy' THEN 1 ELSE 0 END) as healthy,
          SUM(CASE WHEN status = 'Sick' THEN 1 ELSE 0 END) as sick,
          SUM(CASE WHEN status = 'Pregnant' THEN 1 ELSE 0 END) as pregnant,
          SUM(CASE WHEN status = 'Lactating' THEN 1 ELSE 0 END) as lactating
        FROM animals
        GROUP BY animal_type
      `);

      // Process animal stats into organized structure
      const processedStats = {
        totalAnimals: 0,
        goats: { total: 0, males: 0, females: 0, healthy: 0, sick: 0, pregnant: 0, lactating: 0 },
        sheep: { total: 0, males: 0, females: 0, healthy: 0, sick: 0, pregnant: 0, lactating: 0 },
        cattle: { total: 0, males: 0, females: 0, healthy: 0, sick: 0, pregnant: 0, lactating: 0 },
        pigs: { total: 0, males: 0, females: 0, healthy: 0, sick: 0, pregnant: 0, lactating: 0 }
      };

      animalStats.forEach(stat => {
        let type = stat.animal_type.toLowerCase();
        // Handle plural mapping
        if (type === 'goat') type = 'goats';
        if (type === 'pig') type = 'pigs';

        if (processedStats[type]) {
          processedStats[type] = {
            total: parseInt(stat.total) || 0,
            males: parseInt(stat.males) || 0,
            females: parseInt(stat.females) || 0,
            healthy: parseInt(stat.healthy) || 0,
            sick: parseInt(stat.sick) || 0,
            pregnant: parseInt(stat.pregnant) || 0,
            lactating: parseInt(stat.lactating) || 0
          };
          processedStats.totalAnimals += parseInt(stat.total) || 0;
        }
      });

      // 2. Get breeding statistics
      const [breedingStats] = await connection.execute(`
        SELECT
          COUNT(CASE WHEN status = 'Pending' OR status = 'Confirmed' THEN 1 END) as activeBreedings,
          COUNT(CASE WHEN expected_kidding_date IS NOT NULL AND expected_kidding_date > CURDATE() THEN 1 END) as upcomingBirths
        FROM breeding_records
      `);

      // 3. Get financial metrics
      const currentDate = new Date();
      const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
      
      const formattedFirstDay = firstDayOfMonth.toISOString().split('T')[0];
      const formattedLastDay = lastDayOfMonth.toISOString().split('T')[0];
      
      const [financialMetrics] = await connection.execute(`
        SELECT 
          SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as monthlyIncome,
          SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END) as monthlyExpenses
        FROM financial_transactions
        WHERE transaction_date BETWEEN ? AND ?
      `, [formattedFirstDay, formattedLastDay]);
      
      // Calculate net profit and profit margin
      const monthlyIncome = financialMetrics[0].monthlyIncome || 0;
      const monthlyExpenses = financialMetrics[0].monthlyExpenses || 0;
      const netProfit = monthlyIncome - monthlyExpenses;
      const profitMargin = monthlyIncome > 0 ? Math.round((netProfit / monthlyIncome) * 100) : 0;

      // 4. Get feed stock levels
      const [feedStock] = await connection.execute(`
        SELECT 
          name,
          CASE 
            WHEN max_level > 0 THEN ROUND((quantity / max_level) * 100)
            ELSE 0
          END as stock_percentage
        FROM feed_items
        WHERE category = 'Feed'
        LIMIT 4
      `);
      
      // Format feed stock data
      const formattedFeedStock = {
        hay: 0,
        grain: 0,
        alfalfa: 0,
        minerals: 0
      };
      
      feedStock.forEach(item => {
        if (item.name.toLowerCase().includes('hay')) {
          formattedFeedStock.hay = item.stock_percentage;
        } else if (item.name.toLowerCase().includes('grain')) {
          formattedFeedStock.grain = item.stock_percentage;
        } else if (item.name.toLowerCase().includes('alfalfa')) {
          formattedFeedStock.alfalfa = item.stock_percentage;
        } else if (item.name.toLowerCase().includes('mineral')) {
          formattedFeedStock.minerals = item.stock_percentage;
        }
      });

      // 5. Get recent activities
      const [recentActivities] = await connection.execute(`
        (SELECT
          CONCAT('act-', id) as id,
          LOWER(animal_type) as type,
          CONCAT('Added new ', LOWER(animal_type), ': ', name) as title,
          created_at as timestamp,
          LOWER(animal_type) as module
        FROM animals
        ORDER BY created_at DESC
        LIMIT 3)
        
        UNION ALL
        
        (SELECT
          CONCAT('act-hr-', id) as id,
          'health' as type,
          CONCAT(record_type, ': ', (SELECT name FROM animals WHERE id = animal_id)) as title,
          record_date as timestamp,
          'health' as module
        FROM health_records
        WHERE animal_id IS NOT NULL
        ORDER BY record_date DESC
        LIMIT 2)
        
        UNION ALL
        
        (SELECT
          CONCAT('act-br-', id) as id,
          'breeding' as type,
          CONCAT('Breeding: ', (SELECT name FROM animals WHERE id = doe_id)) as title,
          breeding_date as timestamp,
          'breeding' as module
        FROM breeding_records
        WHERE doe_id IS NOT NULL
        ORDER BY breeding_date DESC
        LIMIT 2)
        
        UNION ALL
        
        (SELECT 
          CONCAT('act-ft-', id) as id,
          'finance' as type,
          CONCAT(transaction_type, ': ', description) as title,
          transaction_date as timestamp,
          'finance' as module
        FROM financial_transactions
        ORDER BY transaction_date DESC
        LIMIT 2)
        
        ORDER BY timestamp DESC
        LIMIT 6
      `);

      // 6. Get upcoming events
      const [upcomingEvents] = await connection.execute(`
        (SELECT 
          CONCAT('evt-hr-', id) as id,
          'health' as type,
          CONCAT(record_type, ' Follow-up') as title,
          follow_up_date as date,
          'health' as module
        FROM health_records
        WHERE follow_up_date >= CURDATE()
        ORDER BY follow_up_date ASC
        LIMIT 2)
        
        UNION ALL
        
        (SELECT
          CONCAT('evt-br-', id) as id,
          'breeding' as type,
          CONCAT('Expected Birth: ', (SELECT name FROM animals WHERE id = doe_id)) as title,
          expected_kidding_date as date,
          'breeding' as module
        FROM breeding_records
        WHERE expected_kidding_date >= CURDATE() AND doe_id IS NOT NULL
        ORDER BY expected_kidding_date ASC
        LIMIT 2)
        
        ORDER BY date ASC
        LIMIT 4
      `);

      // 7. Get alerts
      const [inventoryAlerts] = await connection.execute(`
        SELECT COUNT(*) as count
        FROM inventory_items
        WHERE quantity <= min_level
      `);
      
      const [healthAlerts] = await connection.execute(`
        SELECT COUNT(*) as count
        FROM health_records
        WHERE is_critical = 1 AND outcome IS NULL
      `);

      // Combine all data
      const dashboardData = {
        farmStats: {
          // Multi-animal statistics
          totalAnimals: processedStats.totalAnimals,

          // Direct access to animal types (for frontend)
          goats: processedStats.goats,
          sheep: processedStats.sheep,
          cattle: processedStats.cattle,
          pigs: processedStats.pigs,

          // Grouped structure (alternative access)
          animalsByType: {
            goats: processedStats.goats,
            sheep: processedStats.sheep,
            cattle: processedStats.cattle,
            pigs: processedStats.pigs
          },

          // Legacy goat-specific fields for backward compatibility
          totalGoats: processedStats.goats.total,
          healthyGoats: processedStats.goats.healthy,
          sickGoats: processedStats.goats.sick,
          pregnantGoats: processedStats.goats.pregnant,
          maleGoats: processedStats.goats.males,
          femaleGoats: processedStats.goats.females,

          activeBreedings: breedingStats[0]?.activeBreedings || 0,
          upcomingBirths: breedingStats[0]?.upcomingBirths || 0,
          upcomingKiddings: breedingStats[0]?.upcomingBirths || 0, // Legacy field

          feedStock: formattedFeedStock,

          financialMetrics: {
            monthlyIncome: monthlyIncome,
            monthlyExpenses: monthlyExpenses,
            netProfit: netProfit,
            profitMargin: profitMargin,
          },

          inventoryAlerts: inventoryAlerts[0].count || 0,
          healthAlerts: healthAlerts[0].count || 0,
        },
        recentActivities: recentActivities,
        upcomingEvents: upcomingEvents,
      };

      return NextResponse.json(dashboardData);
    } finally {
      // Release the connection
      connection.release();
    }
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data: ' + error.message },
      { status: 500 }
    );
  }
}
