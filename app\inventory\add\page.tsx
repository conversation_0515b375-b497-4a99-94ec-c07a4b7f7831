"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { DatePicker } from "@/components/ui/date-picker"
import { LoadingButton } from "@/components/ui/loading-button"
import { LucidePackage, LucideSave } from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import { toast } from "@/components/ui/use-toast"
import { BackButton } from "@/components/ui/back-button"

export default function AddInventoryItemPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    category: "",
    quantity: "",
    unit: "",
    minLevel: "",
    maxLevel: "",
    location: "",
    expiryDate: null,
    price: "",
    supplier: "",
    notes: "",
  })

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleDateChange = (date) => {
    // Format the date as YYYY-MM-DD if a date is selected
    if (date) {
      const formattedDate = date.toISOString().split('T')[0]
      console.log("Date selected and formatted:", formattedDate)
      setFormData((prev) => ({ ...prev, expiryDate: formattedDate }))
    } else {
      setFormData((prev) => ({ ...prev, expiryDate: null }))
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      console.log("Form submission started")

      // Process the form data
      const processedData = {
        ...formData,
        quantity: formData.quantity ? parseFloat(formData.quantity) : 0,
        minLevel: formData.minLevel ? parseFloat(formData.minLevel) : null,
        maxLevel: formData.maxLevel ? parseFloat(formData.maxLevel) : null,
        price: formData.price ? parseFloat(formData.price) : null
      }

      // Log the date for debugging
      console.log("Expiry date being sent:", formData.expiryDate)

      console.log("Processed data being sent to API:", processedData)

      // Send the data to the API
      const response = await fetch('/api/inventory/items', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(processedData),
        cache: 'no-store',
      })

      console.log("API response status:", response.status)

      const responseData = await response.json()
      console.log("API response data:", responseData)

      if (!response.ok) {
        throw new Error(responseData.error || `Failed with status ${response.status}`)
      }

      // Success handling
      toast({
        title: "Success",
        description: "Inventory item added successfully!",
      })

      // Add a small delay before redirecting to ensure the toast is seen
      setTimeout(() => {
        router.push("/inventory")
      }, 1000)

    } catch (error) {
      console.error('Error submitting form:', error)
      toast({
        title: "Error",
        description: `Failed to add inventory item: ${error.message}`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-accent">Add Inventory Item</h1>
          <BackButton href="/inventory" label="Back to Inventory" />
        </div>

        {/* Form */}
        <Card className="border-t-4 border-t-teal-500">
          <CardHeader>
            <div className="flex items-center gap-2">
              <LucidePackage className="h-6 w-6 text-teal-500" />
              <CardTitle>New Inventory Item</CardTitle>
            </div>
            <CardDescription>Add a new item to your inventory</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-teal-700">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">
                      Item Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="Enter item name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="input-accent"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">
                      Category <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => handleSelectChange("category", value)}
                      required
                    >
                      <SelectTrigger id="category" className="input-accent">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Feed">Feed</SelectItem>
                        <SelectItem value="Medication">Medication</SelectItem>
                        <SelectItem value="Equipment">Equipment</SelectItem>
                        <SelectItem value="Supplies">Supplies</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Quantity and Stock Levels */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-teal-700">Quantity and Stock Levels</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="quantity">
                      Quantity <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="quantity"
                      name="quantity"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="Enter quantity"
                      value={formData.quantity}
                      onChange={handleChange}
                      required
                      className="input-accent"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unit">
                      Unit <span className="text-red-500">*</span>
                    </Label>
                    <Select value={formData.unit} onValueChange={(value) => handleSelectChange("unit", value)} required>
                      <SelectTrigger id="unit" className="input-accent">
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="kg">Kilograms (kg)</SelectItem>
                        <SelectItem value="g">Grams (g)</SelectItem>
                        <SelectItem value="l">Liters (l)</SelectItem>
                        <SelectItem value="ml">Milliliters (ml)</SelectItem>
                        <SelectItem value="pieces">Pieces</SelectItem>
                        <SelectItem value="boxes">Boxes</SelectItem>
                        <SelectItem value="bottles">Bottles</SelectItem>
                        <SelectItem value="doses">Doses</SelectItem>
                        <SelectItem value="blocks">Blocks</SelectItem>
                        <SelectItem value="bales">Bales</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="location">Storage Location</Label>
                    <Input
                      id="location"
                      name="location"
                      placeholder="Enter storage location"
                      value={formData.location}
                      onChange={handleChange}
                      className="input-accent"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="minLevel">Minimum Stock Level</Label>
                    <Input
                      id="minLevel"
                      name="minLevel"
                      type="number"
                      min="0"
                      placeholder="Enter minimum level"
                      value={formData.minLevel}
                      onChange={handleChange}
                      className="input-accent"
                    />
                    <p className="text-xs text-muted-foreground">
                      You'll receive alerts when stock falls below this level
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxLevel">Maximum Stock Level</Label>
                    <Input
                      id="maxLevel"
                      name="maxLevel"
                      type="number"
                      min="0"
                      placeholder="Enter maximum level"
                      value={formData.maxLevel}
                      onChange={handleChange}
                      className="input-accent"
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-teal-700">Additional Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Unit Price</Label>
                    <Input
                      id="price"
                      name="price"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="Enter unit price"
                      value={formData.price}
                      onChange={handleChange}
                      className="input-accent"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="supplier">Supplier</Label>
                    <Input
                      id="supplier"
                      name="supplier"
                      placeholder="Enter supplier name"
                      value={formData.supplier}
                      onChange={handleChange}
                      className="input-accent"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="expiryDate">Expiry Date</Label>
                    <DatePicker
                      id="expiryDate"
                      selected={formData.expiryDate}
                      onSelect={handleDateChange}
                      className="input-accent"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    placeholder="Enter any additional notes about this item"
                    value={formData.notes}
                    onChange={handleChange}
                    className="input-accent min-h-[100px]"
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" onClick={() => router.push("/inventory")}>
                Cancel
              </Button>
              <LoadingButton
                type="submit"
                isLoading={isSubmitting}
                loadingText="Saving..."
                className="bg-gradient-to-r from-teal-500 to-cyan-500 hover:from-teal-600 hover:to-cyan-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
              >
                <LucideSave className="mr-2 h-4 w-4" />
                Save Item
              </LoadingButton>
            </CardFooter>
          </form>
        </Card>
      </div>
    </DashboardLayout>
  )
}

