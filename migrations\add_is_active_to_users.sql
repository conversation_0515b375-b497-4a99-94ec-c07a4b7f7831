-- Add is_active column to users table
ALTER TABLE `users` 
ADD COLUMN `is_active` TINYINT(1) NOT NULL DEFAULT 1 AFTER `last_login`;

-- Set all existing users to active
UPDATE `users` SET `is_active` = 1 WHERE `is_active` IS NULL;

-- Add an index on is_active for faster queries
ALTER TABLE `users` ADD INDEX `idx_is_active` (`is_active`);

-- Add a comment to document the column
ALTER TABLE `users` MODIFY COLUMN `is_active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT 'Flag indicating if the user account is active (1) or inactive (0)';
