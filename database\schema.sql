-- Database schema for Goat Management System

-- Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS goat_management;

-- Use the database
USE goat_management;

-- Goats table
CREATE TABLE IF NOT EXISTS goats (
  id VARCHAR(20) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  breed VARCHAR(50) NOT NULL,
  gender ENUM('Male', 'Female') NOT NULL,
  birth_date DATE,
  acquisition_date DATE,
  acquisition_price DECIMAL(10, 2),
  status ENUM('Healthy', 'Sick', 'Pregnant', 'Lactating', 'Deceased') NOT NULL DEFAULT 'Healthy',
  tag VARCHAR(20),
  weight DECIMAL(6, 2),
  color VARCHAR(50),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Health records table
CREATE TABLE IF NOT EXISTS health_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  goat_id VARCHAR(20) NOT NULL,
  record_date DATE NOT NULL,
  record_type ENUM('Vaccination', 'Treatment', 'Checkup', 'Medication', 'Other') NOT NULL,
  description TEXT NOT NULL,
  performed_by VARCHAR(100),
  cost DECIMAL(10, 2),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (goat_id) REFERENCES goats(id) ON DELETE CASCADE
);

-- Financial transactions table
CREATE TABLE IF NOT EXISTS financial_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  transaction_date DATE NOT NULL,
  transaction_type ENUM('Income', 'Expense') NOT NULL,
  category VARCHAR(50) NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  description TEXT,
  related_goat_id VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (related_goat_id) REFERENCES goats(id) ON DELETE SET NULL
);

-- Inventory transactions table
CREATE TABLE IF NOT EXISTS inventory_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  transaction_date DATE NOT NULL,
  item_name VARCHAR(100) NOT NULL,
  transaction_type ENUM('Purchase', 'Usage', 'Adjustment') NOT NULL,
  quantity DECIMAL(10, 2) NOT NULL,
  unit VARCHAR(20) NOT NULL,
  unit_price DECIMAL(10, 2),
  total_price DECIMAL(10, 2),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Sample data for goats
INSERT INTO goats (id, name, breed, gender, birth_date, acquisition_date, status, tag, weight, color, notes)
VALUES 
  ('G-2023-01', 'Bella', 'Boer', 'Female', '2021-05-15', '2021-08-20', 'Healthy', 'BT-001', 45.5, 'White and Brown', 'Good milk producer'),
  ('G-2023-02', 'Max', 'Alpine', 'Male', '2020-03-10', '2020-06-15', 'Healthy', 'AT-045', 65.2, 'Brown', 'Strong breeding male'),
  ('G-2023-03', 'Luna', 'Nubian', 'Female', '2022-02-20', '2022-05-10', 'Pregnant', 'NT-023', 40.8, 'Black and White', 'First pregnancy'),
  ('G-2023-04', 'Rocky', 'Boer', 'Male', '2019-01-05', '2019-04-12', 'Healthy', 'BT-089', 70.3, 'Brown and White', 'Experienced breeding male'),
  ('G-2023-05', 'Daisy', 'Saanen', 'Female', '2021-07-22', '2021-10-15', 'Lactating', 'ST-012', 42.1, 'White', 'High milk yield'),
  ('G-2023-06', 'Charlie', 'Alpine', 'Male', '2022-04-18', '2022-07-25', 'Sick', 'AT-056', 38.7, 'Gray', 'Recovering from respiratory infection'),
  ('G-2023-07', 'Lily', 'Nubian', 'Female', '2020-09-30', '2020-12-15', 'Pregnant', 'NT-078', 47.6, 'Brown', 'Second pregnancy'),
  ('G-2023-08', 'Oscar', 'Boer', 'Male', '2021-03-12', '2021-06-20', 'Healthy', 'BT-102', 58.9, 'White', 'Good growth rate'),
  ('G-2023-09', 'Ruby', 'Saanen', 'Female', '2022-01-25', '2022-04-10', 'Healthy', 'ST-034', 36.4, 'White', 'Young female with potential'),
  ('G-2023-10', 'Leo', 'Alpine', 'Male', '2018-06-08', '2018-09-15', 'Healthy', 'AT-067', 72.5, 'Brown and Black', 'Senior breeding male');
