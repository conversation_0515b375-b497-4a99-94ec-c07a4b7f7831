import { NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

export async function GET(request) {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    })

    const { searchParams } = new URL(request.url)
    const animalType = searchParams.get('animal_type')
    const gender = searchParams.get('gender') // 'Male' for sires, 'Female' for dams

    let query = `
      SELECT id, tag_number, name, gender, animal_type, breed
      FROM animals 
      WHERE 1=1
    `
    const params = []

    // Filter by animal type if provided
    if (animalType && animalType !== 'all') {
      query += ` AND animal_type = ?`
      params.push(animalType)
    }

    // Filter by gender if provided
    if (gender) {
      query += ` AND gender = ?`
      params.push(gender)
    }

    // Order by name for better UX
    query += ` ORDER BY name ASC, tag_number ASC`

    const [animals] = await connection.execute(query, params)

    await connection.end()

    return NextResponse.json({
      success: true,
      animals: animals,
      count: animals.length
    })

  } catch (error) {
    console.error('Error fetching parent animals:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch animals: ' + error.message,
        animals: [],
        count: 0
      },
      { status: 500 }
    )
  }
}
