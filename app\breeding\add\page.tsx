"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import Link from "next/link"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LucideCalendarClock, LucideSave, LucideInfo } from "lucide-react"
import { BackButton } from "@/components/ui/back-button"
import DashboardLayout from "@/components/dashboard-layout"

// Breeding methods
const breedingMethods = [
  { value: "natural", label: "Natural Breeding" },
  { value: "ai", label: "Artificial Insemination" },
  { value: "embryo", label: "Embryo Transfer" },
]

// Animal type options
const animalTypeOptions = [
  { value: "Goat", label: "Goats", femaleLabel: "Does", maleLabel: "Bucks", birthTerm: "Kidding", offspringTerm: "Kids" },
  { value: "Sheep", label: "Sheep", femaleLabel: "Ewes", maleLabel: "Rams", birthTerm: "Lambing", offspringTerm: "Lambs" },
  { value: "Cattle", label: "Cattle", femaleLabel: "Cows", maleLabel: "Bulls", birthTerm: "Calving", offspringTerm: "Calves" },
  { value: "Pig", label: "Pigs", femaleLabel: "Sows", maleLabel: "Boars", birthTerm: "Farrowing", offspringTerm: "Piglets" },
]

export default function AddBreedingPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [femaleAnimals, setFemaleAnimals] = useState<any[]>([])
  const [maleAnimals, setMaleAnimals] = useState<any[]>([])
  const [isLoadingFemales, setIsLoadingFemales] = useState(true)
  const [isLoadingMales, setIsLoadingMales] = useState(true)
  const [formData, setFormData] = useState({
    animalType: "Goat",
    femaleId: "",
    maleId: "",
    breedingMethod: "natural",
    breedingDate: new Date().toISOString().split("T")[0],
    expectedBirthDate: "",
    actualBirthDate: "",
    numberOfOffspring: "",
    notes: "",
  })

  // Calculate age from birth_date
  const calculateAge = (birthDate) => {
    if (!birthDate) return "Unknown"

    const birth = new Date(birthDate)
    const now = new Date()

    let years = now.getFullYear() - birth.getFullYear()
    const monthDiff = now.getMonth() - birth.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && now.getDate() < birth.getDate())) {
      years--
    }

    return years === 1 ? "1 year" : `${years} years`
  }

  // Fetch female animals from the database based on selected animal type
  useEffect(() => {
    const fetchFemaleAnimals = async () => {
      try {
        setIsLoadingFemales(true)
        console.log(`Fetching female ${formData.animalType.toLowerCase()}s...`)

        // Determine the correct API endpoint based on animal type
        const apiEndpoint = formData.animalType === 'Goat' ? '/api/goats' :
                           formData.animalType === 'Sheep' ? '/api/sheep' :
                           formData.animalType === 'Cattle' ? '/api/cattle' :
                           formData.animalType === 'Pig' ? '/api/pigs' : '/api/animals';

        const response = await fetch(`${apiEndpoint}?gender=Female`, {
          cache: 'no-store'
        })

        if (!response.ok) {
          throw new Error(`Failed to fetch female ${formData.animalType.toLowerCase()}s: ${response.status} ${response.statusText}`)
        }

        const responseData = await response.json()
        console.log(`Female ${formData.animalType.toLowerCase()}s response data:`, responseData)

        // Extract the animals array from the response (different endpoints have different structures)
        const animalsData = responseData.goats || responseData.sheep || responseData.cattle || responseData.pigs || responseData || []

        // Apply client-side filtering to ensure only females are included
        const femalesOnly = animalsData.filter((animal: any) => animal.gender === 'Female')
        console.log(`Filtered to ${femalesOnly.length} female ${formData.animalType.toLowerCase()}s out of ${animalsData.length} total`)

        // Transform the data to match the expected format
        const formattedAnimals = femalesOnly.map((animal: any) => ({
          id: animal.id.toString(),
          name: animal.name || 'Unnamed',
          breed: animal.breed || 'Unknown',
          tagNumber: animal.tag_number || animal.tag || 'No tag',
          age: calculateAge(animal.birth_date),
          status: animal.status || 'Healthy'
        }))

        setFemaleAnimals(formattedAnimals)
      } catch (error) {
        console.error(`Error fetching female ${formData.animalType.toLowerCase()}s:`, error)
        setFemaleAnimals([])
      } finally {
        setIsLoadingFemales(false)
      }
    }

    fetchFemaleAnimals()
  }, [formData.animalType]) // Re-fetch when animal type changes

  // Fetch male animals from the database based on selected animal type
  useEffect(() => {
    const fetchMaleAnimals = async () => {
      try {
        setIsLoadingMales(true)
        console.log(`Fetching male ${formData.animalType.toLowerCase()}s...`)

        // Determine the correct API endpoint based on animal type
        const apiEndpoint = formData.animalType === 'Goat' ? '/api/goats' :
                           formData.animalType === 'Sheep' ? '/api/sheep' :
                           formData.animalType === 'Cattle' ? '/api/cattle' :
                           formData.animalType === 'Pig' ? '/api/pigs' : '/api/animals';

        const response = await fetch(`${apiEndpoint}?gender=Male`, {
          cache: 'no-store'
        })

        if (!response.ok) {
          throw new Error(`Failed to fetch male ${formData.animalType.toLowerCase()}s: ${response.status} ${response.statusText}`)
        }

        const responseData = await response.json()
        console.log(`Male ${formData.animalType.toLowerCase()}s response data:`, responseData)

        // Extract the animals array from the response (different endpoints have different structures)
        const animalsData = responseData.goats || responseData.sheep || responseData.cattle || responseData.pigs || responseData || []

        // Apply client-side filtering to ensure only males are included
        const malesOnly = animalsData.filter((animal: any) => animal.gender === 'Male')
        console.log(`Filtered to ${malesOnly.length} male ${formData.animalType.toLowerCase()}s out of ${animalsData.length} total`)

        // Transform the data to match the expected format
        const formattedAnimals = malesOnly.map((animal: any) => ({
          id: animal.id.toString(),
          name: animal.name || 'Unnamed',
          breed: animal.breed || 'Unknown',
          tagNumber: animal.tag_number || animal.tag || 'No tag',
          age: calculateAge(animal.birth_date)
        }))

        setMaleAnimals(formattedAnimals)
      } catch (error) {
        console.error(`Error fetching male ${formData.animalType.toLowerCase()}s:`, error)
        setMaleAnimals([])
      } finally {
        setIsLoadingMales(false)
      }
    }

    fetchMaleAnimals()
  }, [formData.animalType]) // Re-fetch when animal type changes

  // Calculate expected birth date based on animal type gestation periods
  useEffect(() => {
    if (formData.breedingDate && formData.animalType) {
      const breedingDate = new Date(formData.breedingDate)
      const birthDate = new Date(breedingDate)

      // Different gestation periods for different animals
      const gestationDays = {
        'Goat': 150,    // ~5 months
        'Sheep': 147,   // ~5 months
        'Cattle': 283,  // ~9 months
        'Pig': 114      // ~3.5 months
      }

      const days = gestationDays[formData.animalType as keyof typeof gestationDays] || 150
      birthDate.setDate(breedingDate.getDate() + days)

      setFormData((prev) => ({
        ...prev,
        expectedBirthDate: birthDate.toISOString().split("T")[0],
      }))
    }
  }, [formData.breedingDate, formData.animalType])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))
  }

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Prepare the data to be sent to the API
      const breedingData = {
        female_id: parseInt(formData.femaleId),
        male_id: formData.breedingMethod === "natural" ? parseInt(formData.maleId) : null,
        breeding_method: formData.breedingMethod === "natural" ? "Natural" :
                        formData.breedingMethod === "ai" ? "Artificial_Insemination" : "Embryo_Transfer",
        breeding_date: formData.breedingDate,
        expected_birth_date: formData.expectedBirthDate,
        actual_birth_date: formData.actualBirthDate || null,
        number_of_offspring: formData.numberOfOffspring ? parseInt(formData.numberOfOffspring) : null,
        notes: formData.notes,
      }

      // Send the data to the API
      const response = await fetch('/api/breeding', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(breedingData),
      })

      if (!response.ok) {
        throw new Error(`Failed to create breeding record: ${response.status} ${response.statusText}`)
      }

      // Redirect to the breeding page on success
      router.push('/breeding')
    } catch (error) {
      console.error('Error creating breeding record:', error)
      // Handle error (you could set an error state and display it to the user)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-accent">Add Breeding Record</h1>
          <BackButton href="/breeding" label="Back to Breeding" variant="outline" className="btn-outline-accent" />
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Breeding Information</CardTitle>
              <CardDescription>
                Record a new breeding event for your animals
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Animal Type Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-rose-700">Animal Type</h3>
                <div className="space-y-2">
                  <Label htmlFor="animalType">
                    Select Animal Type <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.animalType}
                    onValueChange={(value) => {
                      handleSelectChange("animalType", value);
                      // Reset animal selections when type changes
                      handleSelectChange("femaleId", "");
                      handleSelectChange("maleId", "");
                    }}
                    required
                  >
                    <SelectTrigger id="animalType" className="input-primary">
                      <SelectValue placeholder="Select animal type" />
                    </SelectTrigger>
                    <SelectContent>
                      {animalTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Animal Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-rose-700">
                  {animalTypeOptions.find(opt => opt.value === formData.animalType)?.label || 'Animal'} Selection
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="femaleId">
                      Select {animalTypeOptions.find(opt => opt.value === formData.animalType)?.femaleLabel?.slice(0, -1) || 'Female'} <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.femaleId}
                      onValueChange={(value) => handleSelectChange("femaleId", value)}
                      required
                    >
                      <SelectTrigger id="femaleId" className="input-primary">
                        <SelectValue placeholder={`Select a female ${formData.animalType.toLowerCase()}`} />
                      </SelectTrigger>
                      <SelectContent>
                        {isLoadingFemales ? (
                          <SelectItem value="" disabled>Loading {animalTypeOptions.find(opt => opt.value === formData.animalType)?.femaleLabel?.toLowerCase() || 'females'}...</SelectItem>
                        ) : femaleAnimals.length > 0 ? (
                          femaleAnimals.map((animal: any) => (
                            <SelectItem key={animal.id} value={animal.id}>
                              {animal.name} (Tag: {animal.tagNumber}, {animal.breed}, {animal.age})
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="" disabled>No female {formData.animalType.toLowerCase()}s found</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <div className="text-xs text-muted-foreground mt-1">
                      {isLoadingFemales ? 'Loading...' :
                       femaleAnimals.length === 0 ? `No female ${formData.animalType.toLowerCase()}s available. Please add female ${formData.animalType.toLowerCase()}s first.` :
                       `${femaleAnimals.length} female ${formData.animalType.toLowerCase()}s available`}
                    </div>
                  </div>

                  {formData.breedingMethod === "natural" && (
                    <div className="space-y-2">
                      <Label htmlFor="maleId">
                        Select {animalTypeOptions.find(opt => opt.value === formData.animalType)?.maleLabel?.slice(0, -1) || 'Male'} <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={formData.maleId}
                        onValueChange={(value) => handleSelectChange("maleId", value)}
                        required={formData.breedingMethod === "natural"}
                      >
                        <SelectTrigger id="maleId" className="input-primary">
                          <SelectValue placeholder={`Select a male ${formData.animalType.toLowerCase()}`} />
                        </SelectTrigger>
                        <SelectContent>
                          {isLoadingMales ? (
                            <SelectItem value="" disabled>Loading {animalTypeOptions.find(opt => opt.value === formData.animalType)?.maleLabel?.toLowerCase() || 'males'}...</SelectItem>
                          ) : maleAnimals.length > 0 ? (
                            maleAnimals.map((animal: any) => (
                              <SelectItem key={animal.id} value={animal.id}>
                                {animal.name} (Tag: {animal.tagNumber}, {animal.breed}, {animal.age})
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="" disabled>No male {formData.animalType.toLowerCase()}s found</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      <div className="text-xs text-muted-foreground mt-1">
                        {isLoadingMales ? 'Loading...' :
                         maleAnimals.length === 0 ? `No male ${formData.animalType.toLowerCase()}s available. Please add male ${formData.animalType.toLowerCase()}s first.` :
                         `${maleAnimals.length} male ${formData.animalType.toLowerCase()}s available`}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Breeding Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-rose-700">Breeding Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="breedingMethod">
                      Breeding Method <span className="text-red-500">*</span>
                    </Label>
                    <Select
                      value={formData.breedingMethod}
                      onValueChange={(value) => handleSelectChange("breedingMethod", value)}
                      required
                    >
                      <SelectTrigger id="breedingMethod" className="input-primary">
                        <SelectValue placeholder="Select breeding method" />
                      </SelectTrigger>
                      <SelectContent>
                        {breedingMethods.map((method) => (
                          <SelectItem key={method.value} value={method.value}>
                            {method.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="breedingDate">
                      Breeding Date <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      type="date"
                      id="breedingDate"
                      name="breedingDate"
                      value={formData.breedingDate}
                      onChange={handleChange}
                      className="input-primary"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="expectedBirthDate">
                      Expected {animalTypeOptions.find(opt => opt.value === formData.animalType)?.birthTerm || 'Birth'} Date <LucideCalendarClock className="inline h-4 w-4 ml-1" />
                    </Label>
                    <Input
                      type="date"
                      id="expectedBirthDate"
                      name="expectedBirthDate"
                      value={formData.expectedBirthDate}
                      onChange={handleChange}
                      className="input-primary"
                      readOnly
                    />
                    <div className="text-xs text-muted-foreground mt-1">
                      Automatically calculated based on gestation period for {formData.animalType.toLowerCase()}s
                    </div>
                  </div>
                </div>
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  className="input-primary min-h-[100px]"
                  placeholder="Add any additional notes about this breeding..."
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button type="button" variant="outline" onClick={() => router.push("/breeding")}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting} className="btn-primary">
                {isSubmitting ? (
                  <>Processing...</>
                ) : (
                  <>
                    <LucideSave className="mr-2 h-4 w-4" />
                    Save Breeding Record
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </form>
      </div>
    </DashboardLayout>
  )
}








