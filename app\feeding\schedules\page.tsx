"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import {
  LucideCalendarClock,
  LucidePlus,
  LucideEdit,
  LucideTrash2,
  LucideCheck,
  LucideX,
  LucideUsers,
  LucideWheat,
  LucideAlarmClock,
  LucideRepeat,
  LucideCalendarDays,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

// Sample feeding schedules data
const feedingSchedules = [
  {
    id: "FS-001",
    name: "Morning Feed - All Goats",
    time: "07:00",
    days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
    goatGroups: ["All Goats"],
    feeds: [
      { name: "Alfalfa Hay", amount: "2.5", unit: "kg" },
      { name: "Grain Mix", amount: "0.5", unit: "kg" },
    ],
    notes: "Ensure fresh water is available",
    active: true,
    lastModified: "2024-05-01",
  },
  {
    id: "FS-002",
    name: "Evening Feed - All Goats",
    time: "17:00",
    days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
    goatGroups: ["All Goats"],
    feeds: [
      { name: "Alfalfa Hay", amount: "2.5", unit: "kg" },
      { name: "Grain Mix", amount: "0.5", unit: "kg" },
    ],
    notes: "Check water levels",
    active: true,
    lastModified: "2024-05-01",
  },
  {
    id: "FS-003",
    name: "Pregnant Does - Supplement",
    time: "12:00",
    days: ["Monday", "Wednesday", "Friday"],
    goatGroups: ["Pregnant Does"],
    feeds: [
      { name: "Protein Supplement", amount: "0.25", unit: "kg" },
      { name: "Alfalfa Pellets", amount: "0.5", unit: "kg" },
    ],
    notes: "Adjust amount based on trimester",
    active: true,
    lastModified: "2024-05-10",
  },
  {
    id: "FS-004",
    name: "Lactating Does - Extra Feed",
    time: "12:00",
    days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
    goatGroups: ["Lactating Does"],
    feeds: [
      { name: "Alfalfa Hay", amount: "1", unit: "kg" },
      { name: "Grain Mix", amount: "0.75", unit: "kg" },
    ],
    notes: "Adjust based on milk production",
    active: true,
    lastModified: "2024-05-15",
  },
  {
    id: "FS-005",
    name: "Kids - Special Feed",
    time: "10:00",
    days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
    goatGroups: ["Kids"],
    feeds: [
      { name: "Alfalfa Pellets", amount: "0.25", unit: "kg" },
      { name: "Grain Mix", amount: "0.1", unit: "kg" },
    ],
    notes: "Ensure kids have access without competition from adults",
    active: true,
    lastModified: "2024-05-20",
  },
  {
    id: "FS-006",
    name: "Mineral Supplement - All Goats",
    time: "14:00",
    days: ["Monday", "Thursday"],
    goatGroups: ["All Goats"],
    feeds: [{ name: "Mineral Blocks", amount: "Free choice", unit: "" }],
    notes: "Check and replace as needed",
    active: true,
    lastModified: "2024-05-05",
  },
]

// Sample goat groups
const goatGroups = [
  { id: "GG-001", name: "All Goats", count: 10 },
  { id: "GG-002", name: "Pregnant Does", count: 2 },
  { id: "GG-003", name: "Lactating Does", count: 3 },
  { id: "GG-004", name: "Kids", count: 4 },
  { id: "GG-005", name: "Bucks", count: 1 },
]

// Today's feeding schedule
const today = new Date()
const dayOfWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"][today.getDay()]
const todaySchedules = feedingSchedules
  .filter((schedule) => schedule.active && schedule.days.includes(dayOfWeek))
  .sort((a, b) => a.time.localeCompare(b.time))

export default function FeedingSchedulesPage() {
  const [activeTab, setActiveTab] = useState("today")
  const [groupFilter, setGroupFilter] = useState("all")

  // Filter schedules by group
  const filteredSchedules =
    groupFilter === "all"
      ? feedingSchedules
      : feedingSchedules.filter((schedule) => schedule.goatGroups.some((group) => group === groupFilter))

  // Format time for display
  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(":")
    return `${hours}:${minutes}`
  }

  // Format days for display
  const formatDays = (days) => {
    if (days.length === 7) return "Every day"
    if (
      days.length === 5 &&
      days.includes("Monday") &&
      days.includes("Tuesday") &&
      days.includes("Wednesday") &&
      days.includes("Thursday") &&
      days.includes("Friday")
    )
      return "Weekdays"
    if (days.length === 2 && days.includes("Saturday") && days.includes("Sunday")) return "Weekends"

    return days.join(", ")
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-secondary">Feeding Schedules</h1>
          <Link href="/feeding/schedules/add">
            <Button className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
              <LucidePlus className="mr-2 h-4 w-4" />
              Create Feeding Schedule
            </Button>
          </Link>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Total Schedules</div>
                  <div className="text-2xl font-bold text-amber-600">{feedingSchedules.length}</div>
                </div>
                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                  <LucideCalendarClock className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Today's Feedings</div>
                  <div className="text-2xl font-bold text-amber-600">{todaySchedules.length}</div>
                </div>
                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                  <LucideCalendarDays className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Goat Groups</div>
                  <div className="text-2xl font-bold text-amber-600">{goatGroups.length}</div>
                </div>
                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                  <LucideUsers className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Active Schedules</div>
                  <div className="text-2xl font-bold text-amber-600">
                    {feedingSchedules.filter((s) => s.active).length}
                  </div>
                </div>
                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                  <LucideRepeat className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3 p-1 bg-gradient-to-r from-amber-50 via-yellow-50 to-orange-50 rounded-xl">
            <TabsTrigger
              value="today"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-500 data-[state=active]:to-yellow-500 data-[state=active]:text-white transition-all duration-300 hover:text-amber-700"
            >
              Today's Schedule
            </TabsTrigger>
            <TabsTrigger
              value="all"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-yellow-500 data-[state=active]:to-orange-500 data-[state=active]:text-white transition-all duration-300 hover:text-yellow-700"
            >
              All Schedules
            </TabsTrigger>
            <TabsTrigger
              value="groups"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white transition-all duration-300 hover:text-orange-700"
            >
              By Goat Group
            </TabsTrigger>
          </TabsList>

          {/* Today's Schedule Tab */}
          <TabsContent value="today" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle>Today's Feeding Schedule ({dayOfWeek})</CardTitle>
                  <LucideCalendarDays className="h-5 w-5 text-amber-500" />
                </div>
                <CardDescription>Scheduled feedings for today</CardDescription>
              </CardHeader>
              <CardContent>
                {todaySchedules.length > 0 ? (
                  <div className="space-y-4">
                    {todaySchedules.map((schedule) => (
                      <div
                        key={schedule.id}
                        className="flex flex-col p-4 border rounded-lg hover:bg-amber-50 transition-colors"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div className="h-8 w-8 rounded-full bg-amber-100 flex items-center justify-center">
                              <LucideAlarmClock className="h-4 w-4 text-amber-600" />
                            </div>
                            <div>
                              <h3 className="font-medium">{schedule.name}</h3>
                              <p className="text-sm text-muted-foreground">{formatTime(schedule.time)}</p>
                            </div>
                          </div>
                          <Badge variant="outline" className="badge-amber">
                            {schedule.goatGroups.join(", ")}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                          <div>
                            <h4 className="text-sm font-medium mb-1">Feed Items:</h4>
                            <ul className="text-sm space-y-1">
                              {schedule.feeds.map((feed, index) => (
                                <li key={index} className="flex items-center gap-2">
                                  <LucideWheat className="h-3 w-3 text-amber-500" />
                                  {feed.name}: {feed.amount} {feed.unit}
                                </li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium mb-1">Notes:</h4>
                            <p className="text-sm text-muted-foreground">{schedule.notes || "No notes"}</p>
                          </div>
                        </div>
                        <div className="flex justify-end mt-4 gap-2">
                          <Link href={`/feeding/record?scheduleId=${schedule.id}`}>
                            <Button
                              size="sm"
                              className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white"
                            >
                              <LucideCheck className="mr-1 h-4 w-4" />
                              Record Feeding
                            </Button>
                          </Link>
                          <Link href={`/feeding/schedules/${schedule.id}/edit`}>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-amber-500 text-amber-600 hover:bg-amber-50"
                            >
                              <LucideEdit className="mr-1 h-4 w-4" />
                              Edit
                            </Button>
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="h-12 w-12 rounded-full bg-amber-100 flex items-center justify-center mx-auto mb-4">
                      <LucideCalendarClock className="h-6 w-6 text-amber-500" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">No Feedings Scheduled Today</h3>
                    <p className="text-muted-foreground mb-4">There are no feeding schedules for today.</p>
                    <Link href="/feeding/schedules/add">
                      <Button className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white">
                        <LucidePlus className="mr-2 h-4 w-4" />
                        Create Feeding Schedule
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* All Schedules Tab */}
          <TabsContent value="all" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle>All Feeding Schedules</CardTitle>
                  <LucideCalendarClock className="h-5 w-5 text-amber-500" />
                </div>
                <CardDescription>Complete list of all feeding schedules</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>Days</TableHead>
                      <TableHead>Goat Groups</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {feedingSchedules.map((schedule) => (
                      <TableRow key={schedule.id}>
                        <TableCell className="font-medium">{schedule.name}</TableCell>
                        <TableCell>{formatTime(schedule.time)}</TableCell>
                        <TableCell>{formatDays(schedule.days)}</TableCell>
                        <TableCell>{schedule.goatGroups.join(", ")}</TableCell>
                        <TableCell>
                          {schedule.active ? (
                            <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Active</Badge>
                          ) : (
                            <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-200">
                              Inactive
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Link href={`/feeding/schedules/${schedule.id}/edit`}>
                              <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                                <LucideEdit className="h-4 w-4" />
                                <span className="sr-only">Edit</span>
                              </Button>
                            </Link>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <LucideTrash2 className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* By Goat Group Tab */}
          <TabsContent value="groups" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle>Feeding Schedules by Goat Group</CardTitle>
                  <LucideUsers className="h-5 w-5 text-amber-500" />
                </div>
                <CardDescription>View schedules filtered by goat group</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <Select value={groupFilter} onValueChange={setGroupFilter}>
                    <SelectTrigger className="w-full md:w-[300px]">
                      <SelectValue placeholder="Select goat group" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Groups</SelectItem>
                      {goatGroups.map((group) => (
                        <SelectItem key={group.id} value={group.name}>
                          {group.name} ({group.count} goats)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {filteredSchedules.length > 0 ? (
                  <div className="space-y-4">
                    {filteredSchedules.map((schedule) => (
                      <div
                        key={schedule.id}
                        className="flex flex-col p-4 border rounded-lg hover:bg-amber-50 transition-colors"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div className="h-8 w-8 rounded-full bg-amber-100 flex items-center justify-center">
                              <LucideAlarmClock className="h-4 w-4 text-amber-600" />
                            </div>
                            <div>
                              <h3 className="font-medium">{schedule.name}</h3>
                              <p className="text-sm text-muted-foreground">
                                {formatTime(schedule.time)} • {formatDays(schedule.days)}
                              </p>
                            </div>
                          </div>
                          <Badge variant="outline" className="badge-amber">
                            {schedule.goatGroups.join(", ")}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                          <div>
                            <h4 className="text-sm font-medium mb-1">Feed Items:</h4>
                            <ul className="text-sm space-y-1">
                              {schedule.feeds.map((feed, index) => (
                                <li key={index} className="flex items-center gap-2">
                                  <LucideWheat className="h-3 w-3 text-amber-500" />
                                  {feed.name}: {feed.amount} {feed.unit}
                                </li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium mb-1">Notes:</h4>
                            <p className="text-sm text-muted-foreground">{schedule.notes || "No notes"}</p>
                          </div>
                        </div>
                        <div className="flex justify-end mt-4 gap-2">
                          <Link href={`/feeding/schedules/${schedule.id}/edit`}>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-amber-500 text-amber-600 hover:bg-amber-50"
                            >
                              <LucideEdit className="mr-1 h-4 w-4" />
                              Edit
                            </Button>
                          </Link>
                          <Button size="sm" variant="outline" className="border-red-500 text-red-600 hover:bg-red-50">
                            <LucideTrash2 className="mr-1 h-4 w-4" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="h-12 w-12 rounded-full bg-amber-100 flex items-center justify-center mx-auto mb-4">
                      <LucideX className="h-6 w-6 text-amber-500" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">No Schedules Found</h3>
                    <p className="text-muted-foreground mb-4">No feeding schedules found for the selected group.</p>
                    <Button variant="outline" onClick={() => setGroupFilter("all")}>
                      Show All Groups
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}

