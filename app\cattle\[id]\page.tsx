"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON>, usePara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import DashboardLayout from "@/components/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { BackButton } from "@/components/ui/back-button"
import { LucideEdit, LucideSave, LucideX, LucideLoader2 } from "lucide-react"

export default function CattleDetailPage() {
  const router = useRouter()
  const params = useParams()
  const cattleId = params.id as string

  const [cattle, setCattle] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [sires, setSires] = useState<any[]>([])
  const [dams, setDams] = useState<any[]>([])
  const [loadingParents, setLoadingParents] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    tag_number: "",
    name: "",
    breed: "",
    gender: "",
    birth_date: "",
    acquisition_date: "",
    status: "",
    weight: "",
    color: "",
    markings: "",
    sire: "",
    dam: "",
    purchase_price: "",
    notes: "",
    is_registered: false,
    registration_number: ""
  })

  // Breed options for cattle
  const breedOptions = [
    "Holstein", "Angus", "Hereford", "Charolais", "Simmental", 
    "Limousin", "Brahman", "Jersey", "Guernsey", "Brown Swiss",
    "Shorthorn", "Devon", "Highland", "Zebu", "Ankole", "Other"
  ]

  // Fetch cattle data
  const fetchCattle = async () => {
    setIsLoading(true)
    setError(null)
    try {
      const response = await fetch(`/api/cattle/${cattleId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch cattle data')
      }
      const data = await response.json()
      setCattle(data)
      setFormData({
        tag_number: data.tag_number || "",
        name: data.name || "",
        breed: data.breed || "",
        gender: data.gender || "",
        birth_date: data.birth_date ? data.birth_date.split('T')[0] : "",
        acquisition_date: data.acquisition_date ? data.acquisition_date.split('T')[0] : "",
        status: data.status || "",
        weight: data.weight || "",
        color: data.color || "",
        markings: data.markings || "",
        sire: data.sire || "",
        dam: data.dam || "",
        purchase_price: data.purchase_price || "",
        notes: data.notes || "",
        is_registered: data.is_registered || false,
        registration_number: data.registration_number || ""
      })
    } catch (err: any) {
      setError(err.message)
      toast({
        title: "Error",
        description: "Failed to load cattle data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch parent animals
  const fetchParentAnimals = async () => {
    setLoadingParents(true)
    try {
      const [siresResponse, damsResponse] = await Promise.all([
        fetch(`/api/animals/parents?animal_type=Cattle&gender=Male`),
        fetch(`/api/animals/parents?animal_type=Cattle&gender=Female`)
      ])
      
      const siresData = await siresResponse.json()
      const damsData = await damsResponse.json()
      
      if (siresData.success) setSires(siresData.animals)
      if (damsData.success) setDams(damsData.animals)
    } catch (error) {
      console.error('Error fetching parent animals:', error)
    } finally {
      setLoadingParents(false)
    }
  }

  // Save changes
  const handleSave = async () => {
    setIsSaving(true)
    try {
      const response = await fetch(`/api/cattle/${cattleId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        throw new Error('Failed to update cattle')
      }

      const updatedCattle = await response.json()
      setCattle(updatedCattle)
      setIsEditing(false)
      toast({
        title: "Success",
        description: "Cattle updated successfully!",
      })
    } catch (err: any) {
      toast({
        title: "Error",
        description: "Failed to update cattle. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Cancel editing
  const handleCancel = () => {
    setIsEditing(false)
    // Reset form data to original cattle data
    if (cattle) {
      setFormData({
        tag_number: cattle.tag_number || "",
        name: cattle.name || "",
        breed: cattle.breed || "",
        gender: cattle.gender || "",
        birth_date: cattle.birth_date ? cattle.birth_date.split('T')[0] : "",
        acquisition_date: cattle.acquisition_date ? cattle.acquisition_date.split('T')[0] : "",
        status: cattle.status || "",
        weight: cattle.weight || "",
        color: cattle.color || "",
        markings: cattle.markings || "",
        sire: cattle.sire || "",
        dam: cattle.dam || "",
        purchase_price: cattle.purchase_price || "",
        notes: cattle.notes || "",
        is_registered: cattle.is_registered || false,
        registration_number: cattle.registration_number || ""
      })
    }
  }

  useEffect(() => {
    if (cattleId) {
      fetchCattle()
      fetchParentAnimals()
    }
  }, [cattleId])

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-6">
          <div className="flex items-center justify-center py-12">
            <LucideLoader2 className="h-8 w-8 animate-spin text-amber-600" />
            <span className="ml-2 text-lg">Loading cattle details...</span>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (error || !cattle) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-6">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Error Loading Cattle</h2>
            <p className="text-gray-600 mb-6">{error || "Cattle not found"}</p>
            <Button onClick={() => router.back()}>Go Back</Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <BackButton
              href="/cattle"
              label="Back to Cattle"
              variant="ghost"
              className="mb-2"
            />
            <h1 className="text-3xl font-bold tracking-tight text-gradient-primary">
              {isEditing ? `Edit ${cattle.name}` : cattle.name}
            </h1>
            <p className="text-muted-foreground mt-1">
              {isEditing ? "Update cattle information" : "Cattle details and information"}
            </p>
          </div>
          <div className="flex gap-2">
            {!isEditing ? (
              <Button
                onClick={() => setIsEditing(true)}
                className="bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white"
              >
                <LucideEdit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            ) : (
              <>
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSaving}
                >
                  <LucideX className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white"
                >
                  {isSaving ? (
                    <LucideLoader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <LucideSave className="mr-2 h-4 w-4" />
                  )}
                  {isSaving ? "Saving..." : "Save"}
                </Button>
              </>
            )}
          </div>
        </div>

        <Card className="border-t-4 border-t-amber-500 shadow-lg">
          <CardHeader className="bg-gradient-to-r from-amber-50 to-orange-50">
            <CardTitle className="flex items-center gap-2 text-amber-700">
              Cattle Information
            </CardTitle>
            <CardDescription>
              {isEditing ? "Edit the details below" : "View cattle details"}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Tag Number*</label>
                {isEditing ? (
                  <Input
                    value={formData.tag_number}
                    onChange={(e) => handleInputChange('tag_number', e.target.value)}
                    className="focus:ring-2 focus:ring-amber-500"
                  />
                ) : (
                  <div className="p-2 bg-gray-50 rounded border">{cattle.tag_number}</div>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Name*</label>
                {isEditing ? (
                  <Input
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="focus:ring-2 focus:ring-amber-500"
                  />
                ) : (
                  <div className="p-2 bg-gray-50 rounded border">{cattle.name}</div>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Breed*</label>
                {isEditing ? (
                  <Select value={formData.breed} onValueChange={(value) => handleInputChange('breed', value)}>
                    <SelectTrigger className="focus:ring-2 focus:ring-amber-500">
                      <SelectValue placeholder="Select breed" />
                    </SelectTrigger>
                    <SelectContent>
                      {breedOptions.map((breed) => (
                        <SelectItem key={breed} value={breed}>{breed}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="p-2 bg-gray-50 rounded border">{cattle.breed}</div>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Gender*</label>
                {isEditing ? (
                  <Select value={formData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
                    <SelectTrigger className="focus:ring-2 focus:ring-amber-500">
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Female">Female</SelectItem>
                      <SelectItem value="Male">Male</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="p-2 bg-gray-50 rounded border">{cattle.gender}</div>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Birth Date</label>
                {isEditing ? (
                  <Input
                    type="date"
                    value={formData.birth_date}
                    onChange={(e) => handleInputChange('birth_date', e.target.value)}
                    className="focus:ring-2 focus:ring-amber-500"
                  />
                ) : (
                  <div className="p-2 bg-gray-50 rounded border">
                    {cattle.birth_date ? new Date(cattle.birth_date).toLocaleDateString() : 'Not specified'}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                {isEditing ? (
                  <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                    <SelectTrigger className="focus:ring-2 focus:ring-amber-500">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Healthy">Healthy</SelectItem>
                      <SelectItem value="Sick">Sick</SelectItem>
                      <SelectItem value="Injured">Injured</SelectItem>
                      <SelectItem value="Quarantined">Quarantined</SelectItem>
                      <SelectItem value="Deceased">Deceased</SelectItem>
                      <SelectItem value="Pregnant">Pregnant</SelectItem>
                      <SelectItem value="Lactating">Lactating</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="p-2 bg-gray-50 rounded border">{cattle.status}</div>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Weight (kg)</label>
                {isEditing ? (
                  <Input
                    type="number"
                    step="0.01"
                    value={formData.weight}
                    onChange={(e) => handleInputChange('weight', e.target.value)}
                    className="focus:ring-2 focus:ring-amber-500"
                  />
                ) : (
                  <div className="p-2 bg-gray-50 rounded border">{cattle.weight || 'Not specified'}</div>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Color</label>
                {isEditing ? (
                  <Input
                    value={formData.color}
                    onChange={(e) => handleInputChange('color', e.target.value)}
                    className="focus:ring-2 focus:ring-amber-500"
                  />
                ) : (
                  <div className="p-2 bg-gray-50 rounded border">{cattle.color || 'Not specified'}</div>
                )}
              </div>

              {/* Conditional fields based on birth date */}
              {formData.birth_date && isEditing && (
                <>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Sire (Father)</label>
                    <Select value={formData.sire} onValueChange={(value) => handleInputChange('sire', value)} disabled={loadingParents}>
                      <SelectTrigger className="focus:ring-2 focus:ring-amber-500">
                        <SelectValue placeholder={loadingParents ? "Loading sires..." : "Select sire (optional)"} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">None / Unknown</SelectItem>
                        {sires.map((sire) => (
                          <SelectItem key={sire.id} value={sire.tag_number}>
                            {sire.name} (#{sire.tag_number}) - {sire.breed}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Dam (Mother)</label>
                    <Select value={formData.dam} onValueChange={(value) => handleInputChange('dam', value)} disabled={loadingParents}>
                      <SelectTrigger className="focus:ring-2 focus:ring-amber-500">
                        <SelectValue placeholder={loadingParents ? "Loading dams..." : "Select dam (optional)"} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">None / Unknown</SelectItem>
                        {dams.map((dam) => (
                          <SelectItem key={dam.id} value={dam.tag_number}>
                            {dam.name} (#{dam.tag_number}) - {dam.breed}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </>
              )}

              {/* Show sire/dam in view mode */}
              {!isEditing && (cattle.sire || cattle.dam) && (
                <>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Sire (Father)</label>
                    <div className="p-2 bg-gray-50 rounded border">{cattle.sire || 'Unknown'}</div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Dam (Mother)</label>
                    <div className="p-2 bg-gray-50 rounded border">{cattle.dam || 'Unknown'}</div>
                  </div>
                </>
              )}

              {/* Purchase price - only show if no birth date */}
              {!formData.birth_date && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Purchase Price</label>
                  {isEditing ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={formData.purchase_price}
                      onChange={(e) => handleInputChange('purchase_price', e.target.value)}
                      className="focus:ring-2 focus:ring-amber-500"
                    />
                  ) : (
                    <div className="p-2 bg-gray-50 rounded border">{cattle.purchase_price || 'Not specified'}</div>
                  )}
                </div>
              )}
            </div>

            {/* Notes section */}
            <div className="mt-6 space-y-2">
              <label className="text-sm font-medium">Notes</label>
              {isEditing ? (
                <Textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={4}
                  className="w-full focus:ring-2 focus:ring-amber-500"
                />
              ) : (
                <div className="p-2 bg-gray-50 rounded border min-h-[100px]">
                  {cattle.notes || 'No notes'}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
