"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  LucidePlus,
  LucideCalendarClock,
  LucideCalendarDays,
  LucideHeart,
  LucideUsers,
  LucideCheck,
  LucideAlertCircle,
  LucideBaby,
  LucideArrowUpRight,
  LucideCalendarRange,
  LucideClipboard,
  LucideLoader2,
  LucideRefreshCw,
  LucideSearch,
  LucideFilter,
  LucideX,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"
import { toast } from "@/components/ui/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Add this function near the top of your component or in a utils file
const formatDate = (dateString: string | Date): string => {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return String(dateString) // Return the original string if parsing fails
  }
}

export default function BreedingPage() {
  const [activeTab, setActiveTab] = useState("overview")
  // Define types for the breeding data
  interface BreedingStats {
    activeBreedings: number;
    pregnantAnimals: number;
    successRate: number;
    offspringPerBirth: number | string;
    upcomingBirths: number;
    animalsInHeat: number;
    totalHeatCycles?: number;
    cycleLengths?: Array<{
      animal_id: number;
      animal_name: string;
      animal_type: string;
      avg_cycle_length: number;
    }>;
    upcomingHeat?: Array<{
      animal_id: number;
      animal_name: string;
      animal_type: string;
      tag_number: string;
      last_heat_date: string;
      predicted_next_heat: string;
    }>;
  }

  interface BreedingData {
    records: any[];
    stats: BreedingStats;
    upcomingBirths: any[];
    heatCycles: any[];
    upcomingEvents: any[];
  }

  const [breedingData, setBreedingData] = useState<BreedingData>({
    records: [],
    stats: {
      activeBreedings: 0,
      pregnantAnimals: 0,
      successRate: 0,
      offspringPerBirth: 0,
      upcomingBirths: 0,
      animalsInHeat: 0,
      totalHeatCycles: 0,
      cycleLengths: [],
      upcomingHeat: []
    },
    upcomingBirths: [],
    heatCycles: [],
    upcomingEvents: []
  })
  const [kidsData, setKidsData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [animalTypeFilter, setAnimalTypeFilter] = useState("all")
  const [kidsSearchTerm, setKidsSearchTerm] = useState("")
  const [filteredKids, setFilteredKids] = useState<any[]>([])
  const [filteredRecords, setFilteredRecords] = useState<any[]>([])

  // Fetch breeding records from the API
  const fetchBreedingData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Fetch main breeding data
      const breedingResponse = await fetch('/api/breeding-records')

      if (!breedingResponse.ok) {
        throw new Error(`Failed to fetch breeding records: ${breedingResponse.status}`)
      }

      const breedingData = await breedingResponse.json()

      // Fetch detailed heat cycle data
      const heatCyclesResponse = await fetch('/api/breeding/heat-cycles')

      if (!heatCyclesResponse.ok) {
        console.warn('Failed to fetch detailed heat cycle data, using basic data instead')
      } else {
        // If heat cycles data is available, enhance the breeding data with it
        const heatCyclesData = await heatCyclesResponse.json()

        // Merge the data
        if (heatCyclesData && heatCyclesData.heatCycles) {
          // Use the more detailed heat cycles data if available
          breedingData.heatCycles = heatCyclesData.heatCycles

          // Update stats with heat cycle data from the heat_cycles table
          if (heatCyclesData.stats) {
            // Additional data for other sections
            breedingData.stats = {
              ...breedingData.stats,
              cycleLengths: heatCyclesData.stats.cycleLengths || [],
              upcomingHeat: heatCyclesData.stats.upcomingHeat || []
            }
          }
        }
      }

      // Fetch heat cycles stats specifically for the dashboard
      try {
        const heatCyclesStatsResponse = await fetch('/api/breeding/heat-cycles-stats')

        if (heatCyclesStatsResponse.ok) {
          const heatCyclesStats = await heatCyclesStatsResponse.json()

          // Log the heat cycle stats for debugging
          console.log('Heat cycle stats from dedicated endpoint:', heatCyclesStats);

          // Update the stats with the data from the dedicated endpoint
          breedingData.stats = {
            ...breedingData.stats,
            // 'doesInHeat' is the count of heat cycle records from the last 2 days
            // This is a simple count of rows in the heat_cycles table with recent dates
            doesInHeat: heatCyclesStats.doesInHeat || 0,

            // 'totalHeatCycles' is the total count of all records in the heat_cycles table
            // This is a simple COUNT(*) of all rows in the heat_cycles table
            totalHeatCycles: heatCyclesStats.totalHeatCycles || 0
          }
        } else {
          console.warn('Failed to fetch heat cycles stats from dedicated endpoint')
        }
      } catch (statsError) {
        console.error('Error fetching heat cycles stats:', statsError)
      }

      setBreedingData(breedingData)
      setFilteredRecords(breedingData.records || [])
    } catch (err: any) {
      console.error('Error fetching breeding data:', err)
      setError(err.message)
      toast({
        title: "Error",
        description: "Failed to load breeding data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch kids data
  const fetchKidsData = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/breeding/kids?max_age_months=6')
      if (!response.ok) {
        throw new Error('Failed to fetch kids data')
      }
      const data = await response.json()
      setKidsData(data)
      setFilteredKids(data.kids || [])
    } catch (err: any) {
      console.error('Error fetching kids data:', err)
      toast({
        title: "Error",
        description: "Failed to load kids data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Fetch data on component mount
  useEffect(() => {
    fetchBreedingData()
    fetchKidsData()
  }, [])

  // Filter records based on search term
  useEffect(() => {
    if (!breedingData.records) return

    if (searchTerm.trim() === "") {
      setFilteredRecords(breedingData.records)
    } else {
      const lowercasedSearch = searchTerm.toLowerCase()
      const filtered = breedingData.records.filter((record: any) =>
        (record.female_name && record.female_name.toLowerCase().includes(lowercasedSearch)) ||
        (record.male_name && record.male_name.toLowerCase().includes(lowercasedSearch)) ||
        (record.breeding_method && record.breeding_method.toLowerCase().includes(lowercasedSearch)) ||
        (record.status && record.status.toLowerCase().includes(lowercasedSearch))
      )
      setFilteredRecords(filtered)
    }
  }, [searchTerm, breedingData.records])

  // Filter kids based on search term
  useEffect(() => {
    if (!kidsData?.kids) return

    if (kidsSearchTerm.trim() === "") {
      setFilteredKids(kidsData.kids)
    } else {
      const lowercasedSearch = kidsSearchTerm.toLowerCase()
      const filtered = kidsData.kids.filter((kid: any) =>
        (kid.tag_number && kid.tag_number.toLowerCase().includes(lowercasedSearch)) ||
        (kid.name && kid.name.toLowerCase().includes(lowercasedSearch)) ||
        (kid.breed && kid.breed.toLowerCase().includes(lowercasedSearch)) ||
        (kid.gender && kid.gender.toLowerCase().includes(lowercasedSearch)) ||
        (kid.status && kid.status.toLowerCase().includes(lowercasedSearch)) ||
        (kid.animal_type && kid.animal_type.toLowerCase().includes(lowercasedSearch)) ||
        (kid.sire_name && kid.sire_name.toLowerCase().includes(lowercasedSearch)) ||
        (kid.dam_name && kid.dam_name.toLowerCase().includes(lowercasedSearch)) ||
        (kid.notes && kid.notes.toLowerCase().includes(lowercasedSearch))
      )
      setFilteredKids(filtered)
    }
  }, [kidsSearchTerm, kidsData?.kids])

  // Calculate days until due date
  const calculateDaysLeft = (dueDate: string | Date): number => {
    if (!dueDate) return 0

    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const due = new Date(dueDate)

    // If the date is in the past, return 0
    if (due < today) return 0

    const diffTime = due.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  // Get status badge for pregnant does
  const getPregnancyStatusBadge = (daysLeft: number) => {
    if (daysLeft <= 30) {
      return <Badge className="bg-red-100 text-red-800 hover:bg-red-200 border-red-300">Due Soon</Badge>
    } else if (daysLeft <= 60) {
      return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200 border-amber-300">Mid-Term</Badge>
    } else {
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-200 border-green-300">Early Term</Badge>
    }
  }

  // Get event type badge
  const getEventTypeBadge = (type: string) => {
    switch (type) {
      case "heat":
        return <Badge className="bg-pink-100 text-pink-800 hover:bg-pink-200 border-pink-300">Heat</Badge>
      case "breeding":
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200 border-purple-300">Breeding</Badge>
      case "pregnancy-check":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300">Pregnancy Check</Badge>
      case "kidding":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200 border-red-300">Kidding</Badge>
      default:
        return <Badge>Event</Badge>
    }
  }

  // Get breeding status badge
  const getBreedingStatusBadge = (status: string) => {
    if (status === "Confirmed" || status === "Confirmed Pregnant") {
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-200 border-green-300">Confirmed</Badge>
    } else if (status === "Pending" || status === "Pending Confirmation") {
      return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200 border-amber-300">Pending</Badge>
    } else if (status === "Failed") {
      return <Badge className="bg-red-100 text-red-800 hover:bg-red-200 border-red-300">Failed</Badge>
    } else if (status === "Kidded") {
      return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300">Kidded</Badge>
    } else {
      return <Badge>{status}</Badge>
    }
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold tracking-tight text-gradient-accent">Breeding Management</h1>
            {isLoading && <LucideLoader2 className="h-5 w-5 text-pink-500 animate-spin" />}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={fetchBreedingData}
              disabled={isLoading}
              className="h-10 w-10 rounded-full"
              title="Refresh data"
            >
              <LucideRefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            <Link href="/breeding/heat-cycle">
              <Button
                variant="outline"
                className="border-2 border-pink-500 text-pink-600 hover:bg-pink-50 hover:text-pink-700 transition-all duration-300"
              >
                <LucideHeart className="mr-2 h-4 w-4" />
                Record Heat Cycle
              </Button>
            </Link>
            <Link href="/breeding/add">
              <Button className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
                <LucidePlus className="mr-2 h-4 w-4" />
                Add Breeding Record
              </Button>
            </Link>
          </div>
        </div>

        {/* Error message */}
        {error && !isLoading && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
            <h3 className="font-medium flex items-center gap-2">
              <LucideAlertCircle className="h-5 w-5" />
              Error Loading Breeding Data
            </h3>
            <p className="mt-1 text-sm">{error}</p>
            <Button
              variant="outline"
              className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
              onClick={fetchBreedingData}
            >
              Retry
            </Button>
          </div>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4 p-1 bg-gradient-to-r from-pink-50 via-rose-50 to-red-50 rounded-xl">
            <TabsTrigger
              value="overview"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-rose-500 data-[state=active]:text-white transition-all duration-300 hover:text-pink-700"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="breeding"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-rose-500 data-[state=active]:to-red-500 data-[state=active]:text-white transition-all duration-300 hover:text-rose-700"
            >
              Breeding Records
            </TabsTrigger>
            <TabsTrigger
              value="kidding"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-500 data-[state=active]:to-orange-500 data-[state=active]:text-white transition-all duration-300 hover:text-red-700"
            >
              Kidding Records
            </TabsTrigger>
            <TabsTrigger
              value="heat"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-amber-500 data-[state=active]:text-white transition-all duration-300 hover:text-orange-700"
            >
              Heat Cycles
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab Content */}
          <TabsContent value="overview" className="space-y-6">
            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
              <Card className="stat-card-primary">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-muted-foreground">Active Breedings</div>
                      <div className="text-2xl font-bold text-pink-600">{breedingData.stats.activeBreedings}</div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-pink-100 flex items-center justify-center">
                      <LucideCalendarClock className="h-5 w-5 text-pink-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="stat-card-secondary">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-muted-foreground">Pregnant Animals</div>
                      <div className="text-2xl font-bold text-rose-600">{breedingData.stats.pregnantAnimals}</div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-rose-100 flex items-center justify-center">
                      <LucideHeart className="h-5 w-5 text-rose-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="stat-card-accent">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-muted-foreground">Success Rate</div>
                      <div className="text-2xl font-bold text-purple-600">{breedingData.stats.successRate}%</div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                      <LucideCheck className="h-5 w-5 text-purple-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="stat-card-amber">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-muted-foreground">Offspring per Birth</div>
                      <div className="text-2xl font-bold text-blue-600">{breedingData.stats.offspringPerBirth}</div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <LucideBaby className="h-5 w-5 text-blue-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="stat-card-primary">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-muted-foreground">Upcoming Births</div>
                      <div className="text-2xl font-bold text-red-600">{breedingData.stats.upcomingBirths}</div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                      <LucideCalendarDays className="h-5 w-5 text-red-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card className="stat-card-secondary">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-muted-foreground">Animals in Heat</div>
                      <div className="text-2xl font-bold text-pink-600">{breedingData.stats.animalsInHeat}</div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-pink-100 flex items-center justify-center">
                      <LucideAlertCircle className="h-5 w-5 text-pink-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Upcoming Events and Pregnant Does */}
            <div className="grid gap-6 md:grid-cols-2">
              {/* Upcoming Events */}
              <Card className="border-l-4 border-l-pink-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle>Upcoming Events</CardTitle>
                    <LucideCalendarRange className="h-5 w-5 text-pink-500" />
                  </div>
                  <CardDescription>Scheduled breeding activities</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {isLoading ? (
                      <div className="flex flex-col items-center justify-center py-8">
                        <LucideLoader2 className="h-8 w-8 text-pink-500 animate-spin mb-3" />
                        <p className="text-sm text-muted-foreground">Loading events...</p>
                      </div>
                    ) : breedingData.upcomingEvents && breedingData.upcomingEvents.length > 0 ? (
                      breedingData.upcomingEvents.map((event: any) => (
                        <div
                          key={event.id}
                          className="flex items-start justify-between p-3 bg-pink-50/50 rounded-md border border-pink-100 hover:bg-pink-50 transition-colors"
                        >
                          <div className="flex items-start gap-3">
                            <div className="mt-0.5">{getEventTypeBadge(event.type)}</div>
                            <div>
                              <div className="font-medium">{event.title}</div>
                              <div className="text-sm text-muted-foreground">{event.description}</div>
                            </div>
                          </div>
                          <div className="text-sm font-medium text-pink-600">
                            {formatDate(event.date)}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-3 text-center text-muted-foreground">No upcoming events</div>
                    )}
                  </div>
                </CardContent>
                <CardFooter>
                  <Link href="/breeding/calendar" className="w-full">
                    <Button
                      variant="outline"
                      className="w-full border-pink-200 text-pink-600 hover:bg-pink-50 hover:text-pink-700"
                    >
                      <LucideCalendarRange className="mr-2 h-4 w-4" />
                      View Full Calendar
                    </Button>
                  </Link>
                </CardFooter>
              </Card>

              {/* Pregnant Animals */}
              <Card className="border-l-4 border-l-rose-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle>Pregnant Animals</CardTitle>
                    <LucideHeart className="h-5 w-5 text-rose-500" />
                  </div>
                  <CardDescription>Currently pregnant animals and due dates</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-[300px] overflow-y-auto pr-1">
                    {isLoading ? (
                      <div className="flex flex-col items-center justify-center py-8">
                        <LucideLoader2 className="h-8 w-8 text-rose-500 animate-spin mb-3" />
                        <p className="text-sm text-muted-foreground">Loading pregnant animals...</p>
                      </div>
                    ) : breedingData.upcomingBirths && breedingData.upcomingBirths.length > 0 ? (
                      breedingData.upcomingBirths.map((birth: any) => {
                        const daysLeft = calculateDaysLeft(birth.expected_kidding_date);
                        return (
                          <div
                            key={birth.id}
                            className="flex items-start justify-between p-3 bg-rose-50/50 rounded-md border border-rose-100 hover:bg-rose-50 transition-colors"
                          >
                            <div className="flex items-start gap-3">
                              <div className="mt-0.5">{getPregnancyStatusBadge(daysLeft)}</div>
                              <div>
                                <div className="font-medium">{birth.female_name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {birth.female_breed} {birth.female_type}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-medium text-rose-600">
                                Due: {formatDate(birth.expected_kidding_date)}
                              </div>
                              <div className="text-xs text-muted-foreground">{daysLeft} days left</div>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="p-3 text-center text-muted-foreground">No pregnant does found</div>
                    )}
                  </div>
                </CardContent>
                <CardFooter>
                  <Link href="/breeding/pregnant" className="w-full">
                    <Button
                      variant="outline"
                      className="w-full border-rose-200 text-rose-600 hover:bg-rose-50 hover:text-rose-700"
                    >
                      <LucideUsers className="mr-2 h-4 w-4" />
                      View All Pregnant Does
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            </div>

            {/* Recent Breedings and Heat Cycles */}
            <div className="grid gap-6 md:grid-cols-2">
              {/* Recent Breedings */}
              <Card className="border-l-4 border-l-purple-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle>Recent Breedings</CardTitle>
                    <LucideCalendarClock className="h-5 w-5 text-purple-500" />
                  </div>
                  <CardDescription>Latest breeding records</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {isLoading ? (
                      <div className="flex flex-col items-center justify-center py-8">
                        <LucideLoader2 className="h-8 w-8 text-purple-500 animate-spin mb-3" />
                        <p className="text-sm text-muted-foreground">Loading breeding records...</p>
                      </div>
                    ) : breedingData.records && breedingData.records.length > 0 ? (
                      // Show only the first 5 records
                      breedingData.records.slice(0, 5).map((breeding: any) => (
                        <div
                          key={breeding.id}
                          className="flex items-start justify-between p-3 bg-purple-50/50 rounded-md border border-purple-100 hover:bg-purple-50 transition-colors"
                        >
                          <div className="flex items-start gap-3">
                            <div className="mt-0.5">{getBreedingStatusBadge(breeding.status)}</div>
                            <div>
                              <div className="font-medium">
                                {breeding.female_name} {breeding.male_name ? `× ${breeding.male_name}` : ''}
                              </div>
                              <div className="text-sm text-muted-foreground">{breeding.breeding_method}</div>
                            </div>
                          </div>
                          <div className="text-sm font-medium text-purple-600">
                            {formatDate(breeding.breeding_date)}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-3 text-center text-muted-foreground">No breeding records found</div>
                    )}
                  </div>
                </CardContent>
                <CardFooter>
                  <Link href="/breeding?tab=breeding" className="w-full">
                    <Button
                      variant="outline"
                      className="w-full border-purple-200 text-purple-600 hover:bg-purple-50 hover:text-purple-700"
                      onClick={() => setActiveTab("breeding")}
                    >
                      <LucideClipboard className="mr-2 h-4 w-4" />
                      View All Breeding Records
                    </Button>
                  </Link>
                </CardFooter>
              </Card>

              {/* Recent Heat Cycles */}
              <Card className="border-l-4 border-l-pink-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle>Recent Heat Cycles</CardTitle>
                    <LucideHeart className="h-5 w-5 text-pink-500" />
                  </div>
                  <CardDescription>Latest heat detection records</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {isLoading ? (
                      <div className="flex flex-col items-center justify-center py-8">
                        <LucideLoader2 className="h-8 w-8 text-pink-500 animate-spin mb-3" />
                        <p className="text-sm text-muted-foreground">Loading heat cycles...</p>
                      </div>
                    ) : breedingData.heatCycles && breedingData.heatCycles.length > 0 ? (
                      breedingData.heatCycles.map((cycle: any) => (
                        <div
                          key={cycle.id}
                          className="flex items-start justify-between p-3 bg-pink-50/50 rounded-md border border-pink-100 hover:bg-pink-50 transition-colors"
                        >
                          <div>
                            <div className="font-medium">{cycle.goat_name}</div>
                            <div className="text-sm text-muted-foreground">
                              Intensity: <span className="font-medium capitalize">{cycle.intensity}</span> •
                              {cycle.breeding_scheduled ? (
                                <span> Action: <span className="font-medium">Breeding Scheduled</span></span>
                              ) : (
                                <span> Action: <span className="font-medium">No Action</span></span>
                              )}
                            </div>
                          </div>
                          <div className="text-sm font-medium text-pink-600">
                            {formatDate(cycle.heat_date)}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-3 text-center text-muted-foreground">No heat cycles found</div>
                    )}
                  </div>
                </CardContent>
                <CardFooter>
                  <Link href="/breeding?tab=heat" className="w-full">
                    <Button
                      variant="outline"
                      className="w-full border-pink-200 text-pink-600 hover:bg-pink-50 hover:text-pink-700"
                      onClick={() => setActiveTab("heat")}
                    >
                      <LucideArrowUpRight className="mr-2 h-4 w-4" />
                      View All Heat Cycles
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>

          {/* Breeding Records Tab Content */}
          <TabsContent value="breeding" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                  <div>
                    <CardTitle>Breeding Records</CardTitle>
                    <CardDescription>All breeding events for your goats</CardDescription>
                  </div>
                  <Link href="/breeding/add">
                    <Button className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
                      <LucidePlus className="mr-2 h-4 w-4" />
                      Add Breeding Record
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                    <p className="text-muted-foreground">Loading breeding records...</p>
                  </div>
                ) : breedingData.records && breedingData.records.length > 0 ? (
                  <div className="overflow-x-auto">
                    <div className="relative flex items-center mb-4">
                      <LucideSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <input
                        type="text"
                        placeholder="Search breeding records..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                      />
                    </div>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Female</TableHead>
                          <TableHead>Male</TableHead>
                          <TableHead>Breeding Date</TableHead>
                          <TableHead>Method</TableHead>
                          <TableHead>Expected Birth</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredRecords.map((record: any) => (
                          <TableRow key={record.id}>
                            <TableCell className="font-medium">
                              <div className="flex flex-col">
                                <span>{record.female_name || "Unknown"}</span>
                                <span className="text-xs text-muted-foreground">#{record.female_tag}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-col">
                                <span>{record.male_name || "N/A"}</span>
                                {record.male_tag && (
                                  <span className="text-xs text-muted-foreground">#{record.male_tag}</span>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>{formatDate(record.breeding_date)}</TableCell>
                            <TableCell>{record.breeding_method}</TableCell>
                            <TableCell>
                              {record.expected_kidding_date ? formatDate(record.expected_kidding_date) : "N/A"}
                            </TableCell>
                            <TableCell>{getBreedingStatusBadge(record.status)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <LucideCalendarClock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-1">No breeding records found</h3>
                    <p className="text-muted-foreground mb-4">Add your first breeding record to get started.</p>
                    <Link href="/breeding/add">
                      <Button className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white">
                        <LucidePlus className="mr-2 h-4 w-4" />
                        Add Breeding Record
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Kidding Records Tab Content */}
          <TabsContent value="kidding" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                  <div>
                    <CardTitle>Kidding Records</CardTitle>
                    <CardDescription>Kids 6 months old or younger</CardDescription>
                  </div>
                  <Link href="/register">
                    <Button className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
                      <LucidePlus className="mr-2 h-4 w-4" />
                      Add Animal
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                    <p className="mt-4 text-muted-foreground">Loading kidding records...</p>
                  </div>
                ) : kidsData && kidsData.kids && kidsData.kids.length > 0 ? (
                  filteredKids.length > 0 ? (
                  <div className="space-y-4">
                    {/* Kids Statistics */}
                    <div className="grid gap-4 md:grid-cols-4 mb-6">
                      <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                        <CardContent className="pt-6">
                          <div className="text-2xl font-bold text-blue-700">{kidsData.stats.totalKids}</div>
                          <p className="text-xs text-blue-600 mt-1">Total Kids</p>
                        </CardContent>
                      </Card>
                      <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100 border-indigo-200">
                        <CardContent className="pt-6">
                          <div className="text-2xl font-bold text-indigo-700">{kidsData.stats.maleCount}</div>
                          <p className="text-xs text-indigo-600 mt-1">Male Kids</p>
                        </CardContent>
                      </Card>
                      <Card className="bg-gradient-to-br from-pink-50 to-pink-100 border-pink-200">
                        <CardContent className="pt-6">
                          <div className="text-2xl font-bold text-pink-700">{kidsData.stats.femaleCount}</div>
                          <p className="text-xs text-pink-600 mt-1">Female Kids</p>
                        </CardContent>
                      </Card>
                      <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                        <CardContent className="pt-6">
                          <div className="text-2xl font-bold text-green-700">{kidsData.stats.avgWeight} kg</div>
                          <p className="text-xs text-green-600 mt-1">Avg Weight</p>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Kids Search */}
                    <div className="space-y-3 mb-4">
                      <div className="relative flex items-center">
                        <LucideSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <input
                          type="text"
                          placeholder="Search kids by name, tag, type, breed, gender, status..."
                          value={kidsSearchTerm}
                          onChange={(e) => setKidsSearchTerm(e.target.value)}
                          className="w-full rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                        />
                        {kidsSearchTerm && (
                          <button
                            onClick={() => setKidsSearchTerm("")}
                            className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
                          >
                            <LucideX className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                      {kidsSearchTerm && (
                        <div className="text-sm text-muted-foreground">
                          Found {filteredKids.length} of {kidsData.kids.length} kids
                        </div>
                      )}
                    </div>

                    {/* Kids Table */}
                    <div className="relative w-full overflow-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Tag Number</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Animal Type</TableHead>
                            <TableHead>Breed</TableHead>
                            <TableHead>Gender</TableHead>
                            <TableHead>Birth Date</TableHead>
                            <TableHead>Age</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Weight (kg)</TableHead>
                            <TableHead>Sire</TableHead>
                            <TableHead>Notes</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredKids.map((kid: any) => (
                            <TableRow key={kid.id}>
                              <TableCell className="font-medium">{kid.tag_number}</TableCell>
                              <TableCell>{kid.name}</TableCell>
                              <TableCell>
                                <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200 border-purple-300">
                                  {kid.animal_type || 'Unknown'}
                                </Badge>
                              </TableCell>
                              <TableCell>{kid.breed}</TableCell>
                              <TableCell>
                                <Badge className={kid.gender === 'Male' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800'}>
                                  {kid.gender}
                                </Badge>
                              </TableCell>
                              <TableCell>{formatDate(kid.birth_date)}</TableCell>
                              <TableCell>
                                <div className="flex flex-col">
                                  <span className="text-sm">{kid.age_months}m {kid.age_days % 30}d</span>
                                  <span className="text-xs text-muted-foreground">({kid.age_days} days)</span>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge className={
                                  kid.status === 'Healthy' ? 'bg-green-100 text-green-800' :
                                  kid.status === 'Sick' ? 'bg-red-100 text-red-800' :
                                  'bg-gray-100 text-gray-800'
                                }>
                                  {kid.status}
                                </Badge>
                              </TableCell>
                              <TableCell>{kid.weight ? kid.weight : 'N/A'}</TableCell>
                              <TableCell>
                                {kid.sire_name ? (
                                  <div className="flex flex-col">
                                    <span className="text-sm">{kid.sire_name}</span>
                                    <span className="text-xs text-muted-foreground">#{kid.sire_tag}</span>
                                  </div>
                                ) : (
                                  <span className="text-muted-foreground">Unknown</span>
                                )}
                              </TableCell>
                              <TableCell className="max-w-xs truncate">{kid.notes || '-'}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <LucideSearch className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                      <h3 className="text-lg font-medium mb-2">No Search Results</h3>
                      <p className="max-w-md mx-auto mb-6">
                        No kids found matching "{kidsSearchTerm}". Try adjusting your search terms.
                      </p>
                      <Button
                        onClick={() => setKidsSearchTerm("")}
                        variant="outline"
                        className="border-red-200 text-red-600 hover:bg-red-50"
                      >
                        Clear Search
                      </Button>
                    </div>
                  )
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <LucideBaby className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                    <h3 className="text-lg font-medium mb-2">No Kids Found</h3>
                    <p className="max-w-md mx-auto mb-6">
                      No animals 6 months old or younger found. Add new animals with birth dates to see them here.
                    </p>
                    <Link href="/register">
                      <Button className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white">
                        <LucidePlus className="mr-2 h-4 w-4" />
                        Add Animal
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Heat Cycles Tab Content */}
          <TabsContent value="heat" className="space-y-6">
            {/* Heat Cycle Stats */}
            <div className="grid gap-4 md:grid-cols-3">
              <Card className="stat-card-primary">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-muted-foreground">Total Heat Records</div>
                      <div className="text-2xl font-bold text-orange-600">
                        {isLoading ? (
                          <LucideLoader2 className="h-5 w-5 animate-spin" />
                        ) : (
                          breedingData.stats?.totalHeatCycles || 0
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Total records in heat_cycles table
                      </div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-orange-100 flex items-center justify-center">
                      <LucideHeart className="h-5 w-5 text-orange-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="stat-card-secondary">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-muted-foreground">Does Currently in Heat</div>
                      <div className="text-2xl font-bold text-amber-600">
                        {isLoading ? (
                          <LucideLoader2 className="h-5 w-5 animate-spin" />
                        ) : (
                          breedingData.stats?.animalsInHeat || 0
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Records from last 2 days
                      </div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                      <LucideAlertCircle className="h-5 w-5 text-amber-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="stat-card-accent">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-muted-foreground">Avg. Cycle Length</div>
                      <div className="text-2xl font-bold text-rose-600">
                        {isLoading ? (
                          <LucideLoader2 className="h-5 w-5 animate-spin" />
                        ) : (
                          breedingData.stats?.cycleLengths && breedingData.stats.cycleLengths.length > 0
                            ? `${Math.round(breedingData.stats.cycleLengths.reduce((sum, cycle) =>
                                sum + Number(cycle.avg_cycle_length), 0) / breedingData.stats.cycleLengths.length)} days`
                            : "21 days"
                        )}
                      </div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-rose-100 flex items-center justify-center">
                      <LucideCalendarRange className="h-5 w-5 text-rose-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Heat Cycle Records */}
            <Card>
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                  <div>
                    <CardTitle>Heat Cycle Records</CardTitle>
                    <CardDescription>Track estrus cycles for your does</CardDescription>
                  </div>
                  <Link href="/breeding/heat-cycle">
                    <Button className="bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
                      <LucidePlus className="mr-2 h-4 w-4" />
                      Record Heat Cycle
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                    <p className="text-muted-foreground">Loading heat cycle records...</p>
                  </div>
                ) : breedingData.heatCycles && breedingData.heatCycles.length > 0 ? (
                  <div className="overflow-x-auto">
                    <div className="relative flex items-center mb-4">
                      <LucideSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <input
                        type="text"
                        placeholder="Search heat cycle records..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                      />
                    </div>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Animal</TableHead>
                          <TableHead>Heat Date</TableHead>
                          <TableHead>Intensity</TableHead>
                          <TableHead>Signs</TableHead>
                          <TableHead>Breeding Scheduled</TableHead>
                          <TableHead>Notes</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {breedingData.heatCycles.map((cycle: any) => (
                          <TableRow key={cycle.id}>
                            <TableCell className="font-medium">
                              <div className="flex flex-col">
                                <span>{cycle.animal_name || "Unknown"}</span>
                                <span className="text-xs text-muted-foreground">#{cycle.animal_tag}</span>
                              </div>
                            </TableCell>
                            <TableCell>{formatDate(cycle.heat_date)}</TableCell>
                            <TableCell>
                              <Badge
                                className={
                                  cycle.intensity === "strong"
                                    ? "bg-red-100 text-red-800 hover:bg-red-200 border-red-300"
                                    : cycle.intensity === "moderate"
                                    ? "bg-amber-100 text-amber-800 hover:bg-amber-200 border-amber-300"
                                    : "bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300"
                                }
                              >
                                {cycle.intensity ? cycle.intensity.charAt(0).toUpperCase() + cycle.intensity.slice(1) : "Unknown"}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {cycle.signs ? (
                                <div className="flex flex-wrap gap-1">
                                  {typeof cycle.signs === 'string'
                                    ? cycle.signs.split(',').map((sign: string, index: number) => (
                                        <Badge key={index} variant="outline" className="bg-pink-50 text-pink-800 border-pink-200">
                                          {sign}
                                        </Badge>
                                      ))
                                    : cycle.signs.map((sign: string, index: number) => (
                                        <Badge key={index} variant="outline" className="bg-pink-50 text-pink-800 border-pink-200">
                                          {sign}
                                        </Badge>
                                      ))
                                  }
                                </div>
                              ) : (
                                "None recorded"
                              )}
                            </TableCell>
                            <TableCell>
                              {cycle.breeding_scheduled ? (
                                <Badge className="bg-green-100 text-green-800 hover:bg-green-200 border-green-300">
                                  Yes
                                </Badge>
                              ) : (
                                <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-200 border-gray-300">
                                  No
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell className="max-w-[200px] truncate" title={cycle.notes || ""}>
                              {cycle.notes || "No notes"}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <LucideHeart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-1">No heat cycle records found</h3>
                    <p className="text-muted-foreground mb-4">Add your first heat cycle record to get started.</p>
                    <Link href="/breeding/heat-cycle">
                      <Button className="bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white">
                        <LucidePlus className="mr-2 h-4 w-4" />
                        Record Heat Cycle
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Upcoming Heat Predictions */}
            <Card className="border-l-4 border-l-amber-500">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle>Upcoming Heat Predictions</CardTitle>
                  <LucideCalendarRange className="h-5 w-5 text-amber-500" />
                </div>
                <CardDescription>Predicted heat cycles based on previous records</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {isLoading ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <LucideLoader2 className="h-8 w-8 text-amber-500 animate-spin mb-3" />
                      <p className="text-sm text-muted-foreground">Calculating predictions...</p>
                    </div>
                  ) : breedingData.stats?.upcomingHeat && breedingData.stats.upcomingHeat.length > 0 ? (
                    <div className="space-y-4">
                      {breedingData.stats.upcomingHeat.map((prediction) => (
                        <div
                          key={prediction.goat_id}
                          className="flex items-start justify-between p-3 bg-amber-50/50 rounded-md border border-amber-100 hover:bg-amber-50 transition-colors"
                        >
                          <div className="flex items-start gap-3">
                            <div className="mt-0.5">
                              <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200 border-amber-300">
                                Predicted
                              </Badge>
                            </div>
                            <div>
                              <div className="font-medium">{prediction.goat_name}</div>
                              <div className="text-sm text-muted-foreground">
                                Tag: {prediction.tag_number} • Last heat: {formatDate(prediction.last_heat_date)}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium text-amber-600">
                              {formatDate(prediction.predicted_next_heat)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {calculateDaysLeft(prediction.predicted_next_heat)} days from now
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <LucideCalendarRange className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                      <h3 className="text-lg font-medium mb-2">No Upcoming Heat Predictions</h3>
                      <p className="max-w-md mx-auto mb-6">
                        Record more heat cycles to generate predictions for your does.
                      </p>
                      <Link href="/breeding/heat-cycle">
                        <Button variant="outline" className="border-amber-200 text-amber-600 hover:bg-amber-50">
                          <LucidePlus className="mr-2 h-4 w-4" />
                          Record Heat Cycle
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Heat Cycle Patterns */}
            <Card className="border-l-4 border-l-orange-500">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle>Heat Cycle Patterns</CardTitle>
                  <LucideFilter className="h-5 w-5 text-orange-500" />
                </div>
                <CardDescription>Analysis of your does' heat cycle patterns</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {isLoading ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <LucideLoader2 className="h-8 w-8 text-orange-500 animate-spin mb-3" />
                      <p className="text-sm text-muted-foreground">Analyzing patterns...</p>
                    </div>
                  ) : breedingData.stats?.cycleLengths && breedingData.stats.cycleLengths.length > 0 ? (
                    <div className="space-y-4">
                      {breedingData.stats.cycleLengths.map((cycle) => (
                        <div
                          key={cycle.goat_id}
                          className="flex items-start justify-between p-3 bg-orange-50/50 rounded-md border border-orange-100 hover:bg-orange-50 transition-colors"
                        >
                          <div>
                            <div className="font-medium">{cycle.goat_name}</div>
                            <div className="text-sm text-muted-foreground">
                              Goat ID: {cycle.goat_id}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium text-orange-600">
                              {Math.round(Number(cycle.avg_cycle_length))} days
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Average cycle length
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <LucideFilter className="h-12 w-12 mx-auto mb-4 text-muted-foreground/50" />
                      <h3 className="text-lg font-medium mb-2">No Cycle Patterns Available</h3>
                      <p className="max-w-md mx-auto mb-6">
                        Record at least two heat cycles for the same doe to see cycle patterns.
                      </p>
                      <Link href="/breeding/heat-cycle">
                        <Button variant="outline" className="border-orange-200 text-orange-600 hover:bg-orange-50">
                          <LucidePlus className="mr-2 h-4 w-4" />
                          Record Heat Cycle
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}








