"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LucideWifiOff, LucideRefreshCw, LucideHome } from "lucide-react"
import Link from "next/link"

export default function OfflinePage() {
  const [isOnline, setIsOnline] = useState(false)

  useEffect(() => {
    // Check if we're online
    setIsOnline(navigator.onLine)

    // Listen for online/offline events
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const handleRetry = () => {
    if (navigator.onLine) {
      window.location.reload()
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <LucideWifiOff className="h-8 w-8 text-red-600" />
          </div>
          <CardTitle className="text-2xl">You're Offline</CardTitle>
          <CardDescription>
            {isOnline 
              ? "Connection restored! You can now reload the page."
              : "It looks like you've lost your internet connection. Some features may not be available."
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">What you can do:</h3>
            <ul className="text-sm text-blue-800 space-y-1 list-disc list-inside">
              <li>View previously loaded pages</li>
              <li>Access cached data</li>
              <li>Wait for connection to restore</li>
            </ul>
          </div>

          <div className="flex flex-col gap-2">
            <Button 
              onClick={handleRetry}
              className="w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700"
              disabled={!isOnline}
            >
              <LucideRefreshCw className="mr-2 h-4 w-4" />
              {isOnline ? "Reload Page" : "Waiting for Connection..."}
            </Button>

            <Link href="/dashboard" className="w-full">
              <Button variant="outline" className="w-full">
                <LucideHome className="mr-2 h-4 w-4" />
                Go to Dashboard
              </Button>
            </Link>
          </div>

          <div className="text-center text-sm text-muted-foreground">
            {isOnline ? (
              <span className="text-green-600 font-medium">✓ Connection restored</span>
            ) : (
              <span className="text-red-600">✗ No internet connection</span>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

