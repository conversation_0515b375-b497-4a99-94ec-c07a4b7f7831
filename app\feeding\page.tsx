"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  LucideClipboardList,
  LucidePlus,
  LucideCalendarClock,
  LucideDroplet,
  LucideUtensils,
  LucidePackage,
  LucideBarChart3,
  LucideClipboard,
  LucideCheck,
  LucideAlertCircle,
  LucideArrowUpRight,
  LucideArrowDownRight,
  LucideActivity,
  LucideLoader2,
  LucideRefreshCw,
  LucideSearch,
  LucideFilter
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "@/components/ui/use-toast"

export default function FeedingPage() {
  const [activeTab, setActiveTab] = useState("inventory")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [feedItems, setFeedItems] = useState([])
  const [groupedItems, setGroupedItems] = useState({})
  const [feedCategories, setFeedCategories] = useState([])
  const [feedingRecords, setFeedingRecords] = useState([])
  const [feedingStats, setFeedingStats] = useState({
    total_records: 0,
    low_consumption: 0,
    normal_consumption: 0,
    high_consumption: 0,
    water_refilled_count: 0,
    feeding_days: 0
  })
  const [recentRecords, setRecentRecords] = useState([])

  // Fetch feed items and feeding records
  const fetchData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Fetch feed items
      const itemsResponse = await fetch('/api/feeding/feed-items')
      if (!itemsResponse.ok) {
        throw new Error(`Failed to fetch feed items: ${itemsResponse.status}`)
      }
      const itemsData = await itemsResponse.json()
      console.log('Feed items data:', itemsData)
      setFeedItems(itemsData.items || [])
      setGroupedItems(itemsData.groupedItems || {})
      setFeedCategories(itemsData.categories || [])

      // Fetch feeding records
      const recordsResponse = await fetch('/api/feeding/records')
      if (!recordsResponse.ok) {
        throw new Error(`Failed to fetch feeding records: ${recordsResponse.status}`)
      }
      const recordsData = await recordsResponse.json()
      console.log('Feeding records data:', recordsData)
      setFeedingRecords(recordsData.records || [])
      setFeedingStats(recordsData.stats || {})
      setRecentRecords(recordsData.recentRecords || [])
    } catch (err) {
      console.error('Error fetching data:', err)
      setError(err.message)
      toast({
        title: "Error",
        description: "Failed to load feeding data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch data on component mount
  useEffect(() => {
    fetchData()
  }, [])
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A"
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Get consumption level badge
  const getConsumptionBadge = (level) => {
    switch (level?.toLowerCase()) {
      case 'low':
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200 border-amber-300">Low</Badge>
      case 'high':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200 border-green-300">High</Badge>
      default:
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300">Normal</Badge>
    }
  }

  // Get inventory status badge
  const getInventoryStatusBadge = (status) => {
    switch (status) {
      case 'low':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200 border-red-300">Low</Badge>
      case 'full':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200 border-green-300">Full</Badge>
      default:
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-300">Normal</Badge>
    }
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center gap-3">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-secondary">Feeding Management</h1>
          {isLoading && <LucideLoader2 className="h-5 w-5 text-amber-500 animate-spin" />}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={fetchData}
            disabled={isLoading}
            className="h-10 w-10 rounded-full"
            title="Refresh data"
          >
            <LucideRefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          <Link href="/feeding/record">
            <Button
              variant="outline"
              className="border-2 border-amber-500 text-amber-600 hover:bg-amber-50 hover:text-amber-700 transition-all duration-300"
            >
              <LucideClipboardList className="mr-2 h-4 w-4" />
              Record Feeding
            </Button>
          </Link>
          <Link href="/feeding/inventory/add">
            <Button className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
              <LucidePlus className="mr-2 h-4 w-4" />
              Add Feed Inventory
            </Button>
          </Link>
        </div>
      </div>

      {/* Error message */}
      {error && !isLoading && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
          <h3 className="font-medium flex items-center gap-2">
            <LucideAlertCircle className="h-5 w-5" />
            Error Loading Feeding Data
          </h3>
          <p className="mt-1 text-sm">{error}</p>
          <Button
            variant="outline"
            className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
            onClick={fetchData}
          >
            Retry
          </Button>
        </div>
      )}

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 p-1 bg-gradient-to-r from-amber-50 via-yellow-50 to-lime-50 rounded-xl">
          <TabsTrigger
            value="inventory"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-500 data-[state=active]:to-yellow-500 data-[state=active]:text-white transition-all duration-300 hover:text-amber-700"
          >
            Feed Inventory
          </TabsTrigger>
          <TabsTrigger
            value="schedules"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-yellow-500 data-[state=active]:to-lime-500 data-[state=active]:text-white transition-all duration-300 hover:text-yellow-700"
          >
            Feeding Schedules
          </TabsTrigger>
          <TabsTrigger
            value="records"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-lime-500 data-[state=active]:to-green-500 data-[state=active]:text-white transition-all duration-300 hover:text-lime-700"
          >
            Feeding Records
          </TabsTrigger>
        </TabsList>

        {/* Inventory Tab Content */}
        <TabsContent value="inventory" className="space-y-6 mt-6">
          {/* Feed Inventory Stats */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card className="bg-gradient-to-br from-amber-50 to-yellow-50">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium flex items-center gap-2">
                  <LucidePackage className="h-5 w-5 text-amber-500" />
                  Feed Categories
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-amber-600">
                  {feedCategories.length}
                </div>
                <p className="text-sm text-muted-foreground">
                  Different feed categories
                </p>
                {feedCategories.length > 0 && (
                  <div className="mt-2 text-xs text-muted-foreground">
                    {feedCategories.map(cat => (
                      <span key={cat.category} className="inline-block mr-2 mb-1">
                        <Badge variant="outline" className="bg-amber-50">
                          {cat.category || 'Uncategorized'}: {cat.count}
                        </Badge>
                      </span>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-yellow-50 to-lime-50">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium flex items-center gap-2">
                  <LucideUtensils className="h-5 w-5 text-lime-500" />
                  Feed Items
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-lime-600">
                  {feedItems?.length || 0}
                </div>
                <p className="text-sm text-muted-foreground">
                  Total feed items in inventory
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-lime-50 to-green-50">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium flex items-center gap-2">
                  <LucideArrowDownRight className="h-5 w-5 text-red-500" />
                  Low Stock Items
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {feedItems?.filter(item => item.status === 'low').length || 0}
                </div>
                <p className="text-sm text-muted-foreground">
                  Items that need restocking
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-50 to-emerald-50">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium flex items-center gap-2">
                  <LucideBarChart3 className="h-5 w-5 text-emerald-500" />
                  Inventory Value
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-emerald-600">
                  MWK {feedItems?.reduce((total, item) => {
                    const price = item.price_per_unit || 0;
                    const quantity = item.quantity || 0;
                    return total + (price * quantity);
                  }, 0).toLocaleString() || 0}
                </div>
                <p className="text-sm text-muted-foreground">
                  Total value of inventory
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Feed Inventory Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LucidePackage className="h-5 w-5 text-amber-500" />
                Feed Inventory Status
              </CardTitle>
              <CardDescription>
                Current inventory levels for all feed items
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                  <p className="text-muted-foreground">Loading feed inventory...</p>
                </div>
              ) : feedItems?.length > 0 ? (
                <div className="space-y-6">
                  {Object.entries(groupedItems).map(([category, items]) => (
                    <div key={category} className="space-y-4">
                      <h3 className="text-lg font-medium">{category}</h3>
                      <div className="space-y-4">
                        {items.map(item => (
                          <div key={item.id} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <div className={`h-4 w-4 rounded-full ${
                                  item.status === 'low'
                                    ? 'bg-red-500'
                                    : item.status === 'full'
                                      ? 'bg-green-500'
                                      : 'bg-amber-500'
                                }`}></div>
                                <span className="text-sm font-medium">{item.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">{item.quantity} {item.unit}</span>
                                {getInventoryStatusBadge(item.status)}
                              </div>
                            </div>
                            <div className={`progress-${
                              item.status === 'low'
                                ? 'destructive'
                                : item.status === 'full'
                                  ? 'success'
                                  : 'primary'
                            }`}>
                              <div className="indicator" style={{ width: `${item.percentFull}%` }}></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <LucidePackage className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-1">No feed items found</h3>
                  <p className="text-muted-foreground mb-4">Add your first feed item to get started.</p>
                  <Link href="/feeding/inventory/add">
                    <Button className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white">
                      <LucidePlus className="mr-2 h-4 w-4" />
                      Add Feed Item
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Schedules Tab Content */}
        <TabsContent value="schedules" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LucideCalendarClock className="h-5 w-5 text-yellow-500" />
                Feeding Schedules
              </CardTitle>
              <CardDescription>
                Manage your regular feeding schedules
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <LucideCalendarClock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-1">No feeding schedules found</h3>
                <p className="text-muted-foreground mb-4">Create your first feeding schedule to get started.</p>
                <Link href="/feeding/schedules/add">
                  <Button className="bg-gradient-to-r from-yellow-500 to-lime-500 hover:from-yellow-600 hover:to-lime-600 text-white">
                    <LucidePlus className="mr-2 h-4 w-4" />
                    Create Feeding Schedule
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Records Tab Content */}
        <TabsContent value="records" className="space-y-6 mt-6">
          {/* Feeding Stats */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card className="bg-gradient-to-br from-lime-50 to-green-50">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium flex items-center gap-2">
                  <LucideClipboard className="h-5 w-5 text-lime-500" />
                  Total Feedings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-lime-600">
                  {feedingStats.total_records || 0}
                </div>
                <p className="text-sm text-muted-foreground">
                  Recorded feeding sessions
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-50 to-emerald-50">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium flex items-center gap-2">
                  <LucideDroplet className="h-5 w-5 text-emerald-500" />
                  Water Refills
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-emerald-600">
                  {feedingStats.water_refilled_count || 0}
                </div>
                <p className="text-sm text-muted-foreground">
                  Times water was refilled
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-emerald-50 to-teal-50">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium flex items-center gap-2">
                  <LucideCalendarClock className="h-5 w-5 text-teal-500" />
                  Feeding Days
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-teal-600">
                  {feedingStats.feeding_days || 0}
                </div>
                <p className="text-sm text-muted-foreground">
                  Days with feeding records
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-teal-50 to-cyan-50">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium flex items-center gap-2">
                  <LucideActivity className="h-5 w-5 text-cyan-500" />
                  Consumption Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-cyan-600">
                  {feedingStats.normal_consumption > feedingStats.low_consumption &&
                   feedingStats.normal_consumption > feedingStats.high_consumption
                    ? "Normal"
                    : feedingStats.high_consumption > feedingStats.low_consumption
                      ? "High"
                      : "Low"}
                </div>
                <p className="text-sm text-muted-foreground">
                  Most common consumption level
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Feeding Records */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LucideClipboard className="h-5 w-5 text-green-500" />
                Recent Feeding Records
              </CardTitle>
              <CardDescription>
                Latest feeding records from the past week
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <LucideLoader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                  <p className="text-muted-foreground">Loading feeding records...</p>
                </div>
              ) : feedingRecords?.length > 0 ? (
                <div className="space-y-4">
                  {feedingRecords.slice(0, 5).map((record) => (
                    <div
                      key={record.id}
                      className="flex items-start justify-between p-3 bg-green-50/50 rounded-md border border-green-100 hover:bg-green-50 transition-colors"
                    >
                      <div className="flex items-start gap-3">
                        <div className="mt-0.5">
                          {getConsumptionBadge(record.consumption_level)}
                        </div>
                        <div>
                          <div className="font-medium">
                            {record.formatted_date} at {record.formatted_time}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {record.feedItems?.map(item => item.name).join(', ') || 'No feed items recorded'}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {record.water_refilled ? (
                          <LucideCheck className="h-4 w-4 text-green-500" title="Water refilled" />
                        ) : (
                          <LucideAlertCircle className="h-4 w-4 text-amber-500" title="Water not refilled" />
                        )}
                        <LucideDroplet className="h-4 w-4 text-blue-500" />
                      </div>
                    </div>
                  ))}
                  <div className="text-center mt-4">
                    <Link href="/feeding/records">
                      <Button variant="outline" className="w-full">
                        View All Feeding Records
                      </Button>
                    </Link>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <LucideClipboard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-1">No feeding records found</h3>
                  <p className="text-muted-foreground mb-4">Record your first feeding to get started.</p>
                  <Link href="/feeding/record">
                    <Button className="bg-gradient-to-r from-lime-500 to-green-500 hover:from-lime-600 hover:to-green-600 text-white">
                      <LucidePlus className="mr-2 h-4 w-4" />
                      Record Feeding
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

