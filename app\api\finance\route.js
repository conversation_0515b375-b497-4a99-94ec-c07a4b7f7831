import { NextResponse } from 'next/server'
import pool from '@/lib/db'

// Enhanced schema check with data validation
async function checkDatabaseSchema() {
  try {
    const [columns] = await pool.execute(`
      SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'financial_transactions'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('Database schema for financial_transactions:');
    columns.forEach(col => {
      console.log(`${col.COLUMN_NAME}: ${col.COLUMN_TYPE} ${col.IS_NULLABLE === 'NO' ? 'NOT NULL' : 'NULL'} DEFAULT ${col.COLUMN_DEFAULT || 'NULL'}`);
    });
    
    return columns;
  } catch (error) {
    console.error('Error checking database schema:', error);
    return null;
  }
}

// Validate data types against schema
function validateDataTypes(data, schema) {
  const issues = [];
  
  schema.forEach(column => {
    const fieldName = column.COLUMN_NAME;
    
    // Skip validation for id and created_at fields
    if (fieldName === 'id' || fieldName === 'created_at') {
      return;
    }
    
    const value = data[fieldName];
    
    // Skip if value is null/undefined and field allows NULL
    if ((value === null || value === undefined) && column.IS_NULLABLE === 'YES') {
      return;
    }
    
    // Check required fields
    if (column.IS_NULLABLE === 'NO' && (value === null || value === undefined)) {
      issues.push(`Field '${fieldName}' is required but missing`);
      return;
    }
    
    // Check data types
    if (value !== null && value !== undefined) {
      if (column.COLUMN_TYPE.includes('int') && typeof value !== 'number') {
        issues.push(`Field '${fieldName}' should be a number, got ${typeof value}: ${value}`);
      }
      else if (column.COLUMN_TYPE.includes('decimal') && typeof value !== 'number') {
        issues.push(`Field '${fieldName}' should be a number, got ${typeof value}: ${value}`);
      }
      else if (column.COLUMN_TYPE.includes('varchar') && typeof value !== 'string') {
        issues.push(`Field '${fieldName}' should be a string, got ${typeof value}: ${value}`);
      }
      else if (column.COLUMN_TYPE.includes('enum')) {
        // Extract enum values
        const enumValues = column.COLUMN_TYPE.match(/'([^']*)'/g).map(v => v.replace(/'/g, ''));
        
        // Case-insensitive check for enum values
        const normalizedValue = typeof value === 'string' ? value.toLowerCase() : value;
        const normalizedEnumValues = enumValues.map(v => v.toLowerCase());
        
        if (!normalizedEnumValues.includes(normalizedValue)) {
          issues.push(`Field '${fieldName}' should be one of [${enumValues.join(', ')}], got: ${value}`);
        }
      }
    }
  });
  
  return issues;
}

export async function POST(request) {
  console.log('=== FINANCE API POST REQUEST RECEIVED ===');
  
  // Check database schema first
  const schema = await checkDatabaseSchema();
  
  try {
    const data = await request.json();
    console.log('API received data:', JSON.stringify(data, null, 2));
    
    // Normalize transaction_type to lowercase
    if (data.transaction_type) {
      data.transaction_type = data.transaction_type.toLowerCase();
      console.log('Normalized transaction_type to:', data.transaction_type);
    }
    
    // Check for field name mismatches
    const expectedFields = [
      'transaction_type', 'amount', 'transaction_date', 'description', 
      'category', 'payment_method', 'reference_number', 'notes', 
      'is_recurring', 'receipt_path'
    ];
    
    const missingFields = expectedFields.filter(field => 
      !Object.keys(data).includes(field) && 
      !['category', 'payment_method', 'reference_number', 'notes', 'receipt_path'].includes(field)
    );
    
    const unexpectedFields = Object.keys(data).filter(field => 
      !expectedFields.includes(field)
    );
    
    if (missingFields.length > 0) {
      console.error('Field name mismatch - Missing expected fields:', missingFields);
    }
    
    if (unexpectedFields.length > 0) {
      console.error('Field name mismatch - Unexpected fields:', unexpectedFields);
    }
    
    // Validate required fields
    if (!data.transaction_type || !data.amount || !data.transaction_date || !data.description || !data.category || !data.payment_method) {
      console.error('Missing required fields:', { 
        transaction_type: data.transaction_type, 
        amount: data.amount, 
        transaction_date: data.transaction_date, 
        description: data.description,
        category: data.category,
        payment_method: data.payment_method
      });
      return NextResponse.json(
        { error: 'Missing required fields: transaction_type, amount, transaction_date, description, category, and payment_method are required' },
        { status: 400 }
      );
    }

    // Validate data types if schema is available
    if (schema) {
      // Filter out the 'id' field from validation since it's auto-incremented
      const dataTypeIssues = validateDataTypes({
        transaction_type: data.transaction_type.toLowerCase(), // Ensure lowercase
        amount: parseFloat(data.amount),
        transaction_date: data.transaction_date,
        description: data.description,
        category: data.category,
        payment_method: data.payment_method,
        reference_number: data.reference_number || null,
        notes: data.notes || null,
        is_recurring: data.is_recurring || 0,
        receipt_path: data.receipt_path || null
      }, schema.filter(col => col.COLUMN_NAME !== 'id' && col.COLUMN_NAME !== 'created_at'));
      
      if (dataTypeIssues.length > 0) {
        console.error('Data type validation issues:', dataTypeIssues);
        return NextResponse.json(
          { error: 'Data validation failed', issues: dataTypeIssues },
          { status: 400 }
        );
      }
    }
    
    // Extract the transaction data
    const {
      transaction_type,
      amount,
      transaction_date,
      description,
      category,
      payment_method,
      reference_number,
      notes,
      is_recurring,
      receipt_path
    } = data;

    // Use lowercase for transaction_type to match ENUM format in database
    const formattedTransactionType = transaction_type.toLowerCase();

    // Log the exact SQL parameters that will be used
    const sqlParams = [
      formattedTransactionType,
      parseFloat(amount), // Ensure amount is a number
      transaction_date,
      description,
      category,
      payment_method,
      reference_number || null,
      notes || null,
      is_recurring ? 1 : 0, // Ensure boolean is converted to 0/1
      receipt_path || null
    ];
    
    console.log('SQL parameters:', JSON.stringify(sqlParams, null, 2));

    // Generate the SQL query for debugging
    const sqlQuery = `
      INSERT INTO financial_transactions (
        transaction_type,
        amount,
        transaction_date,
        description,
        category,
        payment_method,
        reference_number,
        notes,
        is_recurring,
        receipt_path
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    console.log('SQL query:', sqlQuery);

    // Insert into financial_transactions table
    try {
      const [result] = await pool.execute(
        sqlQuery,
        sqlParams
      );

      console.log('Database insert result:', result);
      const transactionId = result.insertId;

      return NextResponse.json({ 
        success: true, 
        message: 'Transaction recorded successfully',
        transactionId
      });
    } catch (dbError) {
      console.error('Database error details:', {
        message: dbError.message,
        code: dbError.code,
        errno: dbError.errno,
        sqlMessage: dbError.sqlMessage,
        sqlState: dbError.sqlState,
        sql: dbError.sql
      });
      
      // Check for specific error types
      if (dbError.code === 'ER_BAD_FIELD_ERROR') {
        console.error('Column name mismatch detected!');
      } else if (dbError.code === 'ER_TRUNCATED_WRONG_VALUE_FOR_FIELD') {
        console.error('Data type mismatch detected!');
      } else if (dbError.code === 'ER_DATA_TOO_LONG') {
        console.error('Data too long for column!');
      }
      
      return NextResponse.json(
        { error: 'Database error: ' + dbError.message, sqlMessage: dbError.sqlMessage },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error creating financial transaction:', error);
    return NextResponse.json(
      { error: 'Failed to record transaction: ' + error.message },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const [transactions] = await pool.execute(`
      SELECT 
        *
      FROM 
        financial_transactions
      ORDER BY 
        transaction_date DESC
    `)

    return NextResponse.json(transactions)
  } catch (error) {
    console.error('Error fetching transactions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch transactions: ' + error.message },
      { status: 500 }
    )
  }
}


























