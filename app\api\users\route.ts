import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { hashPassword, isSecureHashingAvailable } from '@/lib/password-utils'

// GET /api/users - Get all users
export async function GET(request: NextRequest) {
  try {
    // Validate database connection
    if (!db || typeof db.query !== 'function') {
      console.error('Database connection not available');
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      );
    }

    try {
      // Test database connection
      await db.testConnection();
    } catch (dbError) {
      console.error('Database connection test failed:', dbError);
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }

    const users = await db.query(`
      SELECT id, username, email, full_name, role, created_at, last_login, is_active
      FROM users
      ORDER BY created_at DESC
    `);

    return NextResponse.json(users);
  } catch (error: any) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: `Failed to fetch users: ${error.message || 'Unknown error'}` },
      { status: 500 }
    );
  }
}

// POST /api/users - Create a new user
export async function POST(request: NextRequest) {
  try {
    // Validate database connection
    if (!db || typeof db.query !== 'function') {
      console.error('Database connection not available');
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      );
    }

    let requestData;
    try {
      requestData = await request.json();
    } catch (parseError) {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    const { username, password, email, full_name, role } = requestData;

    // Validate required fields
    if (!username || !password || !role) {
      return NextResponse.json(
        { error: 'Username, password, and role are required' },
        { status: 400 }
      );
    }

    try {
      // Test database connection
      await db.testConnection();
    } catch (dbError) {
      console.error('Database connection test failed:', dbError);
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }

    // Check if username already exists
    const existingUsers = await db.query(
      'SELECT id FROM users WHERE username = ?',
      [username]
    );

    // Check if any users were found with this username
    if (Array.isArray(existingUsers) && existingUsers.length > 0) {
      return NextResponse.json(
        { error: 'Username already exists' },
        { status: 400 }
      );
    }

    // Hash password
    const password_hash = await hashPassword(password);

    // Warn if secure hashing is not available
    if (!isSecureHashingAvailable()) {
      console.warn('Using fallback password hashing method - install bcrypt for secure hashing');
    }

    // Insert new user with active status
    const result = await db.query(
      `INSERT INTO users (username, password_hash, email, full_name, role, created_at, is_active)
       VALUES (?, ?, ?, ?, ?, NOW(), 1)`,
      [username, password_hash, email, full_name, role]
    );

    // Get the inserted ID
    let insertId;
    if (result && typeof result === 'object' && 'insertId' in result) {
      insertId = result.insertId;
    } else {
      // Fallback to querying for the user we just created
      const newUser = await db.query(
        'SELECT id FROM users WHERE username = ? ORDER BY created_at DESC LIMIT 1',
        [username]
      );

      if (Array.isArray(newUser) && newUser.length > 0) {
        // Safely access the id property
        const firstUser = newUser[0] as any;
        if (firstUser && typeof firstUser === 'object' && 'id' in firstUser) {
          insertId = firstUser.id;
        } else {
          insertId = 'unknown';
        }
      } else {
        insertId = 'unknown';
      }
    }

    return NextResponse.json(
      { id: insertId, username, email, full_name, role, is_active: true },
      { status: 201 }
    );
  } catch (error: any) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: `Failed to create user: ${error.message || 'Unknown error'}` },
      { status: 500 }
    );
  }
}
