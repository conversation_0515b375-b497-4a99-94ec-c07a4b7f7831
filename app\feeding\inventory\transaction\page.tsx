"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { LoadingButton } from "@/components/ui/loading-button"
import { LucideArrowLeft, LucideArrowDownRight, LucideArrowUpRight, LucideSave } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { BackButton } from "@/components/ui/back-button"

export default function FeedInventoryTransactionPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [feedItems, setFeedItems] = useState([])
  const [selectedItem, setSelectedItem] = useState<{ id: string, name: string, category: string, unit: string, animal_type: string } | null>(null)
  const [formData, setFormData] = useState({
    item: "",
    transactionType: "in",
    quantity: "",
    unit: "",
    date: new Date().toISOString().split("T")[0],
    notes: "",
  })

  // Fetch feed items from API
  useEffect(() => {
    const fetchFeedItems = async () => {
      try {
        const response = await fetch('/api/feeding/feed-items')
        if (response.ok) {
          const data = await response.json()
          setFeedItems(data.feedItems || [])
        } else {
          console.error('Failed to fetch feed items')
        }
      } catch (error) {
        console.error('Error fetching feed items:', error)
      }
    }

    fetchFeedItems()
  }, [])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSelectChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    if (field === "item") {
      const item = feedItems.find((item: any) => item.id === value)
      if (item) {
        setSelectedItem(item)
        setFormData(prev => ({ ...prev, unit: item.unit }))
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Prepare transaction data
      const transactionData = {
        itemId: formData.item,
        transactionType: formData.transactionType,
        quantity: parseFloat(formData.quantity),
        date: formData.date,
        notes: formData.notes,
        recordedBy: "Current User" // Replace with actual user info when available
      }

      console.log('Submitting feed inventory transaction:', transactionData)

      // Here you would typically send to an API endpoint
      // const response = await fetch('/api/feeding/inventory/transactions', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(transactionData)
      // })

      // For now, simulate success
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast({
        title: "Transaction Recorded",
        description: `Feed inventory ${formData.transactionType === 'in' ? 'restocked' : 'used'} successfully.`,
      })

      // Reset form
      setFormData({
        item: "",
        transactionType: "in",
        quantity: "",
        unit: "",
        date: new Date().toISOString().split("T")[0],
        notes: "",
      })
      setSelectedItem(null)

    } catch (error) {
      console.error('Error recording transaction:', error)
      toast({
        title: "Error",
        description: "Failed to record transaction. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <BackButton href="/feeding/inventory" />
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gradient-secondary">Record Feed Transaction</h1>
          <p className="text-muted-foreground">Track feed inventory movements (in/out)</p>
        </div>
      </div>

      {/* Transaction Form */}
      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LucideArrowDownRight className="h-5 w-5 text-green-600" />
            Feed Inventory Transaction
          </CardTitle>
          <CardDescription>Record feed items coming in or going out of inventory</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6">
            {/* Transaction Type */}
            <div className="space-y-3">
              <Label>Transaction Type <span className="text-red-500">*</span></Label>
              <RadioGroup
                value={formData.transactionType}
                onValueChange={(value) => handleSelectChange("transactionType", value)}
                className="flex gap-6"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="in" id="in" />
                  <Label htmlFor="in" className="flex items-center gap-2 cursor-pointer">
                    <LucideArrowDownRight className="h-4 w-4 text-green-600" />
                    Stock In (Restock)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="out" id="out" />
                  <Label htmlFor="out" className="flex items-center gap-2 cursor-pointer">
                    <LucideArrowUpRight className="h-4 w-4 text-red-600" />
                    Stock Out (Usage)
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Feed Item Selection */}
            <div className="space-y-2">
              <Label htmlFor="item">
                Feed Item <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.item}
                onValueChange={(value) => handleSelectChange("item", value)}
                required
              >
                <SelectTrigger id="item" className="input-primary">
                  <SelectValue placeholder="Select feed item" />
                </SelectTrigger>
                <SelectContent>
                  {feedItems.map((item: any) => (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name} - {item.category} ({item.animal_type})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {selectedItem && (
                <p className="text-sm text-muted-foreground">
                  Category: {selectedItem.category} | Target: {selectedItem.animal_type} | Unit: {selectedItem.unit}
                </p>
              )}
            </div>

            {/* Quantity */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="quantity">
                  Quantity <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="quantity"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  value={formData.quantity}
                  onChange={(e) => handleInputChange("quantity", e.target.value)}
                  className="input-primary"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="unit">Unit</Label>
                <Input
                  id="unit"
                  value={formData.unit}
                  placeholder="Unit"
                  className="input-primary bg-muted"
                  readOnly
                />
              </div>
            </div>

            {/* Date */}
            <div className="space-y-2">
              <Label htmlFor="date">
                Transaction Date <span className="text-red-500">*</span>
              </Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange("date", e.target.value)}
                className="input-primary"
                required
              />
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                placeholder="Additional notes about this transaction..."
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
                className="input-primary min-h-[100px]"
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" asChild>
              <Link href="/feeding/inventory">Cancel</Link>
            </Button>
            <LoadingButton
              type="submit"
              isLoading={isSubmitting}
              loadingText="Recording..."
              disabled={!formData.item || !formData.quantity}
              className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
            >
              <LucideSave className="mr-2 h-4 w-4" />
              Record Transaction
            </LoadingButton>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}
