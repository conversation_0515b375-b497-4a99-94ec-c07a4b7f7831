// Simple test script to verify login functionality
const fetch = require('node-fetch');

async function testLogin() {
  try {
    console.log('Testing login functionality...');
    
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      }),
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers.raw());
    
    const data = await response.json();
    console.log('Response data:', data);

    if (response.ok) {
      console.log('✅ Login successful!');
      console.log('User data:', data.user);
      
      // Check if auth cookie was set
      const cookies = response.headers.get('set-cookie');
      if (cookies && cookies.includes('auth-token')) {
        console.log('✅ Auth token cookie set successfully');
      } else {
        console.log('❌ Auth token cookie not found');
      }
    } else {
      console.log('❌ Login failed:', data.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testLogin();
