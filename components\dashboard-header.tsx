"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>rig<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>et<PERSON>ead<PERSON> } from "@/components/ui/sheet"
import {
  LucideBarChart2,
  LucideCalendarClock,
  LucideClipboardList,
  LucideCoins,
  LucideHeart,
  LucideHome,
  LucideLogOut,
  LucideMenu,
  Plus,
  LucideSettings,
  LucideShoppingCart,
  LucideUsers,
  LucideBell,
  LucideX,
} from "lucide-react"
import { NavLink } from "@/components/ui/nav-link"
import { LoadingButton } from "@/components/ui/loading-button"
import SearchBar from "@/components/search-bar"

export default function DashboardHeader() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const [isLogoutLoading, setIsLogoutLoading] = useState(false)
  const pathname = usePathname()

  // Handle scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const handleLogout = () => {
    setIsLogoutLoading(true)
    // Simulate loading for demo purposes
    setTimeout(() => setIsLogoutLoading(false), 1000)
  }

  const menuItems = [
    { href: "/dashboard", icon: LucideHome, label: "Overview", iconColor: "text-green-600" },
    { href: "/register", icon: Plus, label: "Register", iconColor: "text-blue-600" },
    { href: "/goats", icon: LucideUsers, label: "Goats", iconColor: "text-emerald-600" },
    { href: "/sheep", icon: LucideUsers, label: "Sheep", iconColor: "text-blue-600" },
    { href: "/cattle", icon: LucideUsers, label: "Cattle", iconColor: "text-amber-600" },
    { href: "/pigs", icon: LucideUsers, label: "Pigs", iconColor: "text-pink-600" },
    { href: "/health", icon: LucideHeart, label: "Health Records", iconColor: "text-green-600" },
    { href: "/breeding", icon: LucideCalendarClock, label: "Breeding", iconColor: "text-teal-600" },
    { href: "/feeding", icon: LucideClipboardList, label: "Feeding", iconColor: "text-emerald-600" },
    { href: "/finance", icon: LucideCoins, label: "Finance", iconColor: "text-green-600" },
    { href: "/inventory", icon: LucideShoppingCart, label: "Inventory", iconColor: "text-teal-600" },
    { href: "/reports", icon: LucideBarChart2, label: "Reports", iconColor: "text-emerald-600" },
  ]

  return (
    <header
      className={`sticky top-0 z-50 w-full border-b bg-white/95 dark:bg-gray-900/95 backdrop-blur transition-shadow duration-200 ${
        scrolled ? "shadow-md" : ""
      }`}
    >
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-2">
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden hover:bg-emerald-50 hover:text-emerald-600">
                <LucideMenu className="h-6 w-6" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-[280px] sm:w-[350px] p-0">
              <SheetHeader className="sr-only">
                <SheetTitle>Navigation Menu</SheetTitle>
              </SheetHeader>
              <div className="flex h-16 items-center border-b px-4">
                <div className="flex items-center gap-2 font-bold text-xl">
                  <LucideHeart className="h-6 w-6 text-primary" />
                  <span className="bg-gradient-to-r from-green-600 to-emerald-500 bg-clip-text text-transparent">
                    LivestockKeeper
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="ml-auto hover:bg-red-50 hover:text-red-600"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <LucideX className="h-5 w-5" />
                </Button>
              </div>
              <div className="flex flex-col gap-1 p-4 pt-6">
                {menuItems.map((item) => (
                  <NavLink
                    key={item.href}
                    href={item.href}
                    className="flex items-center h-12 px-4 rounded-md text-base font-medium transition-colors hover:bg-emerald-50 hover:text-emerald-700"
                    activeClassName="bg-gradient-to-r from-emerald-50 to-emerald-100 text-emerald-700 font-medium border-l-2 border-l-emerald-500"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <item.icon className={`mr-3 h-5 w-5 ${item.iconColor}`} />
                    {item.label}
                  </NavLink>
                ))}
                <div className="mt-6 border-t pt-6">
                  <NavLink
                    href="/settings"
                    className="flex items-center h-12 px-4 rounded-md text-base font-medium transition-colors hover:bg-green-50 hover:text-green-700"
                    activeClassName="bg-green-100 text-green-700 font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <LucideSettings className="mr-3 h-5 w-5" />
                    Settings
                  </NavLink>
                  <Link href="/logout" className="w-full block" onClick={handleLogout}>
                    <LoadingButton
                      variant="ghost"
                      className="flex items-center h-12 px-4 rounded-md text-base font-medium w-full justify-start transition-colors hover:bg-red-50 hover:text-red-700 mt-1"
                      isLoading={isLogoutLoading}
                    >
                      <LucideLogOut className="mr-3 h-5 w-5" />
                      Logout
                    </LoadingButton>
                  </Link>
                </div>
              </div>
            </SheetContent>
          </Sheet>
          <Link href="/dashboard" className="flex items-center gap-2 font-bold text-xl">
            <LucideHeart className="h-6 w-6 text-primary" />
            <span className="hidden sm:inline bg-gradient-to-r from-green-600 to-emerald-500 bg-clip-text text-transparent">
              Livestock Management
            </span>
          </Link>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative hidden md:block">
            <SearchBar />
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="relative hover:bg-amber-50 hover:text-amber-600 transition-colors"
          >
            <LucideBell className="h-5 w-5" />
            <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-amber-500 animate-pulse"></span>
            <span className="sr-only">Notifications</span>
          </Button>
          <Avatar className="h-9 w-9 transition-transform hover:scale-110 border-2 border-green-200 hover:border-green-300">
            <AvatarImage src="/placeholder.svg?height=36&width=36" alt="User" />
            <AvatarFallback className="bg-gradient-to-br from-green-100 to-emerald-200 text-green-700">
              JD
            </AvatarFallback>
          </Avatar>
        </div>
      </div>
    </header>
  )
}

