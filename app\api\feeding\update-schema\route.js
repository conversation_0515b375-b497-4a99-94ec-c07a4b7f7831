import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

export async function GET() {
  try {
    // Get a connection from the pool
    const connection = await pool.getConnection();
    
    try {
      // Check if feeding_record_goat_groups table exists
      const [tables] = await connection.execute(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'feeding_record_goat_groups'
      `);
      
      let results = {
        message: 'Schema check completed',
        actions: []
      };
      
      // If the table exists, check the column type
      if (tables.length > 0) {
        const [columns] = await connection.execute(`
          SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
          FROM INFORMATION_SCHEMA.COLUMNS
          WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME = 'feeding_record_goat_groups'
          AND COLUMN_NAME = 'feeding_record_id'
        `);
        
        if (columns.length > 0) {
          const column = columns[0];
          results.feeding_record_id_type = column.DATA_TYPE;
          
          // If the column is an integer, alter it to varchar
          if (column.DATA_TYPE === 'int' || column.DATA_TYPE === 'bigint') {
            await connection.execute(`
              ALTER TABLE feeding_record_goat_groups
              MODIFY COLUMN feeding_record_id VARCHAR(50) NOT NULL
            `);
            results.actions.push('Modified feeding_record_id column to VARCHAR(50)');
          }
        }
      } else {
        // Create the table with varchar for feeding_record_id
        await connection.execute(`
          CREATE TABLE IF NOT EXISTS feeding_record_goat_groups (
            id INT AUTO_INCREMENT PRIMARY KEY,
            feeding_record_id VARCHAR(50) NOT NULL,
            goat_group_id VARCHAR(50) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `);
        results.actions.push('Created feeding_record_goat_groups table with VARCHAR(50) for feeding_record_id');
      }
      
      // Check if feeding_record_feed_items table exists
      const [feedItemsTables] = await connection.execute(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'feeding_record_feed_items'
      `);
      
      // If the table exists, check the column type
      if (feedItemsTables.length > 0) {
        const [columns] = await connection.execute(`
          SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH
          FROM INFORMATION_SCHEMA.COLUMNS
          WHERE TABLE_SCHEMA = DATABASE()
          AND TABLE_NAME = 'feeding_record_feed_items'
          AND COLUMN_NAME = 'feeding_record_id'
        `);
        
        if (columns.length > 0) {
          const column = columns[0];
          results.feed_items_record_id_type = column.DATA_TYPE;
          
          // If the column is an integer, alter it to varchar
          if (column.DATA_TYPE === 'int' || column.DATA_TYPE === 'bigint') {
            await connection.execute(`
              ALTER TABLE feeding_record_feed_items
              MODIFY COLUMN feeding_record_id VARCHAR(50) NOT NULL
            `);
            results.actions.push('Modified feeding_record_id column in feeding_record_feed_items to VARCHAR(50)');
          }
        }
      } else {
        // Create the table with varchar for feeding_record_id
        await connection.execute(`
          CREATE TABLE IF NOT EXISTS feeding_record_feed_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            feeding_record_id VARCHAR(50) NOT NULL,
            feed_item_id VARCHAR(50) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            unit VARCHAR(20) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          )
        `);
        results.actions.push('Created feeding_record_feed_items table with VARCHAR(50) for feeding_record_id');
      }
      
      return NextResponse.json(results);
    } finally {
      // Release the connection
      connection.release();
    }
  } catch (error) {
    console.error('Error updating schema:', error);
    return NextResponse.json(
      { error: 'Failed to update schema: ' + error.message },
      { status: 500 }
    );
  }
}
