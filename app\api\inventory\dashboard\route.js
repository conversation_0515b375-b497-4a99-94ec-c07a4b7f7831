import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0
});

export async function GET() {
  let connection;
  
  try {
    // Get a connection from the pool
    connection = await pool.getConnection();
    
    try {
      // Get inventory items
      const [inventoryItems] = await connection.execute(`
        SELECT 
          id,
          name,
          category,
          quantity,
          unit,
          min_level,
          max_level,
          location,
          expiry_date,
          price_per_unit,
          supplier,
          notes,
          created_at,
          updated_at
        FROM 
          inventory_items
        ORDER BY 
          created_at DESC
      `);
      
      // Get recent transactions
      const [recentTransactions] = await connection.execute(`
        SELECT 
          id,
          transaction_type,
          item,
          quantity,
          unit,
          transaction_date,
          notes,
          created_at
        FROM 
          inventory_transactions
        ORDER BY 
          transaction_date DESC, created_at DESC
        LIMIT 10
      `);
      
      // Calculate low stock items (where quantity <= min_level)
      const lowStockItems = inventoryItems.filter(item => 
        item.min_level !== null && item.quantity <= item.min_level
      );
      
      // Calculate expiring soon items (within 30 days)
      const today = new Date();
      const thirtyDaysFromNow = new Date(today);
      thirtyDaysFromNow.setDate(today.getDate() + 30);
      
      const expiringSoonItems = inventoryItems.filter(item => {
        if (!item.expiry_date) return false;
        const expiryDate = new Date(item.expiry_date);
        return expiryDate <= thirtyDaysFromNow && expiryDate >= today;
      });
      
      // Calculate total inventory value
      const totalInventoryValue = inventoryItems.reduce((total, item) => {
        const price = item.price_per_unit || 0;
        return total + (item.quantity * price);
      }, 0);
      
      // Get category counts
      const categories = {};
      inventoryItems.forEach(item => {
        if (!categories[item.category]) {
          categories[item.category] = 0;
        }
        categories[item.category]++;
      });
      
      // Get transaction counts by type
      const [transactionCounts] = await connection.execute(`
        SELECT 
          transaction_type,
          COUNT(*) as count
        FROM 
          inventory_transactions
        GROUP BY 
          transaction_type
      `);
      
      // Get transaction totals by type for the last 30 days
      const [recentTransactionTotals] = await connection.execute(`
        SELECT 
          transaction_type,
          SUM(quantity) as total_quantity
        FROM 
          inventory_transactions
        WHERE 
          transaction_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY 
          transaction_type
      `);
      
      // Return the dashboard data
      return NextResponse.json({
        inventoryItems,
        recentTransactions,
        stats: {
          totalItems: inventoryItems.length,
          totalValue: totalInventoryValue,
          lowStockCount: lowStockItems.length,
          expiringSoonCount: expiringSoonItems.length,
          categories,
          transactionCounts: transactionCounts.reduce((obj, item) => {
            obj[item.transaction_type] = item.count;
            return obj;
          }, {}),
          recentTransactionTotals: recentTransactionTotals.reduce((obj, item) => {
            obj[item.transaction_type] = item.total_quantity;
            return obj;
          }, {})
        },
        lowStockItems,
        expiringSoonItems
      });
      
    } catch (queryError) {
      console.error('Error executing database query:', queryError);
      return NextResponse.json(
        { error: 'Database query error: ' + queryError.message },
        { status: 500 }
      );
    } finally {
      // Release the connection
      if (connection) connection.release();
    }
  } catch (error) {
    console.error('Error in inventory dashboard API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch inventory dashboard data: ' + error.message },
      { status: 500 }
    );
  }
}
