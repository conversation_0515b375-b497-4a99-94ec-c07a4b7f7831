-- Multi-Animal Management System Database Schema
-- Supports: <PERSON><PERSON>, Sheep, Cattle, and Pigs

-- Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS livestock_management;

-- Use the database
USE livestock_management;

-- Animals table (replaces the goats table with multi-animal support)
CREATE TABLE IF NOT EXISTS animals (
  id INT AUTO_INCREMENT PRIMARY KEY,
  tag_number VARCHAR(50) NOT NULL UNIQUE,
  name VARCHAR(100) NOT NULL,
  animal_type ENUM('Goat', 'Sheep', 'Cattle', 'Pig') NOT NULL,
  breed VARCHAR(100) NOT NULL,
  gender ENUM('Male', 'Female') NOT NULL,
  birth_date DATE,
  acquisition_date DATE,
  acquisition_price DECIMAL(10, 2),
  status ENUM('Healthy', 'Sick', 'Pregnant', 'Lactating', 'Deceased', 'Injured', 'Quarantined') NOT NULL DEFAULT 'Healthy',
  weight DECIMAL(8, 2),
  color VARCHAR(100),
  markings TEXT,
  sire VARCHAR(50), -- Reference to father's tag_number
  dam VARCHAR(50),  -- Reference to mother's tag_number
  purchase_price DECIMAL(10, 2),
  is_registered BOOLEAN DEFAULT FALSE,
  registration_number VARCHAR(100),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_animal_type (animal_type),
  INDEX idx_tag_number (tag_number),
  INDEX idx_status (status),
  INDEX idx_gender (gender)
);

-- Health records table (updated to support all animal types)
CREATE TABLE IF NOT EXISTS health_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  animal_id INT NOT NULL,
  record_date DATE NOT NULL,
  record_type ENUM('Vaccination', 'Medication', 'Illness', 'Injury', 'Checkup', 'Deworming', 'Hoof_Trimming', 'Other') NOT NULL,
  diagnosis TEXT,
  treatment TEXT,
  medication VARCHAR(200),
  dosage VARCHAR(100),
  administered_by VARCHAR(100),
  vet_name VARCHAR(100),
  follow_up_date DATE,
  outcome ENUM('ongoing', 'resolved', 'chronic', 'deceased') DEFAULT 'ongoing',
  notes TEXT,
  is_critical BOOLEAN DEFAULT FALSE,
  cost DECIMAL(10, 2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (animal_id) REFERENCES animals(id) ON DELETE CASCADE,
  INDEX idx_animal_id (animal_id),
  INDEX idx_record_date (record_date),
  INDEX idx_record_type (record_type)
);

-- Breeding records table (updated to support all animal types)
CREATE TABLE IF NOT EXISTS breeding_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  female_id INT NOT NULL, -- Changed from doe_id to be more generic
  male_id INT, -- Changed from buck_id to be more generic
  breeding_method ENUM('Natural', 'Artificial_Insemination', 'Embryo_Transfer') DEFAULT 'Natural',
  breeding_date DATE NOT NULL,
  expected_birth_date DATE,
  actual_birth_date DATE,
  number_of_offspring INT,
  status ENUM('Pending', 'Confirmed', 'Born', 'Failed') DEFAULT 'Pending',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (female_id) REFERENCES animals(id) ON DELETE CASCADE,
  FOREIGN KEY (male_id) REFERENCES animals(id) ON DELETE SET NULL,
  INDEX idx_female_id (female_id),
  INDEX idx_male_id (male_id),
  INDEX idx_breeding_date (breeding_date),
  INDEX idx_status (status)
);

-- Heat cycles table (updated to support all animal types)
CREATE TABLE IF NOT EXISTS heat_cycles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  animal_id INT NOT NULL,
  heat_date DATE NOT NULL,
  intensity ENUM('Low', 'Medium', 'High') DEFAULT 'Medium',
  signs TEXT, -- Comma-separated list of signs
  notes TEXT,
  breeding_scheduled BOOLEAN DEFAULT FALSE,
  planned_breeding_date DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (animal_id) REFERENCES animals(id) ON DELETE CASCADE,
  INDEX idx_animal_id (animal_id),
  INDEX idx_heat_date (heat_date)
);

-- Financial transactions table (updated to support all animal types)
CREATE TABLE IF NOT EXISTS financial_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  transaction_date DATE NOT NULL,
  transaction_type ENUM('Income', 'Expense') NOT NULL,
  category VARCHAR(100) NOT NULL,
  amount DECIMAL(12, 2) NOT NULL,
  description TEXT,
  animal_type ENUM('Goat', 'Sheep', 'Cattle', 'Pig', 'General') DEFAULT 'General',
  related_animal_id INT,
  payment_method VARCHAR(50),
  reference_number VARCHAR(100),
  receipt_path VARCHAR(500),
  is_recurring BOOLEAN DEFAULT FALSE,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (related_animal_id) REFERENCES animals(id) ON DELETE SET NULL,
  INDEX idx_transaction_date (transaction_date),
  INDEX idx_transaction_type (transaction_type),
  INDEX idx_animal_type (animal_type),
  INDEX idx_category (category)
);

-- Feeding records table (updated to support all animal types)
CREATE TABLE IF NOT EXISTS feeding_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  feeding_date DATE NOT NULL,
  feeding_time TIME NOT NULL,
  animal_type ENUM('Goat', 'Sheep', 'Cattle', 'Pig', 'All') NOT NULL,
  animal_group VARCHAR(100), -- e.g., "Adult Males", "Pregnant Females", etc.
  feed_type VARCHAR(100) NOT NULL,
  quantity DECIMAL(10, 3) NOT NULL,
  unit VARCHAR(20) NOT NULL,
  consumption_level ENUM('None', 'Low', 'Medium', 'High', 'Complete') DEFAULT 'Complete',
  recorded_by VARCHAR(100),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_feeding_date (feeding_date),
  INDEX idx_animal_type (animal_type)
);

-- Feed items table (already supports all animal types)
CREATE TABLE IF NOT EXISTS feed_items (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  category VARCHAR(50) NOT NULL,
  description TEXT,
  quantity DECIMAL(10, 3) NOT NULL DEFAULT 0.000,
  unit VARCHAR(20) NOT NULL,
  min_level DECIMAL(10, 3),
  max_level DECIMAL(10, 3),
  location VARCHAR(100),
  expiry_date DATE,
  purchase_date DATE,
  price_per_unit DECIMAL(10, 2),
  supplier VARCHAR(100),
  protein DECIMAL(5, 2),
  fiber DECIMAL(5, 2),
  fat DECIMAL(5, 2),
  moisture DECIMAL(5, 2),
  suitable_for SET('Goat', 'Sheep', 'Cattle', 'Pig') DEFAULT 'Goat,Sheep,Cattle,Pig',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category),
  INDEX idx_name (name)
);

-- Inventory items table (already supports all animal types)
CREATE TABLE IF NOT EXISTS inventory_items (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  category VARCHAR(50) NOT NULL,
  quantity DECIMAL(10, 3) NOT NULL DEFAULT 0.000,
  unit VARCHAR(20),
  min_level DECIMAL(10, 3),
  max_level DECIMAL(10, 3),
  location VARCHAR(100),
  expiry_date DATE,
  price_per_unit DECIMAL(10, 2),
  supplier VARCHAR(100),
  applicable_to SET('Goat', 'Sheep', 'Cattle', 'Pig', 'General') DEFAULT 'General',
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY name (name),
  INDEX idx_category (category)
);

-- Inventory transactions table
CREATE TABLE IF NOT EXISTS inventory_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  item_id INT NOT NULL,
  transaction_type ENUM('In', 'Out', 'Adjustment') NOT NULL,
  quantity DECIMAL(10, 3) NOT NULL,
  transaction_date DATE NOT NULL,
  reference VARCHAR(100),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (item_id) REFERENCES inventory_items(id) ON DELETE CASCADE,
  INDEX idx_item_id (item_id),
  INDEX idx_transaction_date (transaction_date)
);

-- Users table (unchanged)
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  email VARCHAR(100),
  full_name VARCHAR(100),
  role ENUM('Admin', 'Manager', 'Staff', 'Viewer') NOT NULL DEFAULT 'Viewer',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_login TIMESTAMP NULL,
  is_active BOOLEAN DEFAULT TRUE,
  INDEX idx_username (username),
  INDEX idx_role (role)
);

-- Migration script to move existing goats data to animals table
-- This will be executed only if the goats table exists
DELIMITER $$
CREATE PROCEDURE MigrateGoatsToAnimals()
BEGIN
  DECLARE table_exists INT DEFAULT 0;

  -- Check if goats table exists
  SELECT COUNT(*) INTO table_exists
  FROM information_schema.tables
  WHERE table_schema = DATABASE() AND table_name = 'goats';

  IF table_exists > 0 THEN
    -- Insert existing goats data into animals table
    INSERT INTO animals (
      tag_number, name, animal_type, breed, gender, birth_date,
      acquisition_date, acquisition_price, status, weight, color,
      markings, sire, dam, purchase_price, is_registered,
      registration_number, notes, created_at, updated_at
    )
    SELECT
      COALESCE(tag_number, CONCAT('G-', id)) as tag_number,
      name,
      'Goat' as animal_type,
      breed,
      gender,
      birth_date,
      acquisition_date,
      acquisition_price,
      status,
      weight,
      color,
      markings,
      sire,
      dam,
      purchase_price,
      COALESCE(is_registered, 0),
      registration_number,
      notes,
      COALESCE(created_at, NOW()),
      COALESCE(updated_at, NOW())
    FROM goats
    WHERE NOT EXISTS (
      SELECT 1 FROM animals
      WHERE animals.tag_number = COALESCE(goats.tag_number, CONCAT('G-', goats.id))
    );

    -- Update health_records to reference animals table
    UPDATE health_records hr
    JOIN goats g ON hr.goat_id = g.id
    JOIN animals a ON a.tag_number = COALESCE(g.tag_number, CONCAT('G-', g.id))
    SET hr.animal_id = a.id
    WHERE hr.animal_id IS NULL OR hr.animal_id = 0;

    -- Update breeding_records to reference animals table
    UPDATE breeding_records br
    JOIN goats g1 ON br.doe_id = g1.id
    JOIN animals a1 ON a1.tag_number = COALESCE(g1.tag_number, CONCAT('G-', g1.id))
    SET br.female_id = a1.id;

    UPDATE breeding_records br
    JOIN goats g2 ON br.buck_id = g2.id
    JOIN animals a2 ON a2.tag_number = COALESCE(g2.tag_number, CONCAT('G-', g2.id))
    SET br.male_id = a2.id
    WHERE br.buck_id IS NOT NULL;

    -- Update heat_cycles to reference animals table
    UPDATE heat_cycles hc
    JOIN goats g ON hc.goat_id = g.id
    JOIN animals a ON a.tag_number = COALESCE(g.tag_number, CONCAT('G-', g.id))
    SET hc.animal_id = a.id;

    -- Update financial_transactions to reference animals table
    UPDATE financial_transactions ft
    JOIN goats g ON ft.related_goat_id = g.id
    JOIN animals a ON a.tag_number = COALESCE(g.tag_number, CONCAT('G-', g.id))
    SET ft.related_animal_id = a.id, ft.animal_type = 'Goat'
    WHERE ft.related_goat_id IS NOT NULL;

    SELECT 'Migration completed successfully' as message;
  ELSE
    SELECT 'No goats table found, skipping migration' as message;
  END IF;
END$$
DELIMITER ;

-- Sample data for different animal types
INSERT INTO animals (tag_number, name, animal_type, breed, gender, birth_date, acquisition_date, status, weight, color, notes) VALUES
-- Goats
('G-001', 'Bella', 'Goat', 'Boer', 'Female', '2021-05-15', '2021-08-20', 'Healthy', 45.5, 'White and Brown', 'Good milk producer'),
('G-002', 'Max', 'Goat', 'East African (Local)', 'Male', '2020-03-10', '2020-06-15', 'Healthy', 35.2, 'Brown', 'Strong breeding male'),
('G-003', 'Luna', 'Goat', 'Malawian Local', 'Female', '2022-02-20', '2022-05-10', 'Pregnant', 30.8, 'Black and White', 'First pregnancy'),

-- Sheep
('S-001', 'Woolly', 'Sheep', 'Dorper', 'Female', '2021-04-12', '2021-07-15', 'Healthy', 55.0, 'White with Black Head', 'Excellent wool quality'),
('S-002', 'Ram', 'Sheep', 'Merino', 'Male', '2020-01-08', '2020-04-20', 'Healthy', 75.3, 'White', 'Prime breeding ram'),
('S-003', 'Daisy', 'Sheep', 'Local Breed', 'Female', '2022-03-15', '2022-06-10', 'Lactating', 48.2, 'Brown', 'Twin lambs'),

-- Cattle
('C-001', 'Bessie', 'Cattle', 'Holstein', 'Female', '2019-08-22', '2020-01-15', 'Healthy', 450.0, 'Black and White', 'High milk yield'),
('C-002', 'Bull', 'Cattle', 'Angus', 'Male', '2018-05-10', '2018-10-05', 'Healthy', 650.5, 'Black', 'Breeding bull'),
('C-003', 'Rosie', 'Cattle', 'Zebu Cross', 'Female', '2020-12-03', '2021-03-20', 'Pregnant', 380.2, 'Brown', 'Local adapted breed'),

-- Pigs
('P-001', 'Porky', 'Pig', 'Large White', 'Male', '2022-01-15', '2022-04-10', 'Healthy', 180.0, 'Pink', 'Breeding boar'),
('P-002', 'Piglet', 'Pig', 'Landrace', 'Female', '2022-03-20', '2022-06-15', 'Lactating', 150.5, 'White', 'Good mother'),
('P-003', 'Bacon', 'Pig', 'Duroc', 'Male', '2022-05-08', '2022-08-12', 'Healthy', 95.2, 'Red', 'Fast growing')
ON DUPLICATE KEY UPDATE name = VALUES(name);
