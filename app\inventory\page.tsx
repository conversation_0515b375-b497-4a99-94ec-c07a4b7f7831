"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"
import {
  LucideShoppingCart,
  LucidePlus,
  LucideSearch,
  LucideFilter,
  LucideRefreshCw,
  LucideX,
  LucideAlertTriangle,
  LucidePackage,
  LucideWheat,
  LucidePill,
  LucideWrench,
  LucideArrowUpRight,
  LucideArrowDownRight,
  LucideCalendarClock,
  LucideBarChart2,
} from "lucide-react"
import DashboardLayout from "@/components/dashboard-layout"

// Sample inventory data for demonstration
const inventoryItems = [
  {
    id: "INV-001",
    name: "Alfalfa Hay",
    category: "Feed",
    quantity: 750,
    unit: "kg",
    minLevel: 200,
    maxLevel: 1000,
    location: "Main Barn",
    expiryDate: "2024-09-15",
    lastRestocked: "2024-05-10",
    price: 0.35,
    supplier: "Green Valley Farms",
  },
  {
    id: "INV-002",
    name: "Grain Mix",
    category: "Feed",
    quantity: 150,
    unit: "kg",
    minLevel: 50,
    maxLevel: 300,
    location: "Feed Storage",
    expiryDate: "2024-08-20",
    lastRestocked: "2024-05-05",
    price: 0.75,
    supplier: "Nutrition Plus",
  },
  {
    id: "INV-003",
    name: "Mineral Blocks",
    category: "Feed",
    quantity: 15,
    unit: "blocks",
    minLevel: 5,
    maxLevel: 30,
    location: "Feed Storage",
    expiryDate: "2025-01-10",
    lastRestocked: "2024-04-15",
    price: 8.5,
    supplier: "Goat Nutrition Co.",
  },
  {
    id: "INV-004",
    name: "Dewormer",
    category: "Medication",
    quantity: 5,
    unit: "bottles",
    minLevel: 2,
    maxLevel: 10,
    location: "Medicine Cabinet",
    expiryDate: "2025-03-20",
    lastRestocked: "2024-03-10",
    price: 45.0,
    supplier: "Animal Health Inc.",
  },
  {
    id: "INV-005",
    name: "Antibiotics",
    category: "Medication",
    quantity: 3,
    unit: "boxes",
    minLevel: 1,
    maxLevel: 5,
    location: "Medicine Cabinet",
    expiryDate: "2024-07-15",
    lastRestocked: "2024-02-20",
    price: 65.0,
    supplier: "VetSupplies",
  },
  {
    id: "INV-006",
    name: "Hoof Trimmers",
    category: "Equipment",
    quantity: 4,
    unit: "pieces",
    minLevel: 2,
    maxLevel: 6,
    location: "Tool Shed",
    expiryDate: null,
    lastRestocked: "2023-11-15",
    price: 28.5,
    supplier: "Farm Tools Co.",
  },
  {
    id: "INV-007",
    name: "Milking Buckets",
    category: "Equipment",
    quantity: 8,
    unit: "pieces",
    minLevel: 4,
    maxLevel: 12,
    location: "Milking Area",
    expiryDate: null,
    lastRestocked: "2023-10-05",
    price: 15.75,
    supplier: "Dairy Supplies Ltd.",
  },
  {
    id: "INV-008",
    name: "Ear Tags",
    category: "Supplies",
    quantity: 120,
    unit: "pieces",
    minLevel: 50,
    maxLevel: 200,
    location: "Office Cabinet",
    expiryDate: null,
    lastRestocked: "2024-01-20",
    price: 0.85,
    supplier: "Farm ID Systems",
  },
  {
    id: "INV-009",
    name: "Bedding Straw",
    category: "Supplies",
    quantity: 300,
    unit: "kg",
    minLevel: 100,
    maxLevel: 500,
    location: "Storage Barn",
    expiryDate: null,
    lastRestocked: "2024-04-25",
    price: 0.25,
    supplier: "Green Valley Farms",
  },
  {
    id: "INV-010",
    name: "Vaccines",
    category: "Medication",
    quantity: 25,
    unit: "doses",
    minLevel: 10,
    maxLevel: 50,
    location: "Refrigerator",
    expiryDate: "2024-08-10",
    lastRestocked: "2024-03-15",
    price: 4.5,
    supplier: "Animal Health Inc.",
  },
]

// Recent transactions for demonstration
const recentTransactions = [
  {
    id: "TRX-001",
    date: "2024-06-01",
    type: "in",
    item: "Alfalfa Hay",
    quantity: 200,
    unit: "kg",
    user: "John Doe",
  },
  {
    id: "TRX-002",
    date: "2024-05-28",
    type: "out",
    item: "Grain Mix",
    quantity: 25,
    unit: "kg",
    user: "Jane Smith",
  },
  {
    id: "TRX-003",
    date: "2024-05-25",
    type: "in",
    item: "Vaccines",
    quantity: 15,
    unit: "doses",
    user: "John Doe",
  },
  {
    id: "TRX-004",
    date: "2024-05-22",
    type: "out",
    item: "Mineral Blocks",
    quantity: 2,
    unit: "blocks",
    user: "Jane Smith",
  },
  {
    id: "TRX-005",
    date: "2024-05-20",
    type: "out",
    item: "Dewormer",
    quantity: 1,
    unit: "bottle",
    user: "John Doe",
  },
]

export default function InventoryPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [stockLevelFilter, setStockLevelFilter] = useState("all")
  const [activeTab, setActiveTab] = useState("overview")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // State for data from API
  const [inventoryData, setInventoryData] = useState({
    inventoryItems: [],
    recentTransactions: [],
    stats: {
      totalItems: 0,
      totalValue: 0,
      lowStockCount: 0,
      expiringSoonCount: 0,
      categories: {},
      transactionCounts: {},
      recentTransactionTotals: {}
    },
    lowStockItems: [],
    expiringSoonItems: []
  })

  // Fetch data from API
  useEffect(() => {
    const fetchInventoryData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const response = await fetch('/api/inventory/dashboard')

        if (!response.ok) {
          throw new Error(`Failed to fetch inventory data: ${response.status}`)
        }

        const data = await response.json()
        console.log('Fetched inventory data:', data)
        setInventoryData(data)
      } catch (err: any) {
        console.error('Error fetching inventory data:', err)
        setError(err.message)
      } finally {
        setIsLoading(false)
      }
    }

    fetchInventoryData()
  }, [])

  // Destructure data for easier access
  const { inventoryItems, recentTransactions, stats, lowStockItems, expiringSoonItems } = inventoryData

  // Calculate total inventory value (fallback to stats if available)
  const totalInventoryValue = stats?.totalValue || 0

  // Filter inventory items based on search and filters
  const filteredItems = inventoryItems?.filter((item: any) => {
    const matchesSearch =
      searchTerm === "" ||
      (item.name && item.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (item.id && item.id.toString().toLowerCase().includes(searchTerm.toLowerCase())) ||
      (item.category && item.category.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesCategory = categoryFilter === "all" || item.category === categoryFilter

    const stockLevel =
      (item.min_level !== null && item.quantity <= item.min_level) ? "low" :
      (item.max_level !== null && item.quantity >= item.max_level) ? "high" :
      "normal"

    const matchesStockLevel = stockLevelFilter === "all" || stockLevel === stockLevelFilter

    return matchesSearch && matchesCategory && matchesStockLevel
  }) || []

  // Get category counts from stats or calculate if not available
  const categoryCounts = stats?.categories || {
    Feed: 0,
    Medication: 0,
    Equipment: 0,
    Supplies: 0
  }

  // Reset filters
  const resetFilters = () => {
    setSearchTerm("")
    setCategoryFilter("all")
    setStockLevelFilter("all")
  }

  // Get stock level badge
  const getStockLevelBadge = (item: any) => {
    // Handle case where min_level or max_level is null
    if (item.min_level === null || item.max_level === null) {
      return (
        <Badge variant="outline" className="badge-secondary">
          Unspecified
        </Badge>
      )
    }

    const stockPercentage = (item.quantity / item.max_level) * 100

    if (item.quantity <= item.min_level) {
      return (
        <Badge variant="outline" className="badge-sick">
          Low Stock
        </Badge>
      )
    } else if (stockPercentage >= 90) {
      return (
        <Badge variant="outline" className="badge-healthy">
          Well Stocked
        </Badge>
      )
    } else {
      return (
        <Badge variant="outline" className="badge-secondary">
          Adequate
        </Badge>
      )
    }
  }

  // Get category icon
  const getCategoryIcon = (category) => {
    switch (category) {
      case "Feed":
        return <LucideWheat className="h-4 w-4 text-amber-500" />
      case "Medication":
        return <LucidePill className="h-4 w-4 text-red-500" />
      case "Equipment":
        return <LucideWrench className="h-4 w-4 text-blue-500" />
      case "Supplies":
        return <LucidePackage className="h-4 w-4 text-purple-500" />
      default:
        return <LucidePackage className="h-4 w-4" />
    }
  }

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-accent">Inventory Management</h1>
          <div className="flex flex-wrap gap-2">
            <Link href="/inventory/transaction">
              <Button
                variant="outline"
                className="border-2 border-teal-500 text-teal-600 hover:bg-teal-50 hover:text-teal-700 transition-all duration-300"
              >
                <LucideShoppingCart className="mr-2 h-4 w-4" />
                Record Transaction
              </Button>
            </Link>
            <Link href="/inventory/add">
              <Button className="bg-gradient-to-r from-teal-500 to-cyan-500 hover:from-teal-600 hover:to-cyan-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
                <LucidePlus className="mr-2 h-4 w-4" />
                Add Inventory Item
              </Button>
            </Link>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="dashboard-card-primary">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Total Items</div>
                  <div className="text-2xl font-bold text-green-600">
                    {isLoading ? (
                      <div className="h-6 w-16 bg-gray-200 animate-pulse rounded"></div>
                    ) : (
                      stats?.totalItems || 0
                    )}
                  </div>
                </div>
                <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                  <LucidePackage className="h-5 w-5 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="dashboard-card-secondary">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Inventory Value</div>
                  <div className="text-2xl font-bold text-emerald-600">
                    {isLoading ? (
                      <div className="h-6 w-24 bg-gray-200 animate-pulse rounded"></div>
                    ) : (
                      `MWK ${totalInventoryValue.toFixed(2)}`
                    )}
                  </div>
                </div>
                <div className="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
                  <LucideBarChart2 className="h-5 w-5 text-emerald-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="dashboard-card-accent">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Low Stock Items</div>
                  <div className="text-2xl font-bold text-red-600">
                    {isLoading ? (
                      <div className="h-6 w-10 bg-gray-200 animate-pulse rounded"></div>
                    ) : (
                      stats?.lowStockCount || 0
                    )}
                  </div>
                </div>
                <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                  <LucideAlertTriangle className="h-5 w-5 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="dashboard-card-amber">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-muted-foreground">Expiring Soon</div>
                  <div className="text-2xl font-bold text-amber-600">
                    {isLoading ? (
                      <div className="h-6 w-10 bg-gray-200 animate-pulse rounded"></div>
                    ) : (
                      stats?.expiringSoonCount || 0
                    )}
                  </div>
                </div>
                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                  <LucideCalendarClock className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Error message */}
        {error && !isLoading && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
            <h3 className="font-medium flex items-center gap-2">
              <LucideAlertTriangle className="h-5 w-5" />
              Error Loading Inventory Data
            </h3>
            <p className="mt-1 text-sm">{error}</p>
            <Button
              variant="outline"
              className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          </div>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3 p-1 bg-gradient-to-r from-teal-50 via-cyan-50 to-sky-50 rounded-xl">
            <TabsTrigger
              value="overview"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-teal-500 data-[state=active]:to-cyan-500 data-[state=active]:text-white transition-all duration-300 hover:text-teal-700"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="items"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-cyan-500 data-[state=active]:to-sky-500 data-[state=active]:text-white transition-all duration-300 hover:text-cyan-700"
            >
              Inventory Items
            </TabsTrigger>
            <TabsTrigger
              value="transactions"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-sky-500 data-[state=active]:to-blue-500 data-[state=active]:text-white transition-all duration-300 hover:text-sky-700"
            >
              Recent Transactions
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            {/* Category Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Inventory by Category</CardTitle>
                <CardDescription>Overview of items by category</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {isLoading ? (
                    // Loading skeleton for categories
                    <>
                      {[1, 2, 3, 4].map((i) => (
                        <Card key={i} className="bg-gray-50 border-gray-200">
                          <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                              <div className="h-10 w-10 rounded-full bg-gray-100 animate-pulse"></div>
                              <div className="space-y-2">
                                <div className="h-4 w-20 bg-gray-200 animate-pulse rounded"></div>
                                <div className="h-6 w-10 bg-gray-200 animate-pulse rounded"></div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </>
                  ) : (
                    // Actual category cards
                    <>
                      <Card className="bg-gradient-to-br from-amber-50 to-yellow-100 border-amber-200">
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                              <LucideWheat className="h-5 w-5 text-amber-600" />
                            </div>
                            <div>
                              <div className="text-sm font-medium">Feed</div>
                              <div className="text-2xl font-bold text-amber-700">{categoryCounts.Feed || 0}</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                      <Card className="bg-gradient-to-br from-red-50 to-rose-100 border-red-200">
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                              <LucidePill className="h-5 w-5 text-red-600" />
                            </div>
                            <div>
                              <div className="text-sm font-medium">Medication</div>
                              <div className="text-2xl font-bold text-red-700">{categoryCounts.Medication || 0}</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                      <Card className="bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200">
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <LucideWrench className="h-5 w-5 text-blue-600" />
                            </div>
                            <div>
                              <div className="text-sm font-medium">Equipment</div>
                              <div className="text-2xl font-bold text-blue-700">{categoryCounts.Equipment || 0}</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                      <Card className="bg-gradient-to-br from-purple-50 to-violet-100 border-purple-200">
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                              <LucidePackage className="h-5 w-5 text-purple-600" />
                            </div>
                            <div>
                              <div className="text-sm font-medium">Supplies</div>
                              <div className="text-2xl font-bold text-purple-700">{categoryCounts.Supplies || 0}</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Alerts */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Low Stock Alerts */}
              <Card className="border-l-4 border-l-red-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Low Stock Alerts</CardTitle>
                    <LucideAlertTriangle className="h-5 w-5 text-red-500" />
                  </div>
                  <CardDescription>Items that need to be restocked soon</CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="space-y-3">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                          <div className="flex items-center gap-2">
                            <div className="h-4 w-4 bg-gray-200 animate-pulse rounded-full"></div>
                            <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="h-4 w-16 bg-gray-200 animate-pulse rounded"></div>
                            <div className="h-8 w-8 bg-gray-200 animate-pulse rounded-full"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : lowStockItems && lowStockItems.length > 0 ? (
                    <div className="space-y-3">
                      {lowStockItems.slice(0, 3).map((item: any) => (
                        <div key={item.id} className="flex items-center justify-between p-2 bg-red-50 rounded-md">
                          <div className="flex items-center gap-2">
                            {getCategoryIcon(item.category)}
                            <span className="font-medium">{item.name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-red-600 font-medium">
                              {item.quantity} / {item.min_level} {item.unit}
                            </span>
                            <Link href={`/inventory/${item.id}`}>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-red-600">
                                <LucideArrowUpRight className="h-4 w-4" />
                              </Button>
                            </Link>
                          </div>
                        </div>
                      ))}
                      {lowStockItems.length > 3 && (
                        <Link
                          href="/inventory/alerts"
                          className="block text-center text-sm text-red-600 hover:underline mt-2"
                        >
                          View all {lowStockItems.length} low stock items
                        </Link>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">
                      No low stock items. Everything is well stocked!
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Expiring Soon Alerts */}
              <Card className="border-l-4 border-l-amber-500">
                <CardHeader className="pb-2">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Expiring Soon</CardTitle>
                    <LucideCalendarClock className="h-5 w-5 text-amber-500" />
                  </div>
                  <CardDescription>Items expiring within 30 days</CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="space-y-3">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                          <div className="flex items-center gap-2">
                            <div className="h-4 w-4 bg-gray-200 animate-pulse rounded-full"></div>
                            <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="h-4 w-24 bg-gray-200 animate-pulse rounded"></div>
                            <div className="h-8 w-8 bg-gray-200 animate-pulse rounded-full"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : expiringSoonItems && expiringSoonItems.length > 0 ? (
                    <div className="space-y-3">
                      {expiringSoonItems.slice(0, 3).map((item: any) => (
                        <div key={item.id} className="flex items-center justify-between p-2 bg-amber-50 rounded-md">
                          <div className="flex items-center gap-2">
                            {getCategoryIcon(item.category)}
                            <span className="font-medium">{item.name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-amber-600 font-medium">
                              Expires: {new Date(item.expiry_date).toLocaleDateString()}
                            </span>
                            <Link href={`/inventory/${item.id}`}>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-amber-600">
                                <LucideArrowUpRight className="h-4 w-4" />
                              </Button>
                            </Link>
                          </div>
                        </div>
                      ))}
                      {expiringSoonItems.length > 3 && (
                        <Link
                          href="/inventory/alerts"
                          className="block text-center text-sm text-amber-600 hover:underline mt-2"
                        >
                          View all {expiringSoonItems.length} expiring items
                        </Link>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">No items expiring soon.</div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Recent Transactions */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>Latest inventory movements</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-3">
                    {[1, 2, 3, 4, 5].map((i) => (
                      <div key={i} className="flex items-center justify-between p-2 bg-gray-50 rounded-md border border-gray-100">
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 rounded-full bg-gray-200 animate-pulse"></div>
                          <div className="space-y-2">
                            <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
                            <div className="h-3 w-24 bg-gray-200 animate-pulse rounded"></div>
                          </div>
                        </div>
                        <div className="h-4 w-16 bg-gray-200 animate-pulse rounded"></div>
                      </div>
                    ))}
                  </div>
                ) : recentTransactions && recentTransactions.length > 0 ? (
                  <div className="space-y-3">
                    {recentTransactions.slice(0, 5).map((transaction: any) => (
                      <div
                        key={transaction.id}
                        className={`flex items-center justify-between p-2 rounded-md ${
                          transaction.transaction_type === "in"
                            ? "bg-green-50 border border-green-100"
                            : "bg-red-50 border border-red-100"
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <div
                            className={`h-8 w-8 rounded-full flex items-center justify-center ${
                              transaction.transaction_type === "in" ? "bg-green-100" : "bg-red-100"
                            }`}
                          >
                            {transaction.transaction_type === "in" ? (
                              <LucideArrowUpRight className="h-4 w-4 text-green-600" />
                            ) : (
                              <LucideArrowDownRight className="h-4 w-4 text-red-600" />
                            )}
                          </div>
                          <div>
                            <div className="font-medium">{transaction.item}</div>
                            <div className="text-xs text-muted-foreground">
                              {new Date(transaction.transaction_date).toLocaleDateString()} • {transaction.created_at ? new Date(transaction.created_at).toLocaleTimeString() : ''}
                            </div>
                          </div>
                        </div>
                        <div className={`font-medium ${transaction.transaction_type === "in" ? "text-green-600" : "text-red-600"}`}>
                          {transaction.transaction_type === "in" ? "+" : "-"}
                          {transaction.quantity} {transaction.unit || ''}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 text-muted-foreground">
                    No recent transactions found.
                  </div>
                )}
                <div className="mt-4 text-center">
                  <Link href="/inventory/transactions">
                    <Button variant="outline" className="btn-outline-accent">
                      View All Transactions
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Items Tab */}
          <TabsContent value="items" className="space-y-4">
            {/* Search and Filters */}
            <Card className="overflow-hidden">
              <CardHeader className="p-4 pb-0">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                  <CardTitle>Inventory Items</CardTitle>
                  <div className="flex items-center gap-2">
                    <LucideFilter className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Filter items</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-4">
                <div className="flex flex-col gap-4">
                  <div className="relative">
                    <LucideSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by name, ID or category..."
                      className="pl-10 pr-10 input-primary"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    {searchTerm && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-teal-50 hover:text-teal-700"
                        onClick={() => setSearchTerm("")}
                      >
                        <LucideX className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid gap-4 md:grid-cols-3">
                    <div>
                      <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                        <SelectTrigger>
                          <SelectValue placeholder="Category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Categories</SelectItem>
                          <SelectItem value="Feed">Feed</SelectItem>
                          <SelectItem value="Medication">Medication</SelectItem>
                          <SelectItem value="Equipment">Equipment</SelectItem>
                          <SelectItem value="Supplies">Supplies</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Select value={stockLevelFilter} onValueChange={setStockLevelFilter}>
                        <SelectTrigger>
                          <SelectValue placeholder="Stock Level" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Stock Levels</SelectItem>
                          <SelectItem value="low">Low Stock</SelectItem>
                          <SelectItem value="normal">Normal Stock</SelectItem>
                          <SelectItem value="high">High Stock</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Button
                      variant="outline"
                      className="flex items-center gap-2 border-teal-500 text-teal-600 hover:bg-teal-50 hover:text-teal-700"
                      onClick={resetFilters}
                      disabled={categoryFilter === "all" && stockLevelFilter === "all" && !searchTerm}
                    >
                      <LucideRefreshCw className="h-4 w-4" />
                      Reset Filters
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Inventory Items Table */}
            <Card>
              <CardContent className="p-0 overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Stock Level</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Expiry Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      // Loading skeleton for items
                      Array(5).fill(0).map((_, i) => (
                        <TableRow key={i}>
                          <TableCell><div className="h-4 w-10 bg-gray-200 animate-pulse rounded"></div></TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <div className="h-4 w-4 bg-gray-200 animate-pulse rounded-full"></div>
                              <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
                            </div>
                          </TableCell>
                          <TableCell><div className="h-4 w-20 bg-gray-200 animate-pulse rounded"></div></TableCell>
                          <TableCell><div className="h-4 w-16 bg-gray-200 animate-pulse rounded"></div></TableCell>
                          <TableCell>
                            <div className="flex flex-col gap-1">
                              <div className="h-6 w-16 bg-gray-200 animate-pulse rounded-full"></div>
                              <div className="h-2 w-full bg-gray-200 animate-pulse rounded"></div>
                            </div>
                          </TableCell>
                          <TableCell><div className="h-4 w-20 bg-gray-200 animate-pulse rounded"></div></TableCell>
                          <TableCell><div className="h-4 w-24 bg-gray-200 animate-pulse rounded"></div></TableCell>
                          <TableCell className="text-right"><div className="h-8 w-16 bg-gray-200 animate-pulse rounded ml-auto"></div></TableCell>
                        </TableRow>
                      ))
                    ) : filteredItems.length > 0 ? (
                      filteredItems.map((item: any) => (
                        <TableRow key={item.id} className="hover-lift">
                          <TableCell className="font-medium">{item.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getCategoryIcon(item.category)}
                              {item.name}
                            </div>
                          </TableCell>
                          <TableCell>{item.category}</TableCell>
                          <TableCell>
                            {item.quantity} {item.unit || ''}
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col gap-1">
                              {getStockLevelBadge(item)}
                              {item.max_level && (
                                <Progress
                                  value={(item.quantity / item.max_level) * 100}
                                  className={`h-2 ${
                                    item.min_level !== null && item.quantity <= item.min_level
                                      ? "bg-red-100"
                                      : item.max_level !== null && item.quantity >= item.max_level * 0.9
                                        ? "bg-green-100"
                                        : "bg-amber-100"
                                  }`}
                                />
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{item.location || 'N/A'}</TableCell>
                          <TableCell>
                            {item.expiry_date ? new Date(item.expiry_date).toLocaleDateString() : "N/A"}
                          </TableCell>
                          <TableCell className="text-right">
                            <Link href={`/inventory/${item.id}`}>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="bg-gradient-to-r from-teal-50 to-cyan-50 text-teal-600 hover:from-teal-100 hover:to-cyan-100 hover:text-teal-700"
                              >
                                View
                              </Button>
                            </Link>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-6">
                          <div className="flex flex-col items-center justify-center p-8">
                            <div className="rounded-full bg-muted p-3 mb-4">
                              <LucideSearch className="h-6 w-6 text-muted-foreground" />
                            </div>
                            <h3 className="text-lg font-medium mb-1">No items found</h3>
                            <p className="text-muted-foreground mb-4">Try adjusting your search or filters</p>
                            <Button variant="outline" onClick={resetFilters}>
                              Reset Filters
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Transactions Tab */}
          <TabsContent value="transactions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>History of inventory movements</CardDescription>
              </CardHeader>
              <CardContent className="p-0 overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Item</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      // Loading skeleton for transactions
                      Array(5).fill(0).map((_, i) => (
                        <TableRow key={i}>
                          <TableCell><div className="h-4 w-16 bg-gray-200 animate-pulse rounded"></div></TableCell>
                          <TableCell><div className="h-4 w-24 bg-gray-200 animate-pulse rounded"></div></TableCell>
                          <TableCell><div className="h-6 w-16 bg-gray-200 animate-pulse rounded-full"></div></TableCell>
                          <TableCell><div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div></TableCell>
                          <TableCell><div className="h-4 w-20 bg-gray-200 animate-pulse rounded"></div></TableCell>
                          <TableCell><div className="h-4 w-20 bg-gray-200 animate-pulse rounded"></div></TableCell>
                          <TableCell className="text-right"><div className="h-8 w-16 bg-gray-200 animate-pulse rounded ml-auto"></div></TableCell>
                        </TableRow>
                      ))
                    ) : recentTransactions && recentTransactions.length > 0 ? (
                      recentTransactions.map((transaction: any) => (
                        <TableRow key={transaction.id} className="hover-lift">
                          <TableCell className="font-medium">{transaction.id}</TableCell>
                          <TableCell>{new Date(transaction.transaction_date).toLocaleDateString()}</TableCell>
                          <TableCell>
                            {transaction.transaction_type === "in" ? (
                              <Badge className="badge-healthy">Stock In</Badge>
                            ) : (
                              <Badge className="badge-amber">Stock Out</Badge>
                            )}
                          </TableCell>
                          <TableCell>{transaction.item}</TableCell>
                          <TableCell>
                            <span
                              className={
                                transaction.transaction_type === "in" ? "text-green-600 font-medium" : "text-red-600 font-medium"
                              }
                            >
                              {transaction.transaction_type === "in" ? "+" : "-"}
                              {transaction.quantity} {transaction.unit || ''}
                            </span>
                          </TableCell>
                          <TableCell>{new Date(transaction.created_at).toLocaleTimeString()}</TableCell>
                          <TableCell className="text-right">
                            <Link href={`/inventory/transactions/${transaction.id}`}>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="bg-gradient-to-r from-sky-50 to-blue-50 text-sky-600 hover:from-sky-100 hover:to-blue-100 hover:text-sky-700"
                              >
                                View
                              </Button>
                            </Link>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-6">
                          <div className="flex flex-col items-center justify-center p-8">
                            <div className="rounded-full bg-muted p-3 mb-4">
                              <LucidePackage className="h-6 w-6 text-muted-foreground" />
                            </div>
                            <h3 className="text-lg font-medium mb-1">No transactions found</h3>
                            <p className="text-muted-foreground mb-4">There are no inventory transactions recorded yet</p>
                            <Link href="/inventory/add-transaction">
                              <Button className="bg-gradient-to-r from-blue-500 to-sky-500 hover:from-blue-600 hover:to-sky-600 text-white">
                                <LucidePlus className="mr-2 h-4 w-4" />
                                Add Transaction
                              </Button>
                            </Link>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}

