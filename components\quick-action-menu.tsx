"use client"
import { useRouter } from "next/navigation"
import { usePermissions } from "@/hooks/usePermissions"
import { Permission } from "@/lib/permissions"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import {
  LucidePlus,
  LucideUsers,
  LucideHeart,
  LucideCalendarClock,
  LucideWheat,
  LucideCoins,
  LucideShoppingCart,
  LucideClipboardList,
  LucidePill,
  LucideCalendarDays,
  LucideActivity,
} from "lucide-react"

export function QuickActionMenu() {
  const router = useRouter()
  const { hasPermission, isLoading } = usePermissions()

  // Define quick actions with their required permissions
  const quickActions = [
    {
      id: 'goats',
      label: 'Goat Registry',
      icon: LucideUsers,
      iconColor: 'text-emerald-600',
      permission: 'view_goats' as Permission,
      subActions: [
        {
          label: 'Add New Goat',
          icon: LucidePlus,
          route: '/goats/add',
          permission: 'manage_goats' as Permission
        },
        {
          label: 'View All Goats',
          icon: LucideUsers,
          route: '/goats',
          permission: 'view_goats' as Permission
        }
      ]
    },
    {
      id: 'health',
      label: 'Health',
      icon: LucideHeart,
      iconColor: 'text-red-600',
      permission: 'add_health_records' as Permission,
      subActions: [
        {
          label: 'Add Health Record',
          icon: LucidePlus,
          route: '/health/add',
          permission: 'add_health_records' as Permission
        },
        {
          label: 'Record Vaccination',
          icon: LucidePill,
          route: '/health/vaccination',
          permission: 'add_health_records' as Permission
        },
        {
          label: 'Record Treatment',
          icon: LucideActivity,
          route: '/health/treatment',
          permission: 'manage_health_records' as Permission
        }
      ]
    },
    {
      id: 'breeding',
      label: 'Breeding',
      icon: LucideCalendarClock,
      iconColor: 'text-pink-600',
      permission: 'manage_breeding' as Permission,
      subActions: [
        {
          label: 'Record Heat Cycle',
          icon: LucideCalendarDays,
          route: '/breeding/heat-cycle',
          permission: 'manage_breeding' as Permission
        },
        {
          label: 'Add Breeding Record',
          icon: LucidePlus,
          route: '/breeding/add',
          permission: 'manage_breeding' as Permission
        }
      ]
    },
    {
      id: 'feeding',
      label: 'Feeding',
      icon: LucideWheat,
      iconColor: 'text-amber-600',
      permission: 'manage_feeding' as Permission,
      subActions: [
        {
          label: 'Record Feeding',
          icon: LucideClipboardList,
          route: '/feeding/record',
          permission: 'manage_feeding' as Permission
        },
        {
          label: 'Add Feed Inventory',
          icon: LucidePlus,
          route: '/feeding/inventory/add',
          permission: 'manage_feeding' as Permission
        }
      ]
    },
    {
      id: 'finance',
      label: 'Finance',
      icon: LucideCoins,
      iconColor: 'text-cyan-600',
      permission: 'manage_finances' as Permission,
      subActions: [
        {
          label: 'Add Transaction',
          icon: LucidePlus,
          route: '/finance/add',
          permission: 'manage_finances' as Permission
        }
      ]
    },
    {
      id: 'inventory',
      label: 'Inventory',
      icon: LucideShoppingCart,
      iconColor: 'text-teal-600',
      permission: 'manage_inventory' as Permission,
      subActions: [
        {
          label: 'Add Inventory Item',
          icon: LucidePlus,
          route: '/inventory/add',
          permission: 'manage_inventory' as Permission
        },
        {
          label: 'Record Transaction',
          icon: LucideShoppingCart,
          route: '/inventory/transaction',
          permission: 'manage_inventory' as Permission
        }
      ]
    }
  ]

  // Filter actions based on user permissions
  const visibleActions = quickActions.filter(action =>
    hasPermission(action.permission)
  ).map(action => ({
    ...action,
    subActions: action.subActions.filter(subAction =>
      hasPermission(subAction.permission)
    )
  })).filter(action => action.subActions.length > 0) // Only show actions that have visible sub-actions

  // Don't show the menu if user has no permissions or is loading
  if (isLoading || visibleActions.length === 0) {
    return null
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white shadow-md hover:shadow-lg transition-all duration-300">
          <LucidePlus className="mr-2 h-4 w-4" />
          Quick Action
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuGroup>
          {visibleActions.map((action) => (
            <DropdownMenuSub key={action.id}>
              <DropdownMenuSubTrigger>
                <action.icon className={`mr-2 h-4 w-4 ${action.iconColor}`} />
                <span>{action.label}</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  {action.subActions.map((subAction, index) => (
                    <DropdownMenuItem
                      key={index}
                      onClick={() => router.push(subAction.route)}
                    >
                      <subAction.icon className="mr-2 h-4 w-4" />
                      <span>{subAction.label}</span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
          ))}
        </DropdownMenuGroup>

        {/* Calendar action - available to all authenticated users */}
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => router.push("/calendar")}>
          <LucideCalendarDays className="mr-2 h-4 w-4 text-purple-600" />
          <span>View Calendar</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

