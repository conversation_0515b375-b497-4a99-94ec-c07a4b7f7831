import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

export async function POST(request) {
  try {
    const data = await request.json();
    console.log('Received inventory item data:', data);
    
    // Validate required fields
    if (!data.name || !data.category || !data.quantity) {
      return NextResponse.json(
        { error: 'Missing required fields: name, category, and quantity are required' },
        { status: 400 }
      );
    }
    
    // Insert data into the inventory_items table
    const [result] = await pool.execute(
      `INSERT INTO inventory_items (
        item_name,
        item_type,
        quantity,
        purchase_date,
        purchase_price,
        supplier,
        location,
        notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        data.name,
        data.category,
        data.quantity,
        data.purchaseDate || null,
        data.price || null,
        data.supplier || null,
        data.location || null,
        data.notes || null
      ]
    );
    
    // Get the inserted ID
    const inventoryItemId = result.insertId;
    console.log('Inventory item inserted with ID:', inventoryItemId);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Inventory item created successfully',
      id: inventoryItemId
    });
    
  } catch (error) {
    console.error('Error creating inventory item:', error);
    return NextResponse.json(
      { error: `Failed to create inventory item: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Query to get all inventory items
    const [rows] = await pool.execute(`
      SELECT 
        id, item_name, item_type, quantity, 
        purchase_date, purchase_price, supplier, location, notes,
        created_at, updated_at
      FROM inventory_items
      ORDER BY created_at DESC
    `);
    
    return NextResponse.json(rows);
  } catch (error) {
    console.error('Error fetching inventory items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch inventory items' },
      { status: 500 }
    );
  }
}