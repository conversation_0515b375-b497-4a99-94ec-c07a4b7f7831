"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  LucideSearch,
  LucideX,
  CatIcon as LucideGoat,
  LucideHeart,
  LucideCalendarClock,
  LucideClipboardList,
} from "lucide-react"
import Link from "next/link"

// Define the types of searchable items
type SearchableItem = {
  id: string
  type: "goat" | "health" | "breeding" | "feeding" | "finance"
  title: string
  subtitle?: string
  tags?: string[]
  url: string
}

// Sample data for demonstration - in a real app, this would come from an API
const sampleSearchData: SearchableItem[] = [
  {
    id: "G-2023-01",
    type: "goat",
    title: "Bella",
    subtitle: "Boer • Female • 2 years",
    tags: ["Healthy"],
    url: "/goats/G-2023-01",
  },
  {
    id: "G-2023-02",
    type: "goat",
    title: "Max",
    subtitle: "Alpine • Male • 3 years",
    tags: ["Healthy"],
    url: "/goats/G-2023-02",
  },
  {
    id: "G-2023-03",
    type: "goat",
    title: "Luna",
    subtitle: "Nubian • Female • 1 year",
    tags: ["Pregnant"],
    url: "/goats/G-2023-03",
  },
  {
    id: "HR-2023-01",
    type: "health",
    title: "Vaccination: Bella",
    subtitle: "Annual vaccination • March 15, 2023",
    url: "/health/1",
  },
  {
    id: "BR-2023-01",
    type: "breeding",
    title: "Breeding: Luna & Max",
    subtitle: "Breeding date: Jan 10, 2023",
    tags: ["Confirmed Pregnant"],
    url: "/breeding/1",
  },
  {
    id: "FR-2023-01",
    type: "feeding",
    title: "Hay Inventory",
    subtitle: "500kg • Expires: June 5, 2023",
    tags: ["Adequate"],
    url: "/feeding/inventory/1",
  },
]

export default function SearchBar() {
  const [searchTerm, setSearchTerm] = useState("")
  const [searchResults, setSearchResults] = useState<SearchableItem[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)

    if (value.length > 1) {
      setIsSearching(true)
      // Simulate API call delay
      setTimeout(() => {
        const filtered = sampleSearchData.filter(
          (item) =>
            item.title.toLowerCase().includes(value.toLowerCase()) ||
            item.subtitle?.toLowerCase().includes(value.toLowerCase()) ||
            item.id.toLowerCase().includes(value.toLowerCase()) ||
            item.tags?.some((tag) => tag.toLowerCase().includes(value.toLowerCase())),
        )
        setSearchResults(filtered)
        setIsSearching(false)
        setShowResults(true)
      }, 300)
    } else {
      setSearchResults([])
      setShowResults(false)
    }
  }

  // Clear search
  const clearSearch = () => {
    setSearchTerm("")
    setSearchResults([])
    setShowResults(false)
  }

  // Handle click outside to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      setShowResults(false)
    } else if (e.key === "Enter" && searchResults.length > 0) {
      router.push(searchResults[0].url)
      setShowResults(false)
    }
  }

  // Get icon based on item type
  const getItemIcon = (type: string) => {
    switch (type) {
      case "goat":
        return <LucideGoat className="h-4 w-4 text-emerald-600" />
      case "health":
        return <LucideHeart className="h-4 w-4 text-red-600" />
      case "breeding":
        return <LucideCalendarClock className="h-4 w-4 text-blue-600" />
      case "feeding":
        return <LucideClipboardList className="h-4 w-4 text-amber-600" />
      default:
        return <LucideSearch className="h-4 w-4" />
    }
  }

  return (
    <div className="relative" ref={searchRef}>
      <div className="relative">
        <LucideSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search goats, health records, etc..."
          className="h-9 w-[180px] lg:w-[280px] rounded-full bg-muted px-10 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500/50 border-emerald-100"
          value={searchTerm}
          onChange={handleSearchChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (searchTerm.length > 1) setShowResults(true)
          }}
        />
        {searchTerm && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-0 rounded-full hover:bg-emerald-100 hover:text-emerald-700"
            onClick={clearSearch}
          >
            <LucideX className="h-4 w-4" />
            <span className="sr-only">Clear search</span>
          </Button>
        )}
      </div>

      {/* Search Results Dropdown */}
      {showResults && (
        <Card className="absolute top-full mt-1 w-full lg:w-[350px] z-50 max-h-[400px] overflow-auto shadow-lg border-green-100 animate-in fade-in-50 zoom-in-95 duration-100">
          <div className="p-2">
            {isSearching ? (
              <div className="flex items-center justify-center p-4">
                <div className="animate-pulse flex space-x-2 items-center">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                </div>
              </div>
            ) : searchResults.length > 0 ? (
              <div className="space-y-1">
                {searchResults.map((result) => (
                  <Link
                    key={result.id}
                    href={result.url}
                    className="flex items-start gap-3 p-2 rounded-md hover:bg-emerald-50 transition-colors"
                    onClick={() => setShowResults(false)}
                  >
                    <div className="mt-0.5">{getItemIcon(result.type)}</div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div className="font-medium truncate">{result.title}</div>
                        <div className="text-xs text-muted-foreground">{result.id}</div>
                      </div>
                      {result.subtitle && (
                        <div className="text-sm text-muted-foreground truncate">{result.subtitle}</div>
                      )}
                      {result.tags && result.tags.length > 0 && (
                        <div className="flex gap-1 mt-1">
                          {result.tags.map((tag) => (
                            <Badge
                              key={tag}
                              variant="outline"
                              className={`text-xs py-0 ${
                                tag === "Healthy"
                                  ? "badge-healthy"
                                  : tag === "Sick"
                                    ? "badge-sick"
                                    : tag === "Pregnant"
                                      ? "badge-pregnant"
                                      : tag === "Lactating"
                                        ? "badge-lactating"
                                        : "badge-primary"
                              }`}
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">No results found for "{searchTerm}"</div>
            )}
          </div>
        </Card>
      )}
    </div>
  )
}

