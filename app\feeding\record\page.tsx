"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { LoadingButton } from "@/components/ui/loading-button"
import { BackButton } from "@/components/ui/back-button"
import {
  LucideClipboardList,
  LucideSave,
  LucideCalendarClock,
  LucideInfo,
  LucideAlarmClock,
  LucidePlus,
} from "lucide-react"


// Animal type options
const animalTypeOptions = [
  { value: "Goat", label: "Goats", groupLabel: "Goat Groups" },
  { value: "Sheep", label: "Sheep", groupLabel: "Sheep Groups" },
  { value: "Cattle", label: "Cattle", groupLabel: "Cattle Groups" },
  { value: "Pig", label: "Pigs", groupLabel: "Pig Groups" },
]

// Sample feeding schedules data - now supports all animal types
const feedingSchedules = [
  {
    id: "FS-001",
    name: "Morning Feed - All Goats",
    time: "07:00",
    days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
    animalType: "Goat",
    animalGroups: [{ id: "GG-001", name: "All Goats", type: "Goat" }],
    feeds: [
      { name: "Alfalfa Hay", amount: "2.5", unit: "kg" },
      { name: "Grain Mix", amount: "0.5", unit: "kg" },
    ],
    notes: "Ensure fresh water is available",
    active: true,
  },
  {
    id: "FS-002",
    name: "Evening Feed - All Goats",
    time: "17:00",
    days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
    animalType: "Goat",
    animalGroups: [{ id: "GG-001", name: "All Goats", type: "Goat" }],
    feeds: [
      { name: "Alfalfa Hay", amount: "2.5", unit: "kg" },
      { name: "Grain Mix", amount: "0.5", unit: "kg" },
    ],
    notes: "Check water levels",
    active: true,
  },
  {
    id: "FS-003",
    name: "Pregnant Does - Supplement",
    time: "12:00",
    days: ["Monday", "Wednesday", "Friday"],
    animalType: "Goat",
    animalGroups: [{ id: "GG-002", name: "Pregnant Does", type: "Goat" }],
    feeds: [
      { name: "Protein Supplement", amount: "0.25", unit: "kg" },
      { name: "Alfalfa Pellets", amount: "0.5", unit: "kg" },
    ],
    notes: "Adjust amount based on trimester",
    active: true,
  },
  {
    id: "FS-004",
    name: "Lactating Does - Extra Feed",
    time: "12:00",
    days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
    animalType: "Goat",
    animalGroups: [{ id: "GG-003", name: "Lactating Does", type: "Goat" }],
    feeds: [
      { name: "Alfalfa Hay", amount: "1", unit: "kg" },
      { name: "Grain Mix", amount: "0.75", unit: "kg" },
    ],
    notes: "Adjust based on milk production",
    active: true,
  },
]

// Sample animal groups - now supports all animal types
const animalGroups = {
  Goat: [
    { id: "GG-001", name: "All Goats", count: 10, type: "Goat" },
    { id: "GG-002", name: "Pregnant Does", count: 2, type: "Goat" },
    { id: "GG-003", name: "Lactating Does", count: 3, type: "Goat" },
    { id: "GG-004", name: "Kids", count: 4, type: "Goat" },
    { id: "GG-005", name: "Bucks", count: 1, type: "Goat" },
  ],
  Sheep: [
    { id: "SG-001", name: "All Sheep", count: 8, type: "Sheep" },
    { id: "SG-002", name: "Pregnant Ewes", count: 2, type: "Sheep" },
    { id: "SG-003", name: "Lactating Ewes", count: 3, type: "Sheep" },
    { id: "SG-004", name: "Lambs", count: 2, type: "Sheep" },
    { id: "SG-005", name: "Rams", count: 1, type: "Sheep" },
  ],
  Cattle: [
    { id: "CG-001", name: "All Cattle", count: 5, type: "Cattle" },
    { id: "CG-002", name: "Pregnant Cows", count: 1, type: "Cattle" },
    { id: "CG-003", name: "Lactating Cows", count: 2, type: "Cattle" },
    { id: "CG-004", name: "Calves", count: 1, type: "Cattle" },
    { id: "CG-005", name: "Bulls", count: 1, type: "Cattle" },
  ],
  Pig: [
    { id: "PG-001", name: "All Pigs", count: 6, type: "Pig" },
    { id: "PG-002", name: "Pregnant Sows", count: 1, type: "Pig" },
    { id: "PG-003", name: "Lactating Sows", count: 2, type: "Pig" },
    { id: "PG-004", name: "Piglets", count: 2, type: "Pig" },
    { id: "PG-005", name: "Boars", count: 1, type: "Pig" },
  ],
}

// Sample feed items
const feedItems = [
  { id: "FEED-001", name: "Alfalfa Hay", category: "Hay", unit: "kg" },
  { id: "FEED-002", name: "Grain Mix", category: "Grain", unit: "kg" },
  { id: "FEED-003", name: "Mineral Blocks", category: "Supplements", unit: "blocks" },
  { id: "FEED-004", name: "Alfalfa Pellets", category: "Pellets", unit: "kg" },
  { id: "FEED-005", name: "Grass Hay", category: "Hay", unit: "kg" },
  { id: "FEED-006", name: "Protein Supplement", category: "Supplements", unit: "kg" },
]

export default function RecordFeedingPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const scheduleId = searchParams.get("scheduleId")

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    recordType: "schedule", // schedule or custom
    scheduleId: "",
    animalType: "Goat", // Default to Goat
    date: new Date().toISOString().split("T")[0],
    time: new Date().toTimeString().split(" ")[0].substring(0, 5),
    animalGroups: [],
    feedItems: [{ feedId: "", amount: "", unit: "" }],
    notes: "",
    consumptionLevel: "normal", // low, normal, high
    waterRefilled: true,
  })

  // Initialize form with schedule data if scheduleId is provided
  useEffect(() => {
    if (scheduleId) {
      const schedule = feedingSchedules.find((s) => s.id === scheduleId)
      if (schedule) {
        setFormData((prev) => ({
          ...prev,
          recordType: "schedule",
          scheduleId: schedule.id,
          animalType: schedule.animalType,
          animalGroups: schedule.animalGroups,
          feedItems: schedule.feeds.map((feed) => {
            const feedItem = feedItems.find((f) => f.name === feed.name)
            return {
              feedId: feedItem?.id || "",
              amount: feed.amount,
              unit: feed.unit,
            }
          }),
          notes: schedule.notes,
        }))
      }
    }
  }, [scheduleId])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))
  }

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleScheduleChange = (scheduleId) => {
    setFormData((prev) => ({ ...prev, scheduleId }))

    // Populate form with schedule data
    const schedule = feedingSchedules.find((s) => s.id === scheduleId)
    if (schedule) {
      setFormData((prev) => ({
        ...prev,
        scheduleId,
        animalType: schedule.animalType,
        animalGroups: schedule.animalGroups,
        feedItems: schedule.feeds.map((feed) => {
          const feedItem = feedItems.find((f) => f.name === feed.name)
          return {
            feedId: feedItem?.id || "",
            amount: feed.amount,
            unit: feed.unit,
          }
        }),
        notes: schedule.notes,
      }))
    }
  }

  const handleAnimalGroupChange = (group) => {
    setFormData((prev) => {
      const isSelected = prev.animalGroups.some(g => g.id === group.id)
      if (isSelected) {
        return {
          ...prev,
          animalGroups: prev.animalGroups.filter((g) => g.id !== group.id),
        }
      } else {
        return {
          ...prev,
          animalGroups: [...prev.animalGroups, group],
        }
      }
    })
  }

  const handleFeedItemChange = (index, field, value) => {
    setFormData((prev) => {
      const updatedFeedItems = [...prev.feedItems]
      updatedFeedItems[index] = {
        ...updatedFeedItems[index],
        [field]: value,
      }

      // If feed item is selected, set the unit
      if (field === "feedId") {
        const feedItem = feedItems.find((f) => f.id === value)
        if (feedItem) {
          updatedFeedItems[index].unit = feedItem.unit
        }
      }

      return {
        ...prev,
        feedItems: updatedFeedItems,
      }
    })
  }

  const addFeedItem = () => {
    setFormData((prev) => ({
      ...prev,
      feedItems: [...prev.feedItems, { feedId: "", amount: "", unit: "" }],
    }))
  }

  const removeFeedItem = (index) => {
    setFormData((prev) => {
      const updatedFeedItems = [...prev.feedItems]
      updatedFeedItems.splice(index, 1)
      return {
        ...prev,
        feedItems: updatedFeedItems,
      }
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Prepare the data to be sent to the API
      const feedingData = {
        recordType: formData.recordType,
        scheduleId: formData.scheduleId || null,
        date: formData.date,
        time: formData.time,
        animalGroups: formData.animalGroups,
        goatGroups: formData.animalGroups, // Keep for backward compatibility
        feedItems: formData.feedItems.filter(item => item.feedId && item.amount),
        consumptionLevel: formData.consumptionLevel,
        waterRefilled: formData.waterRefilled,
        notes: formData.notes,
        recordedBy: "Current User" // Replace with actual user info when available
      }

      // Send the data to the API
      const response = await fetch('/api/feeding/records', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(feedingData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create feeding record')
      }

      // Success - redirect to records page
      router.push("/feeding/records")
    } catch (error) {
      console.error('Error creating feeding record:', error)
      // Add toast notification here if you have a toast component
      alert(`Failed to create feeding record: ${error.message}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get feed name from ID
  const getFeedName = (feedId) => {
    const feed = feedItems.find((f) => f.id === feedId)
    return feed ? feed.name : ""
  }

  return (
    <div className="flex flex-col gap-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <h1 className="text-3xl font-bold tracking-tight text-gradient-secondary">Record Feeding</h1>
          <BackButton href="/feeding/records" label="Back to Feeding Records" variant="outline" className="btn-outline-secondary" />
        </div>

        {/* Form */}
        <Card className="border-t-4 border-t-amber-500">
          <CardHeader>
            <div className="flex items-center gap-2">
              <LucideClipboardList className="h-6 w-6 text-amber-500" />
              <CardTitle>Feeding Record</CardTitle>
            </div>
            <CardDescription>Document what, when, and which animals were fed</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              {/* Animal Type Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">Animal Type</h3>
                <div className="space-y-2">
                  <Label htmlFor="animalType">
                    Select Animal Type <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.animalType}
                    onValueChange={(value) => {
                      handleSelectChange("animalType", value);
                      // Reset animal groups when type changes
                      setFormData(prev => ({ ...prev, animalGroups: [] }));
                    }}
                    required
                  >
                    <SelectTrigger id="animalType" className="input-primary">
                      <SelectValue placeholder="Select animal type" />
                    </SelectTrigger>
                    <SelectContent>
                      {animalTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Record Type Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">Record Type</h3>
                <RadioGroup
                  value={formData.recordType}
                  onValueChange={(value) => handleSelectChange("recordType", value)}
                  className="flex flex-col sm:flex-row gap-4"
                >
                  <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-amber-50 cursor-pointer transition-colors duration-200 border-amber-200 data-[state=checked]:bg-amber-50 data-[state=checked]:border-amber-500">
                    <RadioGroupItem value="schedule" id="schedule" className="text-amber-600" />
                    <Label htmlFor="schedule" className="cursor-pointer">
                      From Feeding Schedule
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 border rounded-lg p-4 hover:bg-amber-50 cursor-pointer transition-colors duration-200 border-amber-200 data-[state=checked]:bg-amber-50 data-[state=checked]:border-amber-500">
                    <RadioGroupItem value="custom" id="custom" className="text-amber-600" />
                    <Label htmlFor="custom" className="cursor-pointer">
                      Custom Feeding
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Schedule Selection (if from schedule) */}
              {formData.recordType === "schedule" && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-amber-700">Feeding Schedule</h3>
                  <div className="space-y-2">
                    <Label htmlFor="scheduleId">
                      Select Schedule <span className="text-red-500">*</span>
                    </Label>
                    <Select value={formData.scheduleId} onValueChange={handleScheduleChange} required>
                      <SelectTrigger id="scheduleId" className="input-primary">
                        <SelectValue placeholder="Select a feeding schedule" />
                      </SelectTrigger>
                      <SelectContent>
                        {feedingSchedules.map((schedule) => (
                          <SelectItem key={schedule.id} value={schedule.id}>
                            {schedule.name} ({schedule.time})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {formData.scheduleId && (
                    <div className="bg-amber-50 p-4 rounded-md border border-amber-200 flex gap-3">
                      <LucideInfo className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
                      <div className="text-sm text-amber-800">
                        <p className="font-medium">Selected Schedule Details:</p>
                        <p>
                          {feedingSchedules.find((s) => s.id === formData.scheduleId)?.name} - Time:{" "}
                          {feedingSchedules.find((s) => s.id === formData.scheduleId)?.time} - Groups:{" "}
                          {feedingSchedules.find((s) => s.id === formData.scheduleId)?.goatGroups.join(", ")}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Date and Time */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">When</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">
                      Date <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="date"
                        name="date"
                        type="date"
                        value={formData.date}
                        onChange={handleChange}
                        required
                        className="input-primary pl-10"
                      />
                      <LucideCalendarClock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-amber-500" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="time">
                      Time <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="time"
                        name="time"
                        type="time"
                        value={formData.time}
                        onChange={handleChange}
                        required
                        className="input-primary pl-10"
                      />
                      <LucideAlarmClock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-amber-500" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Animal Groups */}
              {formData.recordType === "custom" && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-amber-700">Who</h3>
                  <div className="space-y-2">
                    <Label>
                      Select {animalTypeOptions.find(opt => opt.value === formData.animalType)?.groupLabel || 'Animal Groups'} <span className="text-red-500">*</span>
                    </Label>
                    <div className="border rounded-md p-4 bg-amber-50/30">
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                        {(animalGroups[formData.animalType] || []).map((group) => (
                          <div key={group.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={group.id}
                              checked={formData.animalGroups.some(g => g.id === group.id)}
                              onCheckedChange={() => handleAnimalGroupChange(group)}
                            />
                            <Label htmlFor={group.id} className="cursor-pointer">
                              {group.name} ({group.count} {formData.animalType.toLowerCase()}s)
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                    {formData.animalGroups.length === 0 && (
                      <p className="text-sm text-red-500">Please select at least one {formData.animalType.toLowerCase()} group</p>
                    )}
                  </div>
                </div>
              )}

              {/* Feed Items */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">What</h3>
                <div className="space-y-4">
                  {formData.feedItems.map((item, index) => (
                    <div key={index} className="flex flex-col space-y-2 p-4 border rounded-md">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">Feed Item {index + 1}</h4>
                        {formData.recordType === "custom" && formData.feedItems.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => removeFeedItem(index)}
                          >
                            &times;
                          </Button>
                        )}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`feed-${index}`}>
                            Feed Type <span className="text-red-500">*</span>
                          </Label>
                          {formData.recordType === "custom" ? (
                            <Select
                              value={item.feedId}
                              onValueChange={(value) => handleFeedItemChange(index, "feedId", value)}
                              required
                            >
                              <SelectTrigger id={`feed-${index}`} className="input-primary">
                                <SelectValue placeholder="Select feed" />
                              </SelectTrigger>
                              <SelectContent>
                                {feedItems.map((feed) => (
                                  <SelectItem key={feed.id} value={feed.id}>
                                    {feed.name} ({feed.category})
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          ) : (
                            <div className="h-10 px-3 py-2 rounded-md border bg-muted">{getFeedName(item.feedId)}</div>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`amount-${index}`}>
                            Amount <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id={`amount-${index}`}
                            value={item.amount}
                            onChange={(e) => handleFeedItemChange(index, "amount", e.target.value)}
                            required
                            className="input-primary"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`unit-${index}`}>Unit</Label>
                          <div className="h-10 px-3 py-2 rounded-md border bg-muted">{item.unit}</div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {formData.recordType === "custom" && (
                    <Button
                      type="button"
                      variant="outline"
                      className="w-full border-amber-500 text-amber-600 hover:bg-amber-50"
                      onClick={addFeedItem}
                    >
                      <LucidePlus className="mr-2 h-4 w-4" />
                      Add Another Feed Item
                    </Button>
                  )}
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-amber-700">Additional Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="consumptionLevel">Consumption Level</Label>
                    <Select
                      value={formData.consumptionLevel}
                      onValueChange={(value) => handleSelectChange("consumptionLevel", value)}
                    >
                      <SelectTrigger id="consumptionLevel" className="input-primary">
                        <SelectValue placeholder="Select consumption level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low (Less than expected)</SelectItem>
                        <SelectItem value="normal">Normal (As expected)</SelectItem>
                        <SelectItem value="high">High (More than expected)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center space-x-2 mt-8">
                    <Checkbox
                      id="waterRefilled"
                      checked={formData.waterRefilled}
                      onCheckedChange={(checked) =>
                        setFormData((prev) => ({ ...prev, waterRefilled: checked === true }))
                      }
                    />
                    <Label htmlFor="waterRefilled" className="cursor-pointer">
                      Water was refilled/checked
                    </Label>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    placeholder="Enter any observations or notes about this feeding session"
                    value={formData.notes}
                    onChange={handleChange}
                    className="input-primary min-h-[100px]"
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" onClick={() => router.push("/feeding/records")}>
                Cancel
              </Button>
              <LoadingButton
                type="submit"
                isLoading={isSubmitting}
                loadingText="Saving..."
                disabled={formData.animalGroups.length === 0}
                className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
              >
                <LucideSave className="mr-2 h-4 w-4" />
                Save Feeding Record
              </LoadingButton>
            </CardFooter>
          </form>
        </Card>
    </div>
  )
}


