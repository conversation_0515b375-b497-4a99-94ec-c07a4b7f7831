const mysql = require('mysql2/promise');
require('dotenv').config();

async function inspectAllTables() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'goat_management'
    });

    console.log('Connected to database');

    const tablesToInspect = [
      'health_records',
      'breeding_records', 
      'heat_cycles',
      'financial_transactions',
      'feeding_records',
      'feed_items',
      'inventory_items'
    ];

    for (const tableName of tablesToInspect) {
      // Check if table exists
      const [tables] = await connection.execute(`
        SELECT COUNT(*) as count FROM information_schema.tables 
        WHERE table_schema = DATABASE() AND table_name = ?
      `, [tableName]);

      if (tables[0].count > 0) {
        console.log(`\n=== ${tableName.toUpperCase()} TABLE ===`);
        
        // Get table structure
        const [columns] = await connection.execute(`DESCRIBE ${tableName}`);

        console.log('Column Name | Type | Null | Key | Default');
        console.log('------------|------|------|-----|--------');
        columns.forEach(col => {
          console.log(`${col.Field} | ${col.Type} | ${col.Null} | ${col.Key} | ${col.Default}`);
        });

      } else {
        console.log(`\n${tableName} table does not exist`);
      }
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the inspection
inspectAllTables();
