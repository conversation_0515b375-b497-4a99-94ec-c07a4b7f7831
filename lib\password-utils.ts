/**
 * Password utility functions with fallback mechanisms
 * 
 * This module provides password hashing and comparison functions with fallbacks
 * in case the bcrypt library is not available.
 */

import { createHash } from 'crypto';

// Try to import bcrypt, but don't fail if it's not available
let bcrypt: any;
try {
  bcrypt = require('bcrypt');
} catch (error) {
  console.warn('bcrypt module not available, using fallback hashing method');
  bcrypt = null;
}

/**
 * Hash a password using bcrypt or a fallback method
 * 
 * @param password The plain text password to hash
 * @returns A promise that resolves to the hashed password
 */
export async function hashPassword(password: string): Promise<string> {
  if (bcrypt) {
    // Use bcrypt if available (preferred)
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  } else {
    // Fallback to a simple hash (not secure for production!)
    return createHash('sha256').update(password).digest('hex');
  }
}

/**
 * Compare a password with a hash
 * 
 * @param password The plain text password to check
 * @param hash The hash to compare against
 * @returns A promise that resolves to true if the password matches, false otherwise
 */
export async function comparePassword(password: string, hash: string): Promise<boolean> {
  if (bcrypt) {
    // Use bcrypt if available (preferred)
    return bcrypt.compare(password, hash);
  } else {
    // Fallback to a simple hash comparison (not secure for production!)
    const hashedPassword = createHash('sha256').update(password).digest('hex');
    return hashedPassword === hash;
  }
}

/**
 * Check if secure password hashing is available
 * 
 * @returns True if bcrypt is available, false otherwise
 */
export function isSecureHashingAvailable(): boolean {
  return bcrypt !== null;
}
