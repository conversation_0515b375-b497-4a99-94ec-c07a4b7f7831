# Progressive Web App (PWA) Setup

This application has been configured as a Progressive Web App (PWA), allowing users to install it on their devices and use it offline.

## Features

### ✅ Installable
- Users can install the app on their desktop or mobile devices
- Works like a native app with its own icon and window
- Accessible from the home screen or app drawer

### ✅ Offline Support
- Service worker caches essential resources
- Previously visited pages work offline
- Graceful offline fallback page
- Network-first strategy for API calls with cache fallback

### ✅ Fast Loading
- Cached assets load instantly
- Optimized caching strategies for different resource types
- Background sync for better performance

### ✅ App-like Experience
- Standalone display mode (no browser UI)
- Custom splash screen
- Theme color integration
- Responsive design optimized for all devices

## PWA Configuration Files

### 1. Manifest (`public/manifest.json`)
Defines the app's metadata, icons, colors, and behavior:
- **Name**: Goat Farm Management System
- **Short Name**: Goat Manager
- **Theme Color**: #10b981 (emerald green)
- **Display Mode**: Standalone
- **Icons**: 192x192 and 512x512 PNG icons
- **Shortcuts**: Quick access to Dashboard, Goats, and Add Goat

### 2. Service Worker
Automatically generated by `next-pwa` with caching strategies:
- **Static Assets**: Cached for 24 hours
- **Images**: Stale-while-revalidate strategy
- **API Calls**: Network-first with 10s timeout
- **Fonts**: Long-term caching
- **Next.js Data**: Optimized caching

### 3. Icons
Located in `public/` directory:
- `icon.svg` - Vector icon (scalable)
- `icon-192x192.png` - Standard PWA icon
- `icon-512x512.png` - High-resolution PWA icon
- `apple-touch-icon.png` - iOS home screen icon

**Note**: The placeholder PNG files should be replaced with actual PNG images. You can:
1. Use an online tool to convert `icon.svg` to PNG at the required sizes
2. Use design software like Figma, Photoshop, or GIMP
3. Use command-line tools like ImageMagick: `convert icon.svg -resize 192x192 icon-192x192.png`

## Installation Instructions

### For Users

#### Desktop (Chrome, Edge, Brave)
1. Visit the application in your browser
2. Look for the install icon (⊕) in the address bar
3. Click "Install" when prompted
4. The app will open in its own window

#### Mobile (Android)
1. Open the app in Chrome or Samsung Internet
2. Tap the menu (⋮) and select "Add to Home screen"
3. Confirm the installation
4. Launch from your home screen

#### Mobile (iOS/Safari)
1. Open the app in Safari
2. Tap the Share button (□↑)
3. Scroll and tap "Add to Home Screen"
4. Confirm and launch from your home screen

## Development

### Building for Production
```bash
npm run build
npm start
```

The service worker is **disabled in development mode** to avoid caching issues during development.

### Testing PWA Features

1. **Build the production version**:
   ```bash
   npm run build
   npm start
   ```

2. **Test in Chrome DevTools**:
   - Open DevTools (F12)
   - Go to "Application" tab
   - Check "Manifest" section for manifest.json
   - Check "Service Workers" for active worker
   - Use "Lighthouse" tab to run PWA audit

3. **Test Offline Mode**:
   - Open DevTools → Network tab
   - Check "Offline" checkbox
   - Navigate through the app
   - Visit the offline page at `/offline`

4. **Test Installation**:
   - Look for install prompt in browser
   - Install the app
   - Verify it opens in standalone mode

### PWA Audit
Run a Lighthouse audit to check PWA compliance:
1. Open Chrome DevTools
2. Go to "Lighthouse" tab
3. Select "Progressive Web App" category
4. Click "Generate report"
5. Aim for a score of 90+

## Caching Strategies

### Network First (Default)
- Used for: HTML pages, API calls
- Tries network first, falls back to cache
- Timeout: 10 seconds

### Cache First
- Used for: Fonts, audio, video
- Serves from cache if available
- Updates cache in background

### Stale While Revalidate
- Used for: Images, CSS, JavaScript
- Serves cached version immediately
- Updates cache in background

## Customization

### Changing Theme Color
Edit `public/manifest.json` and `app/layout.tsx`:
```json
"theme_color": "#10b981"
```

### Updating App Name
Edit `public/manifest.json`:
```json
"name": "Your App Name",
"short_name": "Short Name"
```

### Modifying Cache Duration
Edit `next.config.mjs` in the `runtimeCaching` array:
```javascript
expiration: {
  maxEntries: 64,
  maxAgeSeconds: 24 * 60 * 60 // 24 hours
}
```

### Adding Shortcuts
Edit `public/manifest.json` in the `shortcuts` array:
```json
{
  "name": "New Shortcut",
  "url": "/path",
  "icons": [{"src": "/icon-192x192.png", "sizes": "192x192"}]
}
```

## Troubleshooting

### Service Worker Not Updating
1. Unregister old service worker in DevTools
2. Clear cache and hard reload (Ctrl+Shift+R)
3. Rebuild the application

### Icons Not Showing
1. Ensure PNG files are actual images (not placeholder text)
2. Check file sizes match manifest specifications
3. Clear browser cache

### App Not Installable
1. Must be served over HTTPS (or localhost)
2. Must have valid manifest.json
3. Must have valid service worker
4. Must have at least one icon

### Offline Page Not Working
1. Check service worker is registered
2. Verify offline page is cached
3. Test with DevTools offline mode

## Browser Support

- ✅ Chrome/Edge (Desktop & Mobile)
- ✅ Firefox (Desktop & Mobile)
- ✅ Safari (iOS 11.3+)
- ✅ Samsung Internet
- ⚠️ Safari (Desktop) - Limited PWA support

## Security Considerations

- PWA requires HTTPS in production
- Service workers have access to cached data
- Sensitive data should not be cached
- API authentication tokens are handled securely

## Performance Benefits

- **First Load**: ~2-3s (network dependent)
- **Cached Load**: <500ms
- **Offline**: Instant (cached pages)
- **Lighthouse Score**: 90+ (PWA category)

## Next Steps

1. Replace placeholder PNG icons with actual images
2. Test installation on various devices
3. Run Lighthouse audit and address any issues
4. Consider adding push notifications (future enhancement)
5. Implement background sync for offline form submissions
6. Add app screenshots to manifest for better install prompts

## Resources

- [Next PWA Documentation](https://github.com/shadowwalker/next-pwa)
- [Web.dev PWA Guide](https://web.dev/progressive-web-apps/)
- [MDN Service Worker API](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
- [PWA Builder](https://www.pwabuilder.com/)

